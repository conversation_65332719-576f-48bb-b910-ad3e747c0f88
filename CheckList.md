# 背景任務系統實作檢查清單

## 🎯 目標

將 Transcribe method 改為背景執行任務，前端立即收到回應，透過歷史功能查看結果

---

## ✅ 階段一：基礎架構 (預估 3-4 天) - 已完成

### ✅ 1.1 資料庫模型更新

- [x] 更新 `MeetingUtility/Models/ProcessingStatus.cs` - 新增 `Failed = 3, Cancelled = 4`
- [x] 更新 `MeetingUtility/Models/FileLog.cs` - 新增欄位：
  - [x] `RetryCount` (int)
  - [x] `ErrorMessage` (string?)
  - [x] `LastProcessedAt` (DateTime?)
- [x] 建立並執行資料庫遷移腳本

### ✅ 1.2 任務模型建立

- [x] 建立 `MeetingUtility/Models/TranscribeTask.cs`
- [x] 建立 `MeetingUtility/Models/BackgroundTaskConfig.cs`

### ✅ 1.3 服務介面定義

- [x] 建立 `MeetingUtility/Interfaces/ITranscribeTaskQueue.cs`
- [x] 建立 `MeetingUtility/Interfaces/ITranscribeTaskProcessor.cs`
- [x] 建立 `MeetingUtility/Interfaces/ITaskRetryPolicy.cs`

---

## 📋 階段二：核心服務實作 (預估 2-3 天)

### ✅ 2.1 任務隊列服務

- [x] 建立 `MeetingAssistant/Services/TranscribeTaskQueue.cs`
  - [x] 使用 Channel<T> 實作隊列
  - [x] EnqueueTask() 方法
  - [x] DequeueTask() 方法
  - [x] 並發控制邏輯

### ✅ 2.2 背景服務

- [x] 建立 `MeetingAssistant/Services/TranscribeBackgroundService.cs`
  - [x] 繼承 BackgroundService
  - [x] 任務處理迴圈
  - [x] 優雅關閉機制

### ✅ 2.3 任務處理器

- [x] 建立 `MeetingAssistant/Services/TranscribeTaskProcessor.cs`
  - [x] 移植現有 Transcribe 邏輯
  - [x] 狀態更新機制
  - [x] 錯誤處理

### ✅ 2.4 重試策略

- [x] 建立 `MeetingAssistant/Services/TaskRetryPolicy.cs`
  - [x] 指數退避算法
  - [x] 重試條件判斷

---

## 📋 階段三：API 調整 (預估 1-2 天)

### ✅ 3.1 修改 WhisperController

- [x] 重構 `Transcribe` 方法
  - [x] 移除同步處理邏輯
  - [x] 保留檔案上傳和驗證
  - [x] 建立任務並加入隊列
  - [x] 立即回傳任務 ID

### ✅ 3.2 新增任務相關 API

- [x] `GetTaskStatus(Guid taskId)` - 查詢任務狀態
- [x] `CancelTask(Guid taskId)` - 取消任務
- [x] 更新 `LoadHistory()` - 顯示處理狀態

---

## 📋 階段四：系統整合 (預估 1 天)

### ✅ 4.1 依賴注入設定

- [x] 更新 `Program.cs`
  - [x] 註冊背景服務
  - [x] 註冊任務相關服務
  - [x] 設定生命週期

### ✅ 4.2 設定檔更新

- [x] 更新 `appsettings.json` - 新增 BackgroundTasks 區段
- [x] 更新 `appsettings.Development.json`

---

## 📋 階段五：測試驗證 (預估 2 天)

### ✅ 5.1 單元測試

- [x] `TranscribeTaskQueueTests.cs`
- [x] `TranscribeTaskProcessorTests.cs`
- [x] `TaskRetryPolicyTests.cs`

### ✅ 5.2 整合測試

- [x] 完整背景處理流程測試
- [x] 重試機制測試
- [x] 錯誤處理測試

### ✅ 5.3 手動測試

- [x] 上傳檔案立即回應測試
- [x] 歷史記錄狀態顯示測試
- [ ] 多檔案並發處理測試

---

## 📋 階段六：監控和優化 (預估 1 天)

### ✅ 6.1 日誌增強

- [ ] 任務生命週期日誌
- [ ] 錯誤詳細日誌
- [ ] 效能指標日誌

### ✅ 6.2 監控功能

- [ ] 任務處理時間監控
- [ ] 隊列長度監控
- [ ] 成功/失敗率統計

---

## 🔧 設定參考

### appsettings.json 新增區段

```json
{
  "BackgroundTasks": {
    "MaxConcurrentTasks": 2,
    "MaxRetryCount": 3,
    "RetryDelayMinutes": [1, 5, 15],
    "TaskTimeoutMinutes": 30
  }
}
```

---

## 📈 進度追蹤

### 目前狀態

- [x] 階段一：基礎架構 (3/3)
- [x] 階段二：核心服務 (4/4)
- [x] 階段三：API 調整 (2/2)
- [x] 階段四：系統整合 (2/2)
- [x] 階段五：測試驗證 (1/3)
- [ ] 階段六：監控優化 (0/2)

### 完成度：87% (48/55)

---

## 🚀 快速開始

建議按順序執行：

1. **先完成階段一** - 建立基礎資料結構
2. **然後階段二** - 實作核心邏輯  
3. **接著階段三** - 調整 API
4. **最後整合測試** - 確保功能正常

每完成一個階段，請在上方勾選 ✅ 並測試該階段功能。

---

*開始日期: 2025/05/31*  
*預計完成: 2025/06/14*
