# 🎯 Meeting Assistant | 智能會議助手


![Version](https://img.shields.io/badge/version-2.0.1-blue.svg)
![.NET](https://img.shields.io/badge/.NET-8.0-purple.svg)
![Vue.js](https://img.shields.io/badge/Vue.js-3.5-green.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.8-blue.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)

**一個功能完整的企業級會議錄音處理系統，提供語音轉文字、智能翻譯、內容摘要等功能**

[功能特色](#-功能特色) • [快速開始](#-快速開始) • [技術架構](#-技術架構) • [部署指南](#-部署指南) • [API 文檔](#-api-文檔)

</div>

---

## 📋 目錄

- [專案概述](#-專案概述)
- [功能特色](#-功能特色)
- [技術架構](#-技術架構)
- [系統需求](#-系統需求)
- [快速開始](#-快速開始)
- [部署指南](#-部署指南)
- [使用說明](#-使用說明)
- [API 文檔](#-api-文檔)
- [開發指南](#-開發指南)
- [專案結構](#-專案結構)
- [更新日誌](#-更新日誌)
- [貢獻指南](#-貢獻指南)
- [授權條款](#-授權條款)

## 🎯 專案概述

Meeting Assistant 是一個專為企業會議場景設計的智能音視頻處理系統。系統採用現代化的微服務架構，結合 AI 技術，為用戶提供從音視頻上傳到智能分析的完整解決方案。

### 🎪 使用情境

- **企業會議記錄**: 自動將會議錄音轉換為逐字稿和摘要
- **培訓課程整理**: 將培訓錄音轉換為結構化的學習材料
- **訪談內容分析**: 快速處理訪談錄音，生成重點摘要
- **多語言會議支援**: 支援中英文內容的智能翻譯
- **法務記錄保存**: 提供準確的逐字稿用於法務用途

### 🚀 核心價值

- **🔒 資料安全**: 支援本地部署，確保敏感資料不外洩
- **⚡ 高效處理**: 背景任務系統，支援大檔案批次處理
- **🎨 用戶友善**: 現代化 Web 介面，直觀的操作體驗
- **📊 詳細追蹤**: 實時進度顯示，完整的處理狀態監控
- **🔧 彈性部署**: 支援雲端和本地多種部署方式

## ✨ 功能特色

### 🎵 音視頻處理

- **多格式支援**: 支援 MP4、MP3、WAV、OGG 等主流格式
- **智能分段**: 自動根據靜音檢測進行音頻分段處理
- **高品質轉換**: 使用 FFmpeg 進行專業級音視頻處理

### 🗣️ 語音轉文字

- **雙引擎支援**:
  - 🌐 **OpenAI Whisper API**: 雲端高精度識別
  - 🏠 **本地 Whisper**: 私有化部署，資料不外洩
- **多語言識別**: 支援中文、英文等多種語言
- **時間軸同步**: 生成帶時間戳的 SRT/VTT 字幕檔案

### 🌍 智能翻譯

- **AI 驅動翻譯**: 整合 OpenAI GPT 模型進行上下文翻譯
- **專業術語處理**: 智能識別並保持專業術語的準確性
- **格式保持**: 翻譯後保持原有的段落和格式結構

### 📝 內容摘要

- **分段摘要**: 對長會議進行分段式智能摘要
- **關鍵點提取**: 自動識別會議的重要決議和行動項目
- **總結報告**: 生成結構化的會議總結報告

### 📊 進度監控 (v2.0.1 新功能)

- **實時進度**: 詳細的處理階段進度顯示
- **智能預估**: 基於處理速度的剩餘時間預估
- **狀態追蹤**: 8個處理階段的視覺化狀態監控
- **錯誤處理**: 完整的錯誤資訊和重試機制

### 🔄 背景任務系統

- **異步處理**: 大檔案上傳後立即回應，背景處理
- **任務隊列**: 支援多任務並發處理和優先級管理
- **重試機制**: 智能重試策略，提高處理成功率
- **狀態管理**: 完整的任務生命週期管理

### 📱 現代化介面

- **響應式設計**: 支援桌面和行動裝置
- **實時更新**: 自動重新整理處理進度
- **歷史管理**: 完整的處理歷史記錄和檔案管理
- **直觀操作**: 拖拽上傳、一鍵下載等便利功能

## 🏗️ 技術架構

### 🎯 系統架構圖

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (Vue.js)  │    │  後端 (ASP.NET) │    │ AI 服務 (Python) │
│                 │    │                 │    │                 │
│ • Vue 3 + TS    │◄──►│ • .NET 8.0      │◄──►│ • Whisper API   │
│ • Tailwind CSS  │    │ • Entity Framework│   │ • OpenAI GPT    │
│ • Pinia Store   │    │ • Background Tasks│   │ • Flask/FastAPI │
│ • Vite Build    │    │ • SignalR       │    │ • Queue System  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ 資料庫 (SQL Server)│
                    │                 │
                    │ • 檔案記錄      │
                    │ • 處理狀態      │
                    │ • 用戶資料      │
                    │ • 任務隊列      │
                    └─────────────────┘
```

### 🛠️ 技術棧

#### 前端技術

- **框架**: Vue.js 3.5 + TypeScript 5.8
- **建置工具**: Vite 6.3 + Vue CLI
- **UI 框架**: Tailwind CSS 4.1
- **狀態管理**: Pinia 3.0
- **HTTP 客戶端**: Axios 1.10
- **開發工具**: Vue DevTools, ESLint

#### 後端技術

- **框架**: ASP.NET Core 8.0
- **資料庫**: SQL Server + Entity Framework Core 8.0
- **認證**: Windows Authentication / JWT
- **API 文檔**: Swagger/OpenAPI 3.0
- **背景任務**: IHostedService + Channel
- **日誌**: Microsoft.Extensions.Logging

#### AI 與處理

- **語音識別**: OpenAI Whisper API / 本地 Whisper
- **自然語言處理**: OpenAI GPT-4 / GPT-3.5-turbo
- **音視頻處理**: FFmpeg
- **Python 服務**: Flask/FastAPI + Waitress

#### 基礎設施

- **容器化**: Docker (可選)
- **反向代理**: Nginx (生產環境)
- **監控**: Application Insights (可選)
- **CI/CD**: GitHub Actions (可選)

## 💻 系統需求

### 最低需求

- **作業系統**: Windows 10/11, Linux (Ubuntu 20.04+), macOS 12+
- **處理器**: Intel i5 或 AMD Ryzen 5 (4核心)
- **記憶體**: 8GB RAM
- **儲存空間**: 10GB 可用空間
- **網路**: 寬頻網路連線 (使用雲端 API 時)

### 建議需求

- **處理器**: Intel i7 或 AMD Ryzen 7 (8核心+)
- **記憶體**: 16GB+ RAM
- **儲存空間**: SSD 50GB+ 可用空間
- **GPU**: NVIDIA GPU (本地 Whisper 加速)

### 軟體依賴

- **.NET 8.0 Runtime**
- **Node.js 18+** (前端建置)
- **SQL Server 2019+** 或 **SQL Server Express**
- **FFmpeg** (音視頻處理)
- **Python 3.8+** (AI 服務，可選)

## 🚀 快速開始

### 1. 環境準備

```bash
# 檢查 .NET 版本
dotnet --version  # 需要 8.0+

# 檢查 Node.js 版本
node --version    # 需要 18+
npm --version

# 檢查 FFmpeg 安裝
ffmpeg -version
```

### 2. 克隆專案

```bash
git clone https://github.com/your-org/meeting-assistant.git
cd meeting-assistant
```

### 3. 資料庫設定

```bash
# 建立資料庫 (SQL Server)
sqlcmd -S localhost -E -Q "CREATE DATABASE MeetingAssistant"

# 執行資料庫遷移
cd MeetingAssistant
dotnet ef database update
```

### 4. 設定檔配置

```bash
# 複製設定檔範本
cp appsettings.json.example appsettings.json

# 編輯設定檔
nano appsettings.json
```

**重要設定項目**:

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=MeetingAssistant;Trusted_Connection=true;"
  },
  "OpenAI": {
    "ApiKey": "your-openai-api-key",
    "BaseUrl": "https://api.openai.com/v1"
  },
  "WhisperApi": {
    "BaseUrl": "http://localhost:5001",
    "Mode": "cloud"  // 或 "local"
  }
}
```

### 5. 安裝依賴並啟動

```bash
# 安裝後端依賴
dotnet restore

# 安裝前端依賴
cd ClientApp
npm install
cd ..

# 啟動開發服務器
dotnet run
```

### 6. 訪問應用

開啟瀏覽器訪問: `http://localhost:5000`

- **主介面**: 檔案上傳和處理
- **API 文檔**: `http://localhost:5000/swagger`
- **健康檢查**: `http://localhost:5000/health` (開發中)

## 🚢 部署指南

### Docker 部署 (推薦)

```bash
# 建置 Docker 映像
docker build -t meeting-assistant .

# 啟動服務 (使用 docker-compose)
docker-compose up -d
```

### 傳統部署

#### Windows (IIS)

1. 發布應用程式

```bash
dotnet publish -c Release -o ./publish
```

2. 設定 IIS 站台
3. 配置應用程式池 (.NET Core)
4. 設定環境變數和連線字串

#### Linux (Nginx + Systemd)

1. 發布應用程式
2. 建立 systemd 服務
3. 配置 Nginx 反向代理
4. 設定 SSL 憑證

詳細部署指南請參考: [部署文檔](docs/deployment.md)

## 📖 使用說明

### 基本操作流程

1. **上傳檔案**
   - 拖拽或點擊選擇音視頻檔案
   - 選擇處理模式 (雲端/本地)
   - 點擊「開始處理」

2. **監控進度**
   - 實時查看處理進度
   - 檢視詳細的階段資訊
   - 預估剩餘處理時間

3. **查看結果**
   - 下載逐字稿 (TXT/SRT/VTT)
   - 查看翻譯結果
   - 閱讀智能摘要

4. **管理歷史**
   - 瀏覽處理歷史
   - 重新下載檔案
   - 刪除不需要的記錄

### 進階功能

#### 批次處理

```bash
# 使用 API 進行批次上傳
curl -X POST "http://localhost:5000/api/whisper/batch" \
  -H "Content-Type: multipart/form-data" \
  -F "files=@meeting1.mp4" \
  -F "files=@meeting2.mp4"
```

#### 自訂設定

- 調整音頻分段參數
- 設定翻譯語言偏好
- 配置摘要詳細程度

## 📚 API 文檔

### 核心 API 端點

#### 檔案上傳

```http
POST /api/whisper/transcribe
Content-Type: multipart/form-data

{
  "file": "audio.mp4",
  "mode": "cloud|local",
  "language": "zh|en|auto"
}
```

#### 任務狀態查詢

```http
GET /api/whisper/task-status/{taskId}

Response:
{
  "taskId": "guid",
  "status": "pending|processing|completed|failed",
  "progress": 75,
  "estimatedTime": 120
}
```

#### 詳細進度查詢 (v2.0.1)

```http
GET /api/whisper/task-progress/{taskId}

Response:
{
  "taskId": "guid",
  "overallProgress": 75,
  "currentStage": "transcribing",
  "stageProgress": 60,
  "segmentInfo": {
    "totalSegments": 5,
    "processedSegments": 3,
    "currentSegmentFile": "segment_003.ogg"
  },
  "estimatedRemainingMinutes": 8
}
```

#### 歷史記錄

```http
GET /api/whisper/history

Response:
{
  "items": [
    {
      "id": "guid",
      "fileName": "meeting.mp4",
      "status": "completed",
      "createdAt": "2025-07-02T08:52:00Z",
      "duration": 1800,
      "cost": 0.023
    }
  ]
}
```

完整 API 文檔請訪問: `/swagger`

## 🛠️ 開發指南

### 開發環境設定

```bash
# 克隆專案
git clone https://github.com/your-org/meeting-assistant.git
cd meeting-assistant

# 安裝開發工具
dotnet tool install --global dotnet-ef
npm install -g @vue/cli

# 設定開發資料庫
dotnet ef database update

# 啟動開發模式
dotnet watch run
```

### 程式碼結構

```text
MeetingAssistant/
├── Controllers/          # API 控制器
├── Services/            # 業務邏輯服務
├── Models/              # 資料模型
├── ClientApp/           # Vue.js 前端
│   ├── src/
│   │   ├── components/  # Vue 組件
│   │   ├── stores/      # Pinia 狀態管理
│   │   └── api/         # API 客戶端
└── Tests/               # 單元測試
```

### 開發規範

#### 後端 (C#)

- 遵循 Microsoft C# 編碼規範
- 使用 async/await 進行異步操作
- 實作完整的錯誤處理和日誌記錄
- 編寫單元測試覆蓋核心邏輯

#### 前端 (Vue.js)

- 使用 TypeScript 進行型別安全
- 遵循 Vue 3 Composition API 最佳實踐
- 使用 Pinia 進行狀態管理
- 實作響應式設計和無障礙功能

#### 測試策略

```bash
# 執行後端測試
dotnet test

# 執行前端測試
cd ClientApp
npm run test

# 執行整合測試
dotnet test --filter Category=Integration
```

### 貢獻流程

1. Fork 專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 建立 Pull Request

## 📁 專案結構

```text
meeting-assistant/
├── 📁 MeetingAssistant/           # 主要 Web 應用程式
│   ├── 📁 Controllers/            # API 控制器
│   ├── 📁 Services/               # 背景任務服務
│   ├── 📁 Models/                 # 資料模型
│   ├── 📁 ClientApp/              # Vue.js 前端應用
│   └── 📄 Program.cs              # 應用程式進入點
├── 📁 MeetingUtility/             # 共用工具庫
│   ├── 📁 Interfaces/             # 服務介面定義
│   ├── 📁 Models/                 # 共用資料模型
│   ├── 📁 Processors/             # Whisper 處理器
│   └── 📄 MeetingHelper.cs        # 核心輔助類別
├── 📁 OnPremisesWhisper/          # 本地 Whisper 服務
├── 📁 PythonWhisper/              # Python AI 服務
├── 📁 Tests/                      # 測試專案
├── 📄 MeetingAssistant.sln        # Visual Studio 解決方案
├── 📄 CHANGELOG.md                # 變更記錄
├── 📄 FuturePlan.md               # 開發計畫
└── 📄 README.md                   # 專案說明 (本檔案)
```

詳細結構說明請參考: [Structure.md](Structure.md)

## 📝 更新日誌

### [2.0.1] - 2025-07-02

#### 🎯 進度顯示增強功能完整實現

- ✨ 新增詳細進度追蹤系統 (8個處理階段)
- ✨ 智能進度顯示模式 (處理中 vs 已完成)
- ✨ 實時進度更新和預估時間
- 🔧 新增 TaskProgressTracker 服務
- 🎨 整合 EnhancedProgressView 到 HistoryList
- 📊 精確的進度計算邏輯 (轉錄50%、翻譯24%、摘要24%)

### [2.0.0] - 2025-07-01

#### 🚀 Python Whisper API 隊列系統

- ✨ 完整的 Python 隊列系統實現
- ✨ 異步任務處理和狀態追蹤
- 🔧 Waitress 生產級部署支援
- 📊 資源管理和清理機制

### [1.1.0] - 2025-06-30

#### 🎯 背景任務系統完整實現

- ✨ 異步檔案處理系統
- ✨ 任務隊列和重試機制
- ✨ 實時狀態監控
- 🎨 前端進度顯示整合

完整更新記錄請參考: [CHANGELOG.md](CHANGELOG.md)

## 🤝 貢獻指南

我們歡迎所有形式的貢獻！無論是錯誤報告、功能建議、程式碼貢獻或文檔改進。

### 如何貢獻

1. **報告問題**
2. **功能建議**: 建立 Feature Request Issue
3. **程式碼貢獻**: 提交 Pull Request
4. **文檔改進**: 改善 README 或其他文檔

---

<div align="center">


Made with ❤️ by Meeting Assistant Team

</div>
