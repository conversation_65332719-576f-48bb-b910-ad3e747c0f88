using System;
using System.Threading;
using System.Threading.Tasks;

using FakeItEasy;

using MeetingAssistant.Models;
using MeetingAssistant.Server.Services;

using MeetingUtility.Interfaces;

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace MeetingAssistant.Tests
{
    /// <summary>
    /// TranscribeBackgroundService 單元測試
    /// </summary>
    public class TranscribeBackgroundServiceTests
    {
        // 模擬的任務佇列，負責提供待處理的語音轉譯任務
        private readonly ITranscribeTaskQueue _mockTaskQueue;
        // 模擬的 DI 範圍工廠，用於產生服務範圍
        private readonly IServiceScopeFactory _mockServiceScopeFactory;
        // 模擬的任務處理器，負責實際處理語音轉譯任務
        private readonly ITranscribeTaskProcessor _mockTaskProcessor;
        // 模擬的重試策略，決定任務失敗時是否重試
        private readonly ITaskRetryPolicy _mockRetryPolicy;
        // 模擬的日誌記錄器
        private readonly ILogger<TranscribeBackgroundService> _mockLogger;
        // 模擬的背景任務組態
        private readonly IOptions<BackgroundTaskConfig> _mockConfig;
        // 被測試的背景服務實例
        private readonly TranscribeBackgroundService _service;

        /// <summary>
        /// 建構子，初始化所有 mock 物件與被測服務
        /// </summary>
        public TranscribeBackgroundServiceTests()
        {
            // 建立 Fake 任務佇列
            _mockTaskQueue = A.Fake<ITranscribeTaskQueue>();
            // 建立 Fake 任務處理器
            _mockTaskProcessor = A.Fake<ITranscribeTaskProcessor>();
            // 建立 Fake 重試策略
            _mockRetryPolicy = A.Fake<ITaskRetryPolicy>();
            // 建立 Fake Logger
            _mockLogger = A.Fake<ILogger<TranscribeBackgroundService>>();
            // 建立測試用組態
            var config = new BackgroundTaskConfig
            {
                MaxConcurrentTasks = 2, // 最大同時處理任務數
                PollingIntervalMs = 100, // 輪詢間隔（毫秒）
                TaskTimeoutMinutes = 60 // 任務逾時分鐘數
            };
            // 建立 Fake Options 並回傳測試組態
            _mockConfig = A.Fake<IOptions<BackgroundTaskConfig>>();
            A.CallTo(() => _mockConfig.Value).Returns(config);

            // 設定 ServiceScopeFactory mock，模擬 DI 範圍與服務解析
            _mockServiceScopeFactory = A.Fake<IServiceScopeFactory>();
            var mockScope = A.Fake<IServiceScope>();
            var mockServiceProvider = A.Fake<IServiceProvider>();

            // 當建立範圍時回傳 mockScope
            A.CallTo(() => _mockServiceScopeFactory.CreateScope()).Returns(mockScope);
            // 當取得 ServiceProvider 屬性時回傳 mockServiceProvider
            A.CallTo(() => mockScope.ServiceProvider).Returns(mockServiceProvider);

            // 直接設置 GetService 方法的返回值，避免使用 GetRequiredService 擴展方法
            A.CallTo(() => mockServiceProvider.GetService(typeof(ITranscribeTaskProcessor)))
                .Returns(_mockTaskProcessor);
            A.CallTo(() => mockServiceProvider.GetService(typeof(ITaskRetryPolicy)))
                .Returns(_mockRetryPolicy);

            // 建立被測服務實例
            _service = new TranscribeBackgroundService(
                _mockTaskQueue,
                _mockServiceScopeFactory,
                _mockLogger,
                _mockConfig);
        }

        /// <summary>
        /// 測試：建構子傳入正確參數時應能正確初始化
        /// </summary>
        [Fact]
        public void Constructor_WithValidArguments_ShouldInitializeSuccessfully()
        {
            // 驗證服務不為 null
            Assert.NotNull(_service);
        }

        /// <summary>
        /// 測試：建構子傳入 null 任務佇列應丟出 ArgumentNullException
        /// </summary>
        [Fact]
        public void Constructor_WithNullTaskQueue_ShouldThrowArgumentNullException()
        {
            Assert.Throws<ArgumentNullException>(() =>
                new TranscribeBackgroundService(
                    null!,
                    _mockServiceScopeFactory,
                    _mockLogger,
                    _mockConfig));
        }

        /// <summary>
        /// 測試：建構子傳入 null DI 範圍工廠應丟出 ArgumentNullException
        /// </summary>
        [Fact]
        public void Constructor_WithNullServiceScopeFactory_ShouldThrowArgumentNullException()
        {
            Assert.Throws<ArgumentNullException>(() =>
                new TranscribeBackgroundService(
                    _mockTaskQueue,
                    null!,
                    _mockLogger,
                    _mockConfig));
        }

        /// <summary>
        /// 測試：建構子傳入 null Logger 應丟出 ArgumentNullException
        /// </summary>
        [Fact]
        public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
        {
            Assert.Throws<ArgumentNullException>(() =>
                new TranscribeBackgroundService(
                    _mockTaskQueue,
                    _mockServiceScopeFactory,
                    null!,
                    _mockConfig));
        }

        /// <summary>
        /// 測試：建構子傳入 null 組態應丟出 ArgumentNullException
        /// </summary>
        [Fact]
        public void Constructor_WithNullConfig_ShouldThrowArgumentNullException()
        {
            Assert.Throws<ArgumentNullException>(() =>
                new TranscribeBackgroundService(
                    _mockTaskQueue,
                    _mockServiceScopeFactory,
                    _mockLogger,
                    null!));
        }

        /// <summary>
        /// 測試：啟動與停止服務應可正常執行且無例外
        /// </summary>
        [Fact]
        public async Task StartStopAsync_ShouldExecuteWithoutErrors()
        {
            // 模擬佇列無任務
            A.CallTo(() => _mockTaskQueue.DequeueTaskAsync(A<CancellationToken>._))
                .Returns(Task.FromResult<TranscribeTask?>(null));

            using var cts = new CancellationTokenSource();

            // 啟動服務
            var startTask = _service.StartAsync(cts.Token);
            // 等待服務啟動
            await Task.Delay(50);
            // 停止服務
            cts.Cancel();
            await startTask;
            await _service.StopAsync(CancellationToken.None);

            // 驗證有呼叫 DequeueTaskAsync
            A.CallTo(() => _mockTaskQueue.DequeueTaskAsync(A<CancellationToken>._))
                .MustHaveHappened();
        }

        /// <summary>
        /// 測試：有任務時應能正確處理並標記完成
        /// </summary>
        [Fact]
        public async Task Service_WhenTaskAvailable_ShouldProcessTask()
        {
            // 建立測試任務
            var task = new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                FileName = "test.mp3",
                Priority = 1
            };

            // 建立成功結果
            var result = TranscribeTaskResult.Success(
                task.TaskId,
                "Test transcription",
                "TestProcessor",
                "Test");

            // 第一次調用返回任務，第二次調用返回 null（沒有更多任務）
            A.CallTo(() => _mockTaskQueue.DequeueTaskAsync(A<CancellationToken>._))
                .ReturnsNextFromSequence(
                    Task.FromResult<TranscribeTask?>(task),
                    Task.FromResult<TranscribeTask?>(null));

            // 處理任務時回傳成功結果
            A.CallTo(() => _mockTaskProcessor.ProcessTaskAsync(task, A<CancellationToken>._))
                .Returns(result);

            using var cts = new CancellationTokenSource();

            // 啟動服務
            var startTask = _service.StartAsync(cts.Token);
            // 等待任務處理
            await Task.Delay(200);
            // 停止服務
            cts.Cancel();
            await startTask;
            await _service.StopAsync(CancellationToken.None);

            // 驗證流程
            A.CallTo(() => _mockTaskQueue.MarkTaskAsProcessingAsync(task.TaskId))
                .MustHaveHappened();
            A.CallTo(() => _mockTaskProcessor.ProcessTaskAsync(task, A<CancellationToken>._))
                .MustHaveHappened();
            A.CallTo(() => _mockTaskQueue.MarkTaskAsCompletedAsync(task.TaskId))
                .MustHaveHappened();
        }

        /// <summary>
        /// 測試：任務失敗且可重試時應執行重試邏輯
        /// </summary>
        [Fact]
        public async Task Service_WhenTaskFails_ShouldRetryTask()
        {
            // 建立失敗任務
            var task = new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                FileName = "test.mp3",
                Priority = 1,
                RetryCount = 0
            };

            // 建立失敗結果
            var failureResult = TranscribeTaskResult.Failure(
                task.TaskId,
                "Processing failed",
                "TestProcessor",
                "Test");

            // 第一次調用返回任務，第二次調用返回 null
            A.CallTo(() => _mockTaskQueue.DequeueTaskAsync(A<CancellationToken>._))
                .ReturnsNextFromSequence(
                    Task.FromResult<TranscribeTask?>(task),
                    Task.FromResult<TranscribeTask?>(null));

            // 處理任務時回傳失敗結果
            A.CallTo(() => _mockTaskProcessor.ProcessTaskAsync(task, A<CancellationToken>._))
                .Returns(failureResult);

            // 應該重試
            A.CallTo(() => _mockRetryPolicy.ShouldRetry(task, "Processing failed", null))
                .Returns(true);

            // 計算重試延遲
            A.CallTo(() => _mockRetryPolicy.CalculateRetryDelaySeconds(0, A<int>._))
                .Returns(1);

            using var cts = new CancellationTokenSource();

            // 啟動服務
            var startTask = _service.StartAsync(cts.Token);
            // 等待任務處理和重試邏輯
            await Task.Delay(300);
            // 停止服務
            cts.Cancel();
            await startTask;
            await _service.StopAsync(CancellationToken.None);

            // 驗證流程
            A.CallTo(() => _mockTaskProcessor.ProcessTaskAsync(task, A<CancellationToken>._))
                .MustHaveHappened();
            A.CallTo(() => _mockRetryPolicy.ShouldRetry(task, "Processing failed", null))
                .MustHaveHappened();
            A.CallTo(() => _mockRetryPolicy.CalculateRetryDelaySeconds(0, A<int>._))
                .MustHaveHappened();
        }

        /// <summary>
        /// 測試：任務失敗且不可重試時應標記為失敗
        /// </summary>
        [Fact]
        public async Task Service_WhenTaskFailsAndShouldNotRetry_ShouldMarkAsFailed()
        {
            // 建立已達最大重試次數的失敗任務
            var task = new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                FileName = "test.mp3",
                Priority = 1,
                RetryCount = 3
            };

            // 建立失敗結果
            var failureResult = TranscribeTaskResult.Failure(
                task.TaskId,
                "Max retries exceeded",
                "TestProcessor",
                "Test");

            // 第一次調用返回任務，第二次調用返回 null
            A.CallTo(() => _mockTaskQueue.DequeueTaskAsync(A<CancellationToken>._))
                .ReturnsNextFromSequence(
                    Task.FromResult<TranscribeTask?>(task),
                    Task.FromResult<TranscribeTask?>(null));

            // 處理任務時回傳失敗結果
            A.CallTo(() => _mockTaskProcessor.ProcessTaskAsync(task, A<CancellationToken>._))
                .Returns(failureResult);

            // 不可重試
            A.CallTo(() => _mockRetryPolicy.ShouldRetry(task, "Max retries exceeded", null))
                .Returns(false);

            using var cts = new CancellationTokenSource();

            // 啟動服務
            var startTask = _service.StartAsync(cts.Token);
            // 等待任務處理
            await Task.Delay(200);
            // 停止服務
            cts.Cancel();
            await startTask;
            await _service.StopAsync(CancellationToken.None);

            // 驗證流程
            A.CallTo(() => _mockTaskProcessor.ProcessTaskAsync(task, A<CancellationToken>._))
                .MustHaveHappened();
            A.CallTo(() => _mockRetryPolicy.ShouldRetry(task, "Max retries exceeded", null))
                .MustHaveHappened();
            // 由於介面中沒有 MarkTaskAsFailedAsync，我們不檢查此呼叫
        }

        /// <summary>
        /// 測試：任務處理器拋出例外時應標記任務為失敗
        /// </summary>
        [Fact]
        public async Task Service_WhenTaskProcessorThrowsException_ShouldMarkTaskAsFailed()
        {
            // 建立會拋出例外的任務
            var task = new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                FileName = "test.mp3",
                Priority = 1
            };

            // 建立例外
            var exception = new InvalidOperationException("Processor error");

            // 第一次調用返回任務，第二次調用返回 null
            A.CallTo(() => _mockTaskQueue.DequeueTaskAsync(A<CancellationToken>._))
                .ReturnsNextFromSequence(
                    Task.FromResult<TranscribeTask?>(task),
                    Task.FromResult<TranscribeTask?>(null));

            // 處理任務時拋出例外
            A.CallTo(() => _mockTaskProcessor.ProcessTaskAsync(task, A<CancellationToken>._))
                .Throws(exception);

            using var cts = new CancellationTokenSource();

            // 啟動服務
            var startTask = _service.StartAsync(cts.Token);
            // 等待任務處理
            await Task.Delay(200);
            // 停止服務
            cts.Cancel();
            await startTask;
            await _service.StopAsync(CancellationToken.None);

            // 驗證流程
            A.CallTo(() => _mockTaskProcessor.ProcessTaskAsync(task, A<CancellationToken>._))
                .MustHaveHappened();
            // 由於介面中沒有 MarkTaskAsFailedAsync，只檢查異常處理
        }

        /// <summary>
        /// 測試：收到取消請求時服務應能正常結束
        /// </summary>
        [Fact]
        public async Task Service_WhenCancellationRequested_ShouldStopGracefully()
        {
            // 模擬佇列無任務
            A.CallTo(() => _mockTaskQueue.DequeueTaskAsync(A<CancellationToken>._))
                .Returns(Task.FromResult<TranscribeTask?>(null));

            using var cts = new CancellationTokenSource();

            // 啟動服務
            var startTask = _service.StartAsync(cts.Token);
            // 立即取消
            cts.Cancel();
            // 等待服務停止
            await startTask;
            await _service.StopAsync(CancellationToken.None);

            // 驗證：若執行到這裡表示沒有異常
            Assert.True(true);
        }
    }
}
