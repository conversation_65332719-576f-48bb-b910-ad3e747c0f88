using System; // 提供基本型別與基礎類別
using System.Threading; // 提供執行緒相關功能
using System.Threading.Tasks; // 提供非同步程式設計功能
using Microsoft.Extensions.Logging; // 提供日誌記錄功能
using Microsoft.Extensions.Options; // 提供選項模式支援
using Xunit; // xUnit 單元測試框架
using FakeItEasy; // 用於建立假物件的函式庫
using MeetingAssistant.Services; // 會議助理服務命名空間
using MeetingAssistant.Models; // 會議助理資料模型命名空間

namespace MeetingAssistant.Tests
{
    /// <summary>
    /// TranscribeTaskQueue 單元測試類別
    /// 針對語音轉錄任務佇列進行功能驗證
    /// </summary>
    public class TranscribeTaskQueueTests : IDisposable
    {
        // 日誌記錄器 (使用 FakeItEasy 建立假物件)
        private readonly ILogger<TranscribeTaskQueue> _logger;
        // 背景任務組態 (IOptions 包裝)
        private readonly IOptions<BackgroundTaskConfig> _config;
        // 測試用的任務佇列實例
        private readonly TranscribeTaskQueue _taskQueue;

        /// <summary>
        /// 建構子：初始化假日誌、組態與任務佇列
        /// </summary>
        public TranscribeTaskQueueTests()
        {
            _logger = A.Fake<ILogger<TranscribeTaskQueue>>(); // 建立假日誌記錄器
            // 建立背景任務組態 (最大佇列容量、最大同時任務數、輪詢間隔、逾時、預設重試次數)
            var config = new BackgroundTaskConfig
            {
                MaxQueueCapacity = 100, // 佇列最大容量
                MaxConcurrentTasks = 2, // 最大同時處理任務數
                PollingIntervalMs = 1000, // 輪詢間隔 (毫秒)
                TaskTimeoutMinutes = 60, // 任務逾時 (分鐘)
                DefaultMaxRetryCount = 3 // 預設最大重試次數
            };
            _config = Options.Create(config); // 包裝成 IOptions
            _taskQueue = new TranscribeTaskQueue(_logger, _config); // 建立任務佇列
        }

        /// <summary>
        /// 測試：EnqueueTaskAsync 應能成功加入有效任務
        /// </summary>
        [Fact]
        public async Task EnqueueTaskAsync_Should_AddTask_When_ValidTask()
        {
            // Arrange：建立測試任務
            var task = CreateTestTask("test-file.mp3");

            // Act：將任務加入佇列
            var result = await _taskQueue.EnqueueTaskAsync(task);

            // Assert：驗證任務已加入且佇列數量正確
            Assert.True(result);
            Assert.Equal(1, _taskQueue.GetPendingTaskCount());
        }

        /// <summary>
        /// 測試：DequeueTaskAsync 應能正確取出佇列中的任務
        /// </summary>
        [Fact]
        public async Task DequeueTaskAsync_Should_ReturnTask_When_TasksInQueue()
        {
            // Arrange：先加入一個任務
            var task = CreateTestTask("test-file.mp3");
            await _taskQueue.EnqueueTaskAsync(task);

            // Act：取出任務
            var dequeuedTask = await _taskQueue.DequeueTaskAsync();

            // Assert：驗證取出的任務正確，且處理中任務數量正確
            Assert.NotNull(dequeuedTask);
            Assert.Equal(task.TaskId, dequeuedTask.TaskId);
            Assert.Equal(task.FileName, dequeuedTask.FileName);
            Assert.Equal(1, _taskQueue.GetProcessingTaskCount());
        }

        /// <summary>
        /// 測試：佇列應優先處理高優先級任務
        /// </summary>
        [Fact]
        public async Task PriorityQueue_Should_ProcessHigherPriorityFirst()
        {
            // Arrange：建立高低優先級任務
            var lowPriorityTask = CreateTestTask("low-priority.mp3", priority: 100);
            var highPriorityTask = CreateTestTask("high-priority.mp3", priority: 1);
            // 先加入低優先級，再加入高優先級
            await _taskQueue.EnqueueTaskAsync(lowPriorityTask);
            await _taskQueue.EnqueueTaskAsync(highPriorityTask);

            // Act：取出第一個任務
            var firstTask = await _taskQueue.DequeueTaskAsync();

            // Assert：應為高優先級任務
            Assert.NotNull(firstTask);
            Assert.Equal(highPriorityTask.TaskId, firstTask.TaskId);
            Assert.Equal("high-priority.mp3", firstTask.FileName);
        }

        /// <summary>
        /// 測試：TryCancelTaskAsync 應能移除佇列中的任務
        /// </summary>
        [Fact]
        public async Task TryCancelTaskAsync_Should_RemoveTaskFromQueue()
        {
            // Arrange：加入一個任務
            var task = CreateTestTask("test-file.mp3");
            await _taskQueue.EnqueueTaskAsync(task);

            // Act：嘗試取消任務
            var cancelled = await _taskQueue.TryCancelTaskAsync(task.TaskId);

            // Assert：應成功取消且佇列為空
            Assert.True(cancelled);
            Assert.Equal(0, _taskQueue.GetPendingTaskCount());
        }

        /// <summary>
        /// 測試：MarkTaskAsCompletedAsync 應能將任務自處理中移除
        /// </summary>
        [Fact]
        public async Task MarkTaskAsCompletedAsync_Should_RemoveFromProcessing()
        {
            // Arrange：加入並取出任務
            var task = CreateTestTask("test-file.mp3");
            await _taskQueue.EnqueueTaskAsync(task);
            var dequeuedTask = await _taskQueue.DequeueTaskAsync();
            Assert.NotNull(dequeuedTask);

            // Act：標記任務完成
            var completed = await _taskQueue.MarkTaskAsCompletedAsync(dequeuedTask.TaskId);

            // Assert：處理中任務數應為 0
            Assert.True(completed);
            Assert.Equal(0, _taskQueue.GetProcessingTaskCount());
        }

        /// <summary>
        /// 測試：RequeueFailedTaskAsync 應能增加重試次數並重新入佇列
        /// </summary>
        [Fact]
        public async Task RequeueFailedTaskAsync_Should_IncrementRetryCount()
        {
            // Arrange：建立已重試一次的任務
            var task = CreateTestTask("test-file.mp3");
            task.RetryCount = 1;
            task.MaxRetryCount = 3;

            // Act：重新入佇列
            var requeued = await _taskQueue.RequeueFailedTaskAsync(task);

            // Assert：重試次數應加一，且佇列數量正確
            Assert.True(requeued);
            Assert.Equal(2, task.RetryCount); // 重試次數應該增加
            Assert.Equal(1, _taskQueue.GetPendingTaskCount());
        }

        /// <summary>
        /// 測試：RequeueFailedTaskAsync 達最大重試次數時應回傳 false
        /// </summary>
        [Fact]
        public async Task RequeueFailedTaskAsync_Should_ReturnFalse_When_MaxRetriesReached()
        {
            // Arrange：建立已達最大重試次數的任務
            var task = CreateTestTask("test-file.mp3");
            task.RetryCount = 3;
            task.MaxRetryCount = 3;

            // Act：嘗試重新入佇列
            var requeued = await _taskQueue.RequeueFailedTaskAsync(task);

            // Assert：應回傳 false 且佇列數量為 0
            Assert.False(requeued);
            Assert.Equal(0, _taskQueue.GetPendingTaskCount());
        }

        /// <summary>
        /// 測試：GetStatistics 應正確回傳佇列統計資訊
        /// </summary>
        [Fact]
        public void GetStatistics_Should_ReturnCorrectCounts()
        {
            // Arrange & Act：直接取得統計資訊
            var stats = _taskQueue.GetStatistics();

            // Assert：驗證統計資訊內容
            Assert.NotNull(stats);
            Assert.Equal(0, stats.PendingTasks);
            Assert.Equal(0, stats.ProcessingTasks);
            Assert.Equal(100, stats.MaxCapacity);
            Assert.True(stats.LastUpdated <= DateTime.Now);
        }

        /// <summary>
        /// 測試：PeekTasksAsync 應能查詢任務但不移除
        /// </summary>
        [Fact]
        public async Task PeekTasksAsync_Should_ReturnTasksWithoutRemoving()
        {
            // Arrange：加入兩個不同優先級任務
            var task1 = CreateTestTask("file1.mp3", priority: 1);
            var task2 = CreateTestTask("file2.mp3", priority: 2);
            await _taskQueue.EnqueueTaskAsync(task1);
            await _taskQueue.EnqueueTaskAsync(task2);

            // Act：查詢佇列中的任務（不移除）
            var peekedTasks = await _taskQueue.PeekTasksAsync(10);

            // Assert：應查詢到兩個任務且順序正確，佇列數量不變
            Assert.Equal(2, peekedTasks.Count);
            Assert.Equal(2, _taskQueue.GetPendingTaskCount()); // 任務應該還在隊列中
            Assert.Equal(task1.TaskId, peekedTasks[0].TaskId); // 高優先級在前
        }

        /// <summary>
        /// 測試：ClearPendingTasksAsync 應能清空所有待處理任務
        /// </summary>
        [Fact]
        public async Task ClearPendingTasksAsync_Should_RemoveAllTasks()
        {
            // Arrange：加入兩個任務
            await _taskQueue.EnqueueTaskAsync(CreateTestTask("file1.mp3"));
            await _taskQueue.EnqueueTaskAsync(CreateTestTask("file2.mp3"));

            // Act：清空所有待處理任務
            var clearedCount = await _taskQueue.ClearPendingTasksAsync();

            // Assert：應清除兩個任務且佇列為空
            Assert.Equal(2, clearedCount);
            Assert.Equal(0, _taskQueue.GetPendingTaskCount());
        }

        /// <summary>
        /// 建立測試用 TranscribeTask 物件
        /// </summary>
        /// <param name="fileName">檔案名稱</param>
        /// <param name="priority">任務優先級 (數字越小優先)</param>
        /// <returns>TranscribeTask 物件</returns>
        private TranscribeTask CreateTestTask(string fileName, int priority = 100)
        {
            return new TranscribeTask
            {
                TaskId = Guid.NewGuid(), // 任務唯一識別碼
                EmpNo = "TEST001", // 測試員工編號
                MediaFilePath = $"/test/path/{fileName}", // 媒體檔案路徑
                DataDirectory = "/test/data", // 資料目錄
                FileName = fileName, // 檔案名稱
                ProcessingMode = "local", // 處理模式
                Priority = priority, // 任務優先級
                MaxRetryCount = 3 // 最大重試次數
            };
        }

        /// <summary>
        /// 釋放資源 (測試結束時自動呼叫)
        /// </summary>
        public void Dispose()
        {
            _taskQueue?.Dispose();
        }
    }
}
