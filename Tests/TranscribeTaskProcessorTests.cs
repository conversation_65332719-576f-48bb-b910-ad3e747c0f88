using System;
using System.Threading;
using System.Threading.Tasks;

using FakeItEasy;

using MeetingAssistant.Models;
using MeetingAssistant.Server.Services;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace MeetingAssistant.Tests
{
    /// <summary>
    /// TranscribeTaskProcessor 單元測試
    /// </summary>
    public class TranscribeTaskProcessorTests : IDisposable
    {
        private readonly IConfiguration _mockConfiguration;
        private readonly FileLogDbContext _dbContext;
        private readonly ILogger<TranscribeTaskProcessor> _mockLogger;

        private readonly TranscribeTaskProcessor _processor;

        public TranscribeTaskProcessorTests()
        {
            // 建立記憶體資料庫
            var options = new DbContextOptionsBuilder<FileLogDbContext>()
                //.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;


            // 建立模擬設定
            _mockConfiguration = A.Fake<IConfiguration>();
            A.<PERSON>(() => _mockConfiguration["APIKey:OPENAI_API_KEY"]).Returns("test-api-key");
            A.CallTo(() => _mockConfiguration["FileDir"]).Returns(@"C:\TEMP\");
            A.CallTo(() => _mockConfiguration["OnPremisesApiUrl"]).Returns("FakeURL");
            A.CallTo(() => _mockConfiguration["Connections:MeetingAssistant"]).Returns("Fake-connection-string");

            _dbContext = new FileLogDbContext(options, _mockConfiguration);
            _mockLogger = A.Fake<ILogger<TranscribeTaskProcessor>>();

            _processor = new TranscribeTaskProcessor(_mockConfiguration, _dbContext, _mockLogger);
        }

        [Fact]
        public async Task ProcessTaskAsync_Should_ReturnFailure_When_TaskIsNull()
        {
            // Act
            var result = await _processor.ProcessTaskAsync(null!, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("轉錄任務為 null", result.ErrorMessage);
        }

        [Fact]
        public async Task ProcessTaskAsync_Should_ReturnFailure_When_FileNotExists()
        {
            // Arrange
            var task = CreateTestTask();
            task.MediaFilePath = "non-existent-file.mp3";

            // Act
            var result = await _processor.ProcessTaskAsync(task, CancellationToken.None);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("檔案不存在", result.ErrorMessage);
        }

        [Fact]
        public void SupportedFileFormat_Should_ReturnTrue_ForValidFormats()
        {
            // Act & Assert
            Assert.True(_processor.SupportedFileFormat("mp3"));
            Assert.True(_processor.SupportedFileFormat("mp4"));
            Assert.True(_processor.SupportedFileFormat("wav"));
            Assert.True(_processor.SupportedFileFormat("m4a"));
            Assert.True(_processor.SupportedFileFormat("webm"));
        }

        [Fact]
        public void SupportedFileFormat_Should_ReturnFalse_ForInvalidFormats()
        {
            // Act & Assert
            Assert.False(_processor.SupportedFileFormat("txt"));
            Assert.False(_processor.SupportedFileFormat("doc"));
            Assert.False(_processor.SupportedFileFormat("pdf"));
            Assert.False(_processor.SupportedFileFormat("xyz"));
        }

        [Fact]
        public void IsFileSizeValid_Should_ReturnTrue_When_SizeWithinLimit()
        {
            // Arrange
            long validSize = 20 * 1024 * 1024; // 20 MB

            // Act
            var isValid = _processor.IsFileSizeValid(validSize);

            // Assert
            Assert.True(isValid);
        }

        [Fact]
        public void IsFileSizeValid_Should_ReturnFalse_When_SizeExceedsLimit()
        {
            // Arrange
            long invalidSize = 5L * 1024 * 1024 * 1024; // 14MB (超過 4G 限制)

            // Act
            var isValid = _processor.IsFileSizeValid(invalidSize);

            // Assert
            Assert.False(isValid);
        }

        [Fact]
        public void ProcessorName_Should_ReturnCorrectValue()
        {
            // Act & Assert
            Assert.Equal("TranscribeTaskProcessor", _processor.ProcessorName);
        }

        [Fact]
        public void ProcessorType_Should_ReturnLocal()
        {
            // Act & Assert
            Assert.Equal("local", _processor.ProcessorType);
        }

        [Fact]
        public async Task IsAvailableAsync_Should_ReturnTrue_When_ApiKeyConfigured()
        {
            // Act
            var isAvailable = await _processor.IsAvailableAsync();

            // Assert
            Assert.True(isAvailable);
        }

        [Fact]
        public async Task IsAvailableAsync_Should_ReturnFalse_When_ApiKeyMissing()
        {
            // Arrange
            var options = new DbContextOptionsBuilder<FileLogDbContext>().Options;
            var dbContext = new FileLogDbContext(options, _mockConfiguration);
            A.CallTo(() => _mockConfiguration["OPENAI_API_KEY"]).Returns(string.Empty);
            var processorWithoutKey = new TranscribeTaskProcessor(_mockConfiguration, dbContext, _mockLogger);

            // Act
            var isAvailable = await processorWithoutKey.IsAvailableAsync();

            // Assert
            Assert.False(isAvailable);
        }

        [Fact]
        public void GetConfiguration_Should_ReturnValidConfiguration()
        {
            // Act
            var config = _processor.GetConfiguration();

            // Assert
            Assert.NotNull(config);
        }

        [Theory]
        [InlineData(1024 * 1024, null, 1)] // 1MB 檔案
        [InlineData(5 * 1024 * 1024, 300, 2)] // 5MB 檔案，5分鐘長度
        [InlineData(10 * 1024 * 1024, 600, 3)] // 10MB 檔案，10分鐘長度
        public void EstimateProcessingTimeMinutes_Should_ReturnReasonableEstimate(long fileSizeBytes, int? durationSeconds, int expectedMinutes)
        {
            // Act
            var estimatedTime = _processor.EstimateProcessingTimeMinutes(fileSizeBytes, durationSeconds);

            // Assert
            Assert.True(estimatedTime >= expectedMinutes);
            Assert.True(estimatedTime <= expectedMinutes + 5); // 允許一些變動範圍
        }

        [Fact]
        public async Task ProcessTaskAsync_Should_UpdateFileLogStatus_When_Processing()
        {
            // Arrange
            var fileLog = new FileLog
            {
                Uid = Guid.NewGuid(),
                EmpNo = "TEST001",
                FileName = "test.mp3",
                Status = ProcessingStatus.Pending
            };

            await _dbContext.FileLogs.AddAsync(fileLog);
            await _dbContext.SaveChangesAsync();

            var task = CreateTestTask();
            task.TaskId = fileLog.Uid;

            // Act
            var result = await _processor.ProcessTaskAsync(task, CancellationToken.None);

            // Assert
            var updatedFileLog = await _dbContext.FileLogs.FindAsync(fileLog.Uid);
            Assert.NotNull(updatedFileLog);
            // 由於檔案不存在，狀態應該會被設為 Failed
            Assert.Equal(ProcessingStatus.Failed, updatedFileLog.Status);
        }

        [Fact]
        public async Task ProcessTaskAsync_Should_HandleCancellation()
        {
            // Arrange
            var task = CreateTestTask();
            using var cts = new CancellationTokenSource();
            cts.Cancel(); // 立即取消

            // Act
            var result = await _processor.ProcessTaskAsync(task, cts.Token);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("任務被取消", result.ErrorMessage);
        }

        [Fact]
        public void ProcessTaskAsync_Should_ValidateFileExtension()
        {
            // Arrange
            var task = CreateTestTask();
            task.FileName = "test.txt"; // 不支援的格式
            task.MediaFilePath = "C:\\test\\test.txt";

            // Act & Assert
            var exception = Assert.ThrowsAsync<ArgumentException>(
                () => _processor.ProcessTaskAsync(task, CancellationToken.None));
        }

        [Fact]
        public async Task ProcessTaskAsync_Should_LogProcessingSteps()
        {
            // Arrange
            var task = CreateTestTask();

            // Act
            await _processor.ProcessTaskAsync(task, CancellationToken.None);

            // Assert
            // 檢查是否有記錄日誌（由於使用 FakeItEasy，我們檢查日誌方法是否被呼叫）
            A.CallTo(_mockLogger).Where(call => call.Method.Name == "Log").MustHaveHappened();
        }

        private TranscribeTask CreateTestTask()
        {
            // 測試資料夾
            string testFolder = @"C:\TEMP";
            // 確保測試資料夾存在
            if (!System.IO.Directory.Exists(testFolder))
            {
                System.IO.Directory.CreateDirectory(testFolder);
            }
            string testMediaFileName = "test.mp3";
            // 測試媒體檔案路徑
            string mediaFilePath = System.IO.Path.Combine(testFolder, testMediaFileName);
            // 確保測試檔案存在
            if (!System.IO.File.Exists(mediaFilePath))
            {
                System.IO.File.WriteAllText(mediaFilePath, "This is a test file.");
            }

            return new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                EmpNo = "TEST001",
                MediaFilePath = mediaFilePath,
                DataDirectory = testFolder,
                FileName = testMediaFileName,
                ProcessingMode = "cloud",
                Priority = 100,
                MaxRetryCount = 3,
                RetryCount = 0,
                CreatedAt = DateTime.Now
            };
        }

        public void Dispose()
        {
            _dbContext?.Dispose();
        }
    }
}
