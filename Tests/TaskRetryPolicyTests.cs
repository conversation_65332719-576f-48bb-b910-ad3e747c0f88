// 引入系統基本類別
using System;
// 引入網路相關類別
using System.Net;
using System.Net.Http;
// 引入非同步處理相關類別
using System.Threading.Tasks;
// 引入 Microsoft 擴充日誌記錄器
using Microsoft.Extensions.Logging;
// 引入組態選項
using Microsoft.Extensions.Options;
// 引入 xUnit 測試框架
using Xunit;
// 引入 FakeItEasy 用於建立假物件（Mock）
using FakeItEasy;
// 引入會議助手服務
using MeetingAssistant.Services;
// 引入資料模型
using MeetingAssistant.Models;
// 引入介面
using MeetingUtility.Interfaces;

namespace MeetingAssistant.Tests
{
    /// <summary>
    /// TaskRetryPolicy 單元測試類別
    /// </summary>
    public class TaskRetryPolicyTests
    {
        // 日誌記錄器假物件
        private readonly ILogger<TaskRetryPolicy> _logger;
        // 背景任務組態選項
        private readonly IOptions<BackgroundTaskConfig> _config;
        // 重試策略物件
        private readonly TaskRetryPolicy _retryPolicy;

        /// <summary>
        /// 建構子：初始化重試策略測試物件
        /// </summary>
        public TaskRetryPolicyTests()
        {
            // 建立假的日誌記錄器
            _logger = A.Fake<ILogger<TaskRetryPolicy>>();
            // 建立背景任務組態
            var config = new BackgroundTaskConfig
            {
                DefaultMaxRetryCount = 3,         // 預設最大重試次數
                MaxRetryIntervalMinutes = 30,      // 最大重試間隔（分鐘）
                RetryIntervalBaseSeconds = 30      // 重試基礎間隔（秒）
            };
            // 封裝成 IOptions
            _config = Options.Create(config);
            // 初始化重試策略物件
            _retryPolicy = new TaskRetryPolicy(_logger, _config);
        }

        /// <summary>
        /// 測試：當重試次數未達最大值時，應允許重試
        /// </summary>
        [Fact]
        public void ShouldRetry_Should_ReturnTrue_When_RetryCountBelowMax()
        {
            // Arrange：建立測試任務，設定重試次數小於最大值
            var task = CreateTestTask();
            task.RetryCount = 1; // 已重試 1 次
            task.MaxRetryCount = 3; // 最大可重試 3 次
            var errorMessage = "Network timeout";

            // Act：呼叫重試判斷方法
            var shouldRetry = _retryPolicy.ShouldRetry(task, errorMessage);

            // Assert：應允許重試
            Assert.True(shouldRetry);
        }

        /// <summary>
        /// 測試：當重試次數等於最大值時，不應再重試
        /// </summary>
        [Fact]
        public void ShouldRetry_Should_ReturnFalse_When_RetryCountExceedsMax()
        {
            // Arrange：建立測試任務，設定重試次數等於最大值
            var task = CreateTestTask();
            task.RetryCount = 3;
            task.MaxRetryCount = 3;
            var errorMessage = "Network timeout";

            // Act：呼叫重試判斷方法
            var shouldRetry = _retryPolicy.ShouldRetry(task, errorMessage);

            // Assert：不應再重試
            Assert.False(shouldRetry);
        }

        /// <summary>
        /// 測試：遇到不可重試的例外時，不應重試
        /// </summary>
        [Fact]
        public void ShouldRetry_Should_ReturnFalse_When_NonRetryableException()
        {
            // Arrange：建立不可重試例外
            var task = CreateTestTask();
            task.RetryCount = 1;
            task.MaxRetryCount = 3;
            var errorMessage = "File not found";
            var exception = new ArgumentException("Invalid file format");

            // Act：呼叫重試判斷方法
            var shouldRetry = _retryPolicy.ShouldRetry(task, errorMessage, exception);

            // Assert：不應重試
            Assert.False(shouldRetry);
        }

        /// <summary>
        /// 測試：遇到可重試的例外時，應允許重試
        /// </summary>
        [Fact]
        public void ShouldRetry_Should_ReturnTrue_When_RetryableException()
        {
            // Arrange：建立可重試例外
            var task = CreateTestTask();
            task.RetryCount = 1;
            task.MaxRetryCount = 3;
            var errorMessage = "Connection timeout";
            var exception = new HttpRequestException("Request timeout");

            // Act：呼叫重試判斷方法
            var shouldRetry = _retryPolicy.ShouldRetry(task, errorMessage, exception);

            // Assert：應允許重試
            Assert.True(shouldRetry);
        }

        /// <summary>
        /// 測試：重試延遲應採用指數退避演算法
        /// </summary>
        [Fact]
        public void CalculateRetryDelaySeconds_Should_UseExponentialBackoff()
        {
            // Arrange：設定重試次數與基礎延遲
            int retryCount = 2;
            int baseDelaySeconds = 30;

            // Act：計算延遲秒數
            var delaySeconds = _retryPolicy.CalculateRetryDelaySeconds(retryCount, baseDelaySeconds);

            // Assert：延遲應大於等於基礎值且不超過最大值
            Assert.True(delaySeconds >= baseDelaySeconds); // 至少是基礎延遲
            Assert.True(delaySeconds <= 1800); // 不超過最大值 (30分鐘)
        }

        /// <summary>
        /// 測試：網路錯誤應視為暫時性錯誤（可重試）
        /// </summary>
        [Fact]
        public void IsTransientError_Should_ReturnTrue_ForNetworkErrors()
        {
            // Arrange：建立網路相關例外
            var httpException = new HttpRequestException("Connection timeout");
            var timeoutException = new TimeoutException("Operation timeout");

            // Act & Assert：應視為暫時性錯誤
            Assert.True(_retryPolicy.IsTransientError("Network timeout", httpException));
            Assert.True(_retryPolicy.IsTransientError("Request timeout", timeoutException));
            Assert.True(_retryPolicy.IsTransientError("503 Server Unavailable"));
            Assert.True(_retryPolicy.IsTransientError("Rate limit exceeded"));
        }

        /// <summary>
        /// 測試：永久性錯誤不應允許重試
        /// </summary>
        [Fact]
        public void IsTransientError_Should_ReturnFalse_ForPermanentErrors()
        {
            // Arrange：建立永久性錯誤例外
            var argumentException = new ArgumentException("Invalid parameter");
            var formatException = new FormatException("Invalid format");

            // Act & Assert：不應視為暫時性錯誤
            Assert.False(_retryPolicy.IsTransientError("Invalid file format", argumentException));
            Assert.False(_retryPolicy.IsTransientError("401 Unauthorized"));
            Assert.False(_retryPolicy.IsTransientError("File not found", formatException));
            Assert.False(_retryPolicy.IsTransientError("400 Bad Request"));
        }

        /// <summary>
        /// 測試：重試時應遞增 RetryCount
        /// </summary>
        [Fact]
        public void UpdateTaskForRetry_Should_IncrementRetryCount()
        {
            // Arrange：建立測試任務
            var task = CreateTestTask();
            task.RetryCount = 1;
            var errorMessage = "Connection timeout";

            // Act：呼叫更新重試任務方法
            var updatedTask = _retryPolicy.UpdateTaskForRetry(task, errorMessage);

            // Assert：RetryCount 應加一，TaskId 不變
            Assert.Equal(2, updatedTask.RetryCount);
            Assert.Equal(task.TaskId, updatedTask.TaskId);
        }

        /// <summary>
        /// 測試：超過最大重試次數時應回傳 true
        /// </summary>
        [Fact]
        public void HasExceededMaxRetries_Should_ReturnTrue_When_ExceedsMax()
        {
            // Arrange：建立已達最大重試次數的任務
            var task = CreateTestTask();
            task.RetryCount = 3;
            task.MaxRetryCount = 3;

            // Act：判斷是否超過最大重試次數
            var exceeded = _retryPolicy.HasExceededMaxRetries(task);

            // Assert：應為 true
            Assert.True(exceeded);
        }

        /// <summary>
        /// 測試：未超過最大重試次數時應回傳 false
        /// </summary>
        [Fact]
        public void HasExceededMaxRetries_Should_ReturnFalse_When_BelowMax()
        {
            // Arrange：建立未達最大重試次數的任務
            var task = CreateTestTask();
            task.RetryCount = 2;
            task.MaxRetryCount = 3;

            // Act：判斷是否超過最大重試次數
            var exceeded = _retryPolicy.HasExceededMaxRetries(task);

            // Assert：應為 false
            Assert.False(exceeded);
        }

        /// <summary>
        /// 測試：取得重試策略組態內容
        /// </summary>
        [Fact]
        public void GetConfiguration_Should_ReturnValidConfiguration()
        {
            // Act：取得組態
            var configuration = _retryPolicy.GetConfiguration();

            // Assert：組態內容應正確
            Assert.NotNull(configuration);
            Assert.NotEmpty(configuration.PolicyName);
            Assert.True(configuration.DefaultMaxRetryCount > 0);
            Assert.True(configuration.BaseRetryDelaySeconds > 0);
            Assert.True(configuration.MaxRetryDelaySeconds > configuration.BaseRetryDelaySeconds);
        }

        /// <summary>
        /// 測試：PolicyName 屬性應有值
        /// </summary>
        [Fact]
        public void PolicyName_Should_ReturnCorrectValue()
        {
            // Act & Assert：PolicyName 不應為空
            Assert.NotEmpty(_retryPolicy.PolicyName);
        }

        /// <summary>
        /// 測試：DefaultMaxRetryCount 屬性應回傳組態值
        /// </summary>
        [Fact]
        public void DefaultMaxRetryCount_Should_ReturnConfigValue()
        {
            // Act & Assert：應等於組態設定值
            Assert.Equal(3, _retryPolicy.DefaultMaxRetryCount);
        }

        /// <summary>
        /// 測試：MaxRetryDelaySeconds 屬性應回傳組態值
        /// </summary>
        [Fact]
        public void MaxRetryDelaySeconds_Should_ReturnConfigValue()
        {
            // Act & Assert：應等於 30 分鐘（1800 秒）
            Assert.Equal(1800, _retryPolicy.MaxRetryDelaySeconds); // 30 分鐘 = 1800 秒
        }

        /// <summary>
        /// 測試：不同重試次數下延遲計算應正確
        /// </summary>
        /// <param name="retryCount">重試次數</param>
        /// <param name="baseDelay">基礎延遲秒數</param>
        [Theory]
        [InlineData(0, 30)] // 第一次重試基礎延遲
        [InlineData(1, 30)] // 第二次重試
        [InlineData(2, 30)] // 第三次重試
        public void CalculateRetryDelaySeconds_Should_HandleDifferentRetryCounts(int retryCount, int baseDelay)
        {
            // Act：計算延遲秒數
            var delaySeconds = _retryPolicy.CalculateRetryDelaySeconds(retryCount, baseDelay);

            // Assert：延遲應大於等於基礎值且不超過最大值
            Assert.True(delaySeconds >= baseDelay);
            Assert.True(delaySeconds <= 1800); // 不超過最大值
        }

        /// <summary>
        /// 測試：重試次數極大時延遲不應超過最大值
        /// </summary>
        [Fact]
        public void CalculateRetryDelaySeconds_Should_RespectMaximumDelay()
        {
            // Arrange：設定極大重試次數
            int retryCount = 10; // 很大的重試次數
            int baseDelaySeconds = 30;

            // Act：計算延遲秒數
            var delaySeconds = _retryPolicy.CalculateRetryDelaySeconds(retryCount, baseDelaySeconds);

            // Assert：延遲不應超過 30 分鐘
            Assert.True(delaySeconds <= 1800); // 不應該超過 30 分鐘
        }

        /// <summary>
        /// 建立測試用 TranscribeTask 物件
        /// </summary>
        /// <returns>TranscribeTask 物件</returns>
        private TranscribeTask CreateTestTask()
        {
            return new TranscribeTask
            {
                TaskId = Guid.NewGuid(),                // 任務唯一識別碼
                EmpNo = "TEST001",                      // 員工編號
                MediaFilePath = "/test/path/test-file.mp3", // 媒體檔案路徑
                DataDirectory = "/test/data",           // 資料目錄
                FileName = "test-file.mp3",              // 檔案名稱
                ProcessingMode = "local",                // 處理模式
                Priority = 100,                          // 優先權
                MaxRetryCount = 3,                       // 最大重試次數
                RetryCount = 0,                          // 當前重試次數
                CreatedAt = DateTime.Now                 // 建立時間
            };
        }
    }
}
