// 引入 FakeItEasy 用於建立假物件（Mock）
using FakeItEasy;
// 引入 Microsoft.Extensions.Configuration 用於讀取組態檔
using Microsoft.Extensions.Configuration;
// 引入 Newtonsoft.Json.Linq 用於處理 JSON 物件
using Newtonsoft.Json.Linq;
// 引入泛型集合
using System.Collections.Generic;
// 引入程式碼覆蓋率排除屬性
using System.Diagnostics.CodeAnalysis;
// 引入 HttpClient 相關類別
using System.Net.Http;
// 引入非同步處理相關類別
using System.Threading.Tasks;
// 引入會議輔助工具類
using MeetingUtility;

namespace MeetingAssistant.Tests
{
    // 排除此測試類別不納入程式碼覆蓋率統計
    [ExcludeFromCodeCoverage]
    public class MeetingHelperTests
    {
        // 會議輔助工具實例
        private readonly MeetingHelper _meetingHelper;

        // 建構子：初始化 MeetingHelper 實例，讀取 API 金鑰
        public MeetingHelperTests()
        {
            // 建立組態物件，讀取 appsettings.json
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true).Build();
            // 取得 OpenAI API 金鑰
            string? apiKey = configuration["APIKey:OPENAI_API_KEY"];
            if (apiKey != null)
            {
                // 若有金鑰則用金鑰初始化
                _meetingHelper = new MeetingHelper(apiKey);
            }
            else
            {
                // 若無金鑰則用預設建構子
                _meetingHelper = new MeetingHelper();
            }
        }

        /// <summary>
        /// 測試翻譯功能，應回傳翻譯後的文字
        /// </summary>
        [Fact]
        public async Task TranslateAsync_ShouldReturnTranslatedText()
        {
            // Arrange：建立測試用的 JSON 物件清單
            var responses = new List<JObject>
            {
                new JObject { ["text"] = "Hello, this is a test." }, // 第一段測試文字
                new JObject { ["text"] = "This is another test." }    // 第二段測試文字
            };

            // Act：呼叫翻譯方法
            var (result, cost) = await _meetingHelper.OpenAiTranslateAsync(responses);

            // Assert：檢查回傳結果不為空
            Assert.False(string.IsNullOrEmpty(result));
        }

        /// <summary>
        /// 測試摘要功能，應回傳會議摘要
        /// </summary>
        [Fact]
        public async Task SummarizeAsync_ShouldReturnSummary()
        {
            // Arrange：建立測試用的 JSON 物件清單
            var responses = new List<JObject>
            {
                new JObject { ["text"] = "This is the first part of the meeting." }, // 會議第一部分
                new JObject { ["text"] = "This is the second part of the meeting." }  // 會議第二部分
            };

            // Act：呼叫摘要方法
            var (result, _) = await _meetingHelper.OpenAiSummarizeAsync(responses);

            // Assert：檢查回傳結果不為空
            Assert.False(string.IsNullOrEmpty(result));
        }

        /// <summary>
        /// 測試單一字串摘要功能，應回傳會議摘要
        /// </summary>
        [Fact]
        public async Task SummarizeAsync_SingleString_ShouldReturnSummary()
        {
            // Arrange：測試用會議內容
            var meetingText = "This is a meeting note that needs to be summarized. It contains key points and action items.";

            // Act：呼叫摘要方法
            var (result, _) = await _meetingHelper.OpenAiSummarizeAsync(meetingText);

            // Assert：檢查回傳結果不為空
            Assert.False(string.IsNullOrEmpty(result));
            // 若使用真實 API 金鑰，結果應包含有意義的摘要文字
            // 此為整合測試，依賴實際 OpenAI API 呼叫
        }

        /// <summary>
        /// 測試自訂 HttpClient 摘要功能（目前僅驗證實作可用）
        /// </summary>
        [Fact]
        public async Task SummarizeAsync_WithHttpClient_ReturnsExpectedResult()
        {
            // 此測試需自訂 HttpClient 以模擬 API 回應
            // 建立假的 HttpMessageHandler
            var handler = A.Fake<HttpMessageHandler>();
            // 建立 HttpClient 實例
            var httpClient = new HttpClient(handler);

            // 若 MeetingHelper 支援注入 HttpClient，可於此傳入
            // 目前僅驗證現有實作可用
            var testText = "Meeting to discuss project progress. Team reported 70% completion. Next steps: finalize documentation by Friday.";
            var (result, cost) = await _meetingHelper.OpenAiSummarizeAsync(testText);

            // Assert：檢查回傳結果不為空
            Assert.False(string.IsNullOrEmpty(result));
            // 若有 Mock，可驗證回傳內容是否符合預期
        }

        /// <summary>
        /// 測試摘要錯誤處理，應回傳預設訊息
        /// </summary>
        [Fact]
        public async Task SummarizeAsync_ErrorHandling_ReturnsDefaultMessage()
        {
            // Arrange：建立無效輸入
            string invalidInput = string.Empty;

            // Act：呼叫摘要方法
            var (result, cost) = await _meetingHelper.OpenAiSummarizeAsync(invalidInput);

            // Assert：檢查成本為 0 且回傳預設訊息
            Assert.Equal(0.0, cost);
            Assert.Equal("無法取得會議摘要", result);
        }

        /// <summary>
        /// 測試中文斷句功能，應正確分段
        /// </summary>
        /// <param name="input">輸入的中文句子</param>
        /// <param name="expected">預期分段結果</param>
        [Theory]
        [InlineData("這是一個測試。這是另一個測試。", "這是一個測試。\r\n這是另一個測試。\r\n")]
        [InlineData("你好！你在做什麼？", "你好！\r\n你在做什麼？\r\n")]
        [InlineData("", "")]
        public void SegmentChineseSentences_ShouldSegmentCorrectly(string input, string expected)
        {
            // Act：呼叫斷句方法
            var result = MeetingHelper.SegmentChineseSentences(input);

            // Assert：檢查分段結果是否正確
            Assert.Equal(expected, result);
        }        
        
        /// <summary>
        /// 測試 OnPremisesSummarizeAsync 方法處理空字串或空白字串的情況
        /// 應該回傳預設的"無法得出會議摘要"訊息
        /// </summary>
        //[Theory]
        //[InlineData("")]
        //[InlineData("   ")]
        //public async Task OnPremisesSummarizeAsync_WithEmptyOrWhitespaceInput_ShouldReturnDefaultMessage(string input)
        //{
        //    // Act
        //    string result = await _meetingHelper.OnPremisesSummarizeAsync(input);

        //    // Assert
        //    Assert.Equal("無法得出會議摘要", result);
        //}        
        
        /// <summary>
        /// 測試 OnPremisesSummarizeAsync 方法處理 null 輸入的情況
        /// 應該回傳預設的"無法得出會議摘要"訊息
        /// </summary>
        //[Fact]
        //public async Task OnPremisesSummarizeAsync_WithNullInput_ShouldReturnDefaultMessage()
        //{
        //    // Act
        //    string? nullInput = null;
        //    string result = await _meetingHelper.OnPremisesSummarizeAsync(nullInput!);

        //    // Assert
        //    Assert.Equal("無法得出會議摘要", result);
        //}

        /// <summary>
        /// 測試 OnPremisesSummarizeAsync 方法處理有效會議記錄的情況
        /// 注意：此測試需要 Ollama 服務運行在指定的 URL
        /// </summary>
        //[Fact]
        //public async Task OnPremisesSummarizeAsync_WithValidMeetingNotes_ShouldReturnSummary()
        //{
        //    // Arrange
        //    string meetingNotes = @"會議主題：產品開發進度討論
        //                           參與人員：張經理、李工程師、王設計師
        //                           主要討論事項：
        //                           1. 新功能開發進度回報
        //                           2. 使用者介面設計審查
        //                           3. 測試計畫安排
        //                           決議事項：
        //                           - 李工程師將在下週完成後端 API 開發
        //                           - 王設計師需要修改首頁設計稿
        //                           - 下次會議時間訂於下週三下午 2 點";

        //    try
        //    {
        //        // Act
        //        string result = await _meetingHelper.OnPremisesSummarizeAsync(meetingNotes);

        //        // Assert
        //        Assert.NotNull(result);
        //        Assert.NotEmpty(result);
        //        // 確認不是預設的錯誤訊息
        //        Assert.NotEqual("無法得出會議摘要", result);
        //    }
        //    catch (HttpRequestException)
        //    {
        //        // 如果 Ollama 服務不可用，跳過此測試
        //        Assert.True(true, "Ollama 服務不可用，跳過此測試");
        //    }
        //    catch (TaskCanceledException)
        //    {
        //        // 如果請求超時，跳過此測試
        //        Assert.True(true, "請求超時，跳過此測試");
        //    }
        //}

        /// <summary>
        /// 測試 OnPremisesSummarizeAsync 方法處理簡短文字的情況
        /// 應該能處理簡短的會議記錄
        /// </summary>
        //[Fact]
        //public async Task OnPremisesSummarizeAsync_WithShortText_ShouldHandleGracefully()
        //{
        //    // Arrange
        //    string shortText = "簡短會議記錄";

        //    try
        //    {
        //        // Act
        //        string result = await _meetingHelper.OnPremisesSummarizeAsync(shortText);

        //        // Assert
        //        Assert.NotNull(result);
        //        Assert.NotEmpty(result);
        //        // 可能回傳無法總結訊息或實際摘要
        //        Assert.True(result == "無法得出會議摘要" || result.Length > 0);
        //    }
        //    catch (HttpRequestException)
        //    {
        //        // 如果 Ollama 服務不可用，跳過此測試
        //        Assert.True(true, "Ollama 服務不可用，跳過此測試");
        //    }
        //    catch (TaskCanceledException)
        //    {
        //        // 如果請求超時，跳過此測試
        //        Assert.True(true, "請求超時，跳過此測試");
        //    }
        //}

        /// <summary>
        /// 測試 OnPremisesSummarizeAsync 方法處理長文本的情況
        /// 應該能處理較長的會議記錄並產生適當的摘要
        /// </summary>
        //[Fact]
        //public async Task OnPremisesSummarizeAsync_WithLongText_ShouldReturnAppropriteSummary()
        //{
        //    // Arrange
        //    string longMeetingNotes = @"
        //        會議時間：2025年6月9日上午10:00-12:00
        //        會議地點：會議室A
        //        主持人：陳總經理
        //        參與人員：各部門主管15人
                
        //        會議議程：
        //        1. 第二季業績檢討
        //        2. 新產品上市策略討論
        //        3. 人力資源配置調整
        //        4. 預算分配檢討
        //        5. 下季度目標設定
                
        //        主要討論內容：
        //        陳總經理首先報告第二季整體業績，營收較去年同期成長15%，獲利率維持在12%。
        //        銷售部經理報告新產品預計下月上市，目標客群為25-35歲年輕族群。
        //        行銷部經理提出數位行銷策略，預算增加20%投入社群媒體廣告。
        //        人資部經理報告人力需求，預計招募10名新進員工。
                
        //        決議事項：
        //        1. 批准新產品上市計畫，預算500萬元
        //        2. 同意增加數位行銷預算100萬元
        //        3. 核准人力招募計畫
        //        4. 下季度營收目標設定為成長18%
        //        5. 各部門主管於下週提交詳細執行計畫
                
        //        下次會議：2025年7月15日上午10:00";

        //    try
        //    {
        //        // Act
        //        string result = await _meetingHelper.OnPremisesSummarizeAsync(longMeetingNotes);

        //        // Assert
        //        Assert.NotNull(result);
        //        Assert.NotEmpty(result);
        //        // 確認不是預設的錯誤訊息
        //        Assert.NotEqual("無法得出會議摘要", result);
        //        // 摘要應該比原文短
        //        Assert.True(result.Length < longMeetingNotes.Length);
        //    }
        //    catch (HttpRequestException)
        //    {
        //        // 如果 Ollama 服務不可用，跳過此測試
        //        Assert.True(true, "Ollama 服務不可用，跳過此測試");
        //    }
        //    catch (TaskCanceledException)
        //    {
        //        // 如果請求超時，跳過此測試
        //        Assert.True(true, "請求超時，跳過此測試");
        //    }
        //}

        /// <summary>
        /// 測試 OnPremisesSummarizeAsync 方法處理英文會議記錄的情況
        /// 應該能將英文內容轉換為繁體中文摘要
        /// </summary>
        //[Fact]
        //public async Task OnPremisesSummarizeAsync_WithEnglishInput_ShouldReturnChineseSummary()
        //{
        //    // Arrange
        //    string englishMeetingNotes = @"
        //        Meeting Topic: Product Development Review
        //        Date: June 9, 2025
        //        Attendees: John Smith (Manager), Mary Johnson (Developer), David Lee (Designer)
                
        //        Discussion Points:
        //        1. Current development progress review
        //        2. UI/UX design feedback
        //        3. Testing strategy planning
        //        4. Budget allocation review
                
        //        Key Decisions:
        //        - Mary will complete the backend API development by next week
        //        - David needs to revise the homepage design mockup
        //        - Next meeting scheduled for next Wednesday at 2 PM
        //        - Budget approved for additional testing resources";

        //    try
        //    {
        //        // Act
        //        string result = await _meetingHelper.OnPremisesSummarizeAsync(englishMeetingNotes);

        //        // Assert
        //        Assert.NotNull(result);
        //        Assert.NotEmpty(result);
        //        // 確認不是預設的錯誤訊息
        //        Assert.NotEqual("無法得出會議摘要", result);
        //        // 結果應該包含中文字符
        //        Assert.Matches(@"[\u4e00-\u9fff]", result);
        //    }
        //    catch (HttpRequestException)
        //    {
        //        // 如果 Ollama 服務不可用，跳過此測試
        //        Assert.True(true, "Ollama 服務不可用，跳過此測試");
        //    }
        //    catch (TaskCanceledException)
        //    {
        //        // 如果請求超時，跳過此測試
        //        Assert.True(true, "請求超時，跳過此測試");
        //    }
        //}
    }
}
