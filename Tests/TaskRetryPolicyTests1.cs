using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Xunit;
using FakeItEasy;
using MeetingAssistant.Models;
using MeetingAssistant.Services;
using System.Net.Http;

namespace MeetingAssistant.Tests
{
    /// <summary>
    /// TaskRetryPolicy 單元測試
    /// </summary>
    public class TaskRetryPolicyTests1
    {
        // 模擬的日誌記錄器
        private readonly ILogger<TaskRetryPolicy> _fakeLogger;
        // 模擬的組態選項
        private readonly IOptions<BackgroundTaskConfig> _fakeOptions;
        // 測試用的 TaskRetryPolicy 實例
        private readonly TaskRetryPolicy _retryPolicy;
        // 測試用的組態物件
        private readonly BackgroundTaskConfig _config;

        /// <summary>
        /// 建構子，初始化 Fake 物件與 TaskRetryPolicy 實例
        /// </summary>
        public TaskRetryPolicyTests1()
        {
            // 建立 Fake Logger
            _fakeLogger = A.Fake<ILogger<TaskRetryPolicy>>();
            // 建立測試用組態
            _config = new BackgroundTaskConfig
            {
                DefaultMaxRetryCount = 3, // 預設最大重試次數
                RetryIntervalBaseSeconds = 30, // 重試間隔基底秒數
                MaxRetryIntervalMinutes = 30 // 最大重試間隔分鐘
            };
            // 建立 Fake Options 並回傳測試組態
            _fakeOptions = A.Fake<IOptions<BackgroundTaskConfig>>();
            A.CallTo(() => _fakeOptions.Value).Returns(_config);
            // 建立 TaskRetryPolicy 實例
            _retryPolicy = new TaskRetryPolicy(_fakeLogger, _fakeOptions);
        }

        #region ShouldRetry 測試

        /// <summary>
        /// 測試：當任務為 null 時，ShouldRetry 應回傳 false
        /// </summary>
        [Fact]
        public void ShouldRetry_TaskIsNull_ReturnsFalse()
        {
            // 執行
            var result = _retryPolicy.ShouldRetry(null!, "test error");

            // 驗證
            Assert.False(result);
        }

        /// <summary>
        /// 測試：當重試次數已達最大值時，ShouldRetry 應回傳 false
        /// </summary>
        [Fact]
        public void ShouldRetry_ExceededMaxRetries_ReturnsFalse()
        {
            // 建立已達最大重試次數的任務
            var task = new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                RetryCount = 3, // 已重試 3 次
                MaxRetryCount = 3 // 最大重試 3 次
            };

            // 執行
            var result = _retryPolicy.ShouldRetry(task, "timeout error");

            // 驗證
            Assert.False(result);
        }

        /// <summary>
        /// 測試：遇到暫時性錯誤時，ShouldRetry 應回傳 true
        /// </summary>
        [Fact]
        public void ShouldRetry_TransientError_ReturnsTrue()
        {
            // 建立可重試的任務
            var task = new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                RetryCount = 1,
                MaxRetryCount = 3
            };

            // 執行
            var result = _retryPolicy.ShouldRetry(task, "connection timeout");

            // 驗證
            Assert.True(result);
        }

        /// <summary>
        /// 測試：遇到永久性錯誤時，ShouldRetry 應回傳 false
        /// </summary>
        [Fact]
        public void ShouldRetry_PermanentError_ReturnsFalse()
        {
            // 建立可重試的任務
            var task = new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                RetryCount = 1,
                MaxRetryCount = 3
            };

            // 執行
            var result = _retryPolicy.ShouldRetry(task, "invalid file format");

            // 驗證
            Assert.False(result);
        }

        #endregion

        #region CalculateRetryDelaySeconds 測試

        /// <summary>
        /// 測試：第一次重試時，應回傳基底延遲（含隨機抖動）
        /// </summary>
        [Fact]
        public void CalculateRetryDelaySeconds_FirstRetry_ReturnsBaseDelay()
        {
            // 執行
            var result = _retryPolicy.CalculateRetryDelaySeconds(0, 30);

            // 驗證：考慮 10% 的隨機抖動
            Assert.True(result >= 27 && result <= 33);
        }

        /// <summary>
        /// 測試：指數退避機制下，重試延遲應逐次增加
        /// </summary>
        [Fact]
        public void CalculateRetryDelaySeconds_ExponentialBackoff_IncreasesDelay()
        {
            // 執行三次不同重試次數的延遲
            var delay1 = _retryPolicy.CalculateRetryDelaySeconds(0, 30);
            var delay2 = _retryPolicy.CalculateRetryDelaySeconds(1, 30);
            var delay3 = _retryPolicy.CalculateRetryDelaySeconds(2, 30);

            // 驗證：有隨機抖動，檢查大致趨勢
            Assert.True(delay2 > delay1 * 1.5); // 第二次應大於第一次的 1.5 倍
            Assert.True(delay3 > delay2 * 1.5); // 第三次應大於第二次的 1.5 倍
        }

        /// <summary>
        /// 測試：負數重試次數應視為 0
        /// </summary>
        [Fact]
        public void CalculateRetryDelaySeconds_NegativeRetryCount_TreatsAsZero()
        {
            // 執行
            var result = _retryPolicy.CalculateRetryDelaySeconds(-1, 30);

            // 驗證：與 retryCount = 0 相同
            Assert.True(result >= 27 && result <= 33);
        }

        /// <summary>
        /// 測試：超過最大延遲時，應限制在最大值以內
        /// </summary>
        [Fact]
        public void CalculateRetryDelaySeconds_ExceedsMaxDelay_ClampsToMax()
        {
            // 執行
            var result = _retryPolicy.CalculateRetryDelaySeconds(10, 30); // 應超過最大值

            // 驗證
            var maxDelaySeconds = _config.MaxRetryIntervalMinutes * 60;
            Assert.True(result <= maxDelaySeconds);
        }

        #endregion

        #region IsTransientError 測試

        /// <summary>
        /// 測試：暫時性錯誤關鍵字應回傳 true
        /// </summary>
        [Theory]
        [InlineData("timeout", true)]
        [InlineData("connection", true)]
        [InlineData("network", true)]
        [InlineData("rate limit", true)]
        [InlineData("503", true)]
        [InlineData("502", true)]
        [InlineData("429", true)]
        [InlineData("server error", true)]
        public void IsTransientError_TransientKeywords_ReturnsTrue(string errorMessage, bool expected)
        {
            // 執行
            var result = _retryPolicy.IsTransientError(errorMessage);

            // 驗證
            Assert.Equal(expected, result);
        }

        /// <summary>
        /// 測試：永久性錯誤關鍵字應回傳 false
        /// </summary>
        [Theory]
        [InlineData("invalid file", false)]
        [InlineData("unauthorized", false)]
        [InlineData("authentication", false)]
        [InlineData("401", false)]
        [InlineData("403", false)]
        [InlineData("bad request", false)]
        public void IsTransientError_PermanentKeywords_ReturnsFalse(string errorMessage, bool expected)
        {
            // 執行
            var result = _retryPolicy.IsTransientError(errorMessage);

            // 驗證
            Assert.Equal(expected, result);
        }

        /// <summary>
        /// 測試：空字串或 null 應回傳 false
        /// </summary>
        [Fact]
        public void IsTransientError_EmptyMessage_ReturnsFalse()
        {
            // 執行
            var result1 = _retryPolicy.IsTransientError("");
            var result2 = _retryPolicy.IsTransientError(null!);

            // 驗證
            Assert.False(result1);
            Assert.False(result2);
        }

        /// <summary>
        /// 測試：HttpRequestException 例外訊息包含暫時性錯誤時應回傳 true
        /// </summary>
        [Fact]
        public void IsTransientError_HttpRequestException_ChecksMessage()
        {
            // 建立 HttpRequestException
            var httpEx = new HttpRequestException("Request timeout occurred");

            // 執行
            var result = _retryPolicy.IsTransientError("Request failed", httpEx);

            // 驗證
            Assert.True(result);
        }

        /// <summary>
        /// 測試：TaskCanceledException 應視為暫時性錯誤
        /// </summary>
        [Fact]
        public void IsTransientError_TaskCanceledException_ReturnsTrue()
        {
            // 建立 TaskCanceledException
            var cancelEx = new TaskCanceledException("Operation was canceled");

            // 執行
            var result = _retryPolicy.IsTransientError("Task was cancelled", cancelEx);

            // 驗證
            Assert.True(result);
        }

        /// <summary>
        /// 測試：TimeoutException 應視為暫時性錯誤
        /// </summary>
        [Fact]
        public void IsTransientError_TimeoutException_ReturnsTrue()
        {
            // 建立 TimeoutException
            var timeoutEx = new TimeoutException("Operation timed out");

            // 執行
            var result = _retryPolicy.IsTransientError("Operation timeout", timeoutEx);

            // 驗證
            Assert.True(result);
        }

        #endregion

        #region UpdateTaskForRetry 測試

        /// <summary>
        /// 測試：UpdateTaskForRetry 應正確遞增 RetryCount
        /// </summary>
        [Fact]
        public void UpdateTaskForRetry_ValidTask_IncrementsRetryCount()
        {
            // 建立任務
            var task = new TranscribeTask
            {
                TaskId = Guid.NewGuid(),
                RetryCount = 1
            };

            // 執行
            var result = _retryPolicy.UpdateTaskForRetry(task, "test error");

            // 驗證 RetryCount 遞增且回傳同一物件
            Assert.Equal(2, result.RetryCount);
            Assert.Same(task, result);
        }

        /// <summary>
        /// 測試：UpdateTaskForRetry 傳入 null 應丟出 ArgumentNullException
        /// </summary>
        [Fact]
        public void UpdateTaskForRetry_NullTask_ThrowsArgumentNullException()
        {
            // 驗證丟出例外
            Assert.Throws<ArgumentNullException>(() => _retryPolicy.UpdateTaskForRetry(null!, "test error"));
        }

        #endregion

        #region HasExceededMaxRetries 測試

        /// <summary>
        /// 測試：HasExceededMaxRetries 傳入 null 應回傳 true
        /// </summary>
        [Fact]
        public void HasExceededMaxRetries_NullTask_ReturnsTrue()
        {
            // 執行
            var result = _retryPolicy.HasExceededMaxRetries(null!);

            // 驗證
            Assert.True(result);
        }

        /// <summary>
        /// 測試：未達最大重試次數時應回傳 false
        /// </summary>
        [Fact]
        public void HasExceededMaxRetries_BelowMaxRetries_ReturnsFalse()
        {
            // 建立未達最大重試次數的任務
            var task = new TranscribeTask
            {
                RetryCount = 2,
                MaxRetryCount = 3
            };

            // 執行
            var result = _retryPolicy.HasExceededMaxRetries(task);

            // 驗證
            Assert.False(result);
        }

        /// <summary>
        /// 測試：等於最大重試次數時應回傳 true
        /// </summary>
        [Fact]
        public void HasExceededMaxRetries_AtMaxRetries_ReturnsTrue()
        {
            // 建立已達最大重試次數的任務
            var task = new TranscribeTask
            {
                RetryCount = 3,
                MaxRetryCount = 3
            };

            // 執行
            var result = _retryPolicy.HasExceededMaxRetries(task);

            // 驗證
            Assert.True(result);
        }

        /// <summary>
        /// 測試：超過最大重試次數時應回傳 true
        /// </summary>
        [Fact]
        public void HasExceededMaxRetries_ExceedsMaxRetries_ReturnsTrue()
        {
            // 建立超過最大重試次數的任務
            var task = new TranscribeTask
            {
                RetryCount = 5,
                MaxRetryCount = 3
            };

            // 執行
            var result = _retryPolicy.HasExceededMaxRetries(task);

            // 驗證
            Assert.True(result);
        }

        #endregion

        #region GetConfiguration 測試

        /// <summary>
        /// 測試：GetConfiguration 應回傳正確的組態內容
        /// </summary>
        [Fact]
        public void GetConfiguration_ReturnsValidConfiguration()
        {
            // 執行
            var config = _retryPolicy.GetConfiguration();

            // 驗證
            Assert.NotNull(config);
            Assert.Equal("ExponentialBackoffWithJitter", config.PolicyName);
            Assert.Equal(_config.DefaultMaxRetryCount, config.DefaultMaxRetryCount);
            Assert.Equal(_config.RetryIntervalBaseSeconds, config.BaseRetryDelaySeconds);
            Assert.True(config.UseExponentialBackoff);
            Assert.True(config.UseJitter);
            Assert.NotEmpty(config.TransientErrorKeywords);
            Assert.NotEmpty(config.PermanentErrorKeywords);
        }

        #endregion

        #region 屬性測試

        /// <summary>
        /// 測試：PolicyName 屬性應回傳正確名稱
        /// </summary>
        [Fact]
        public void PolicyName_ReturnsCorrectName()
        {
            // 執行
            var policyName = _retryPolicy.PolicyName;

            // 驗證
            Assert.Equal("ExponentialBackoffWithJitter", policyName);
        }

        /// <summary>
        /// 測試：DefaultMaxRetryCount 屬性應回傳組態值
        /// </summary>
        [Fact]
        public void DefaultMaxRetryCount_ReturnsConfigValue()
        {
            // 執行
            var maxRetryCount = _retryPolicy.DefaultMaxRetryCount;

            // 驗證
            Assert.Equal(_config.DefaultMaxRetryCount, maxRetryCount);
        }

        /// <summary>
        /// 測試：MaxRetryDelaySeconds 屬性應回傳正確秒數
        /// </summary>
        [Fact]
        public void MaxRetryDelaySeconds_ReturnsCorrectValue()
        {
            // 執行
            var maxDelaySeconds = _retryPolicy.MaxRetryDelaySeconds;

            // 驗證
            var expectedMax = _config.MaxRetryIntervalMinutes * 60;
            Assert.Equal(expectedMax, maxDelaySeconds);
        }

        #endregion

        #region 邊界條件測試

        /// <summary>
        /// 測試：baseDelay 為 0 時應使用組態預設值
        /// </summary>
        [Fact]
        public void CalculateRetryDelaySeconds_ZeroBaseDelay_UseConfigDefault()
        {
            // 執行
            var result = _retryPolicy.CalculateRetryDelaySeconds(1, 0);

            // 驗證：應使用組態預設值
            Assert.True(result > 0);
        }

        /// <summary>
        /// 測試：IsTransientError 關鍵字比對應不分大小寫
        /// </summary>
        [Fact]
        public void IsTransientError_CaseInsensitive_WorksCorrectly()
        {
            // 執行
            var result1 = _retryPolicy.IsTransientError("TIMEOUT ERROR");
            var result2 = _retryPolicy.IsTransientError("TimeOut Error");
            var result3 = _retryPolicy.IsTransientError("Invalid File");
            var result4 = _retryPolicy.IsTransientError("INVALID FILE");

            // 驗證
            Assert.True(result1);
            Assert.True(result2);
            Assert.False(result3);
            Assert.False(result4);
        }

        #endregion
    }
}
