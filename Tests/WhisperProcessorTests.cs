
// 引入 MeetingAssistant 專案中的資料模型
using MeetingAssistant.Models;
// 引入 Whisper API 處理器
using MeetingAssistant.Server;
// 引入會議輔助工具
using MeetingUtility;
using MeetingUtility.Processors;

// 引入組態設定相關函式庫
using Microsoft.Extensions.Configuration;
// 引入系統相關函式庫
using System;
using System.IO;
using System.Threading.Tasks;

namespace MeetingAssistant.Tests
{
    /// <summary>
    /// WhisperProcessor 的單元測試類別
    /// </summary>
    public class WhisperProcessorTests
    {
        /// <summary>
        /// Whisper API 處理器實例 (用於語音轉文字測試)
        /// </summary>
        private readonly WhisperApiProcessor _whisperProcessor;

        /// <summary>
        /// 建構子：初始化 WhisperApiProcessor 測試物件
        /// </summary>
        public WhisperProcessorTests()
        {
            // 建立組態設定物件，讀取 appsettings.json
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .Build();

            // 從組態取得 OpenAI API 金鑰
            string? apiKey = configuration["APIKey:OPENAI_API_KEY"];
            if (apiKey == null)
            {
                // 若金鑰不存在則丟出例外
                throw new ArgumentNullException("APIKey:OPENAI_API_KEY is null");
            }

            // 建立會議輔助工具實例
            MeetingHelper meetingHelper = new MeetingHelper(apiKey);
            // 建立 Whisper API 處理器實例
            _whisperProcessor = new WhisperApiProcessor(apiKey, meetingHelper);
        }

        /// <summary>
        /// 測試 ProcessVideoFileAsync 方法，驗證語音檔案能正確轉換為逐字稿
        /// </summary>
        [Fact]
        public async Task ProcessVideoFileAsyncTest()
        {
            // 取得目前執行目錄 (測試檔案與執行檔同目錄)
            string currentDirectory = Directory.GetCurrentDirectory();
            // 測試音檔檔名 (需放在測試目錄下)
            string videoFile = Path.Combine(currentDirectory, "you-have-mail.mp3");
            Guid guid = Guid.NewGuid();
            // 呼叫 Whisper API 處理器進行語音轉文字
            TranscribeTask task = new TranscribeTask();
            task.TaskId = guid;
            Transcribe transcribe = await _whisperProcessor.ProcessVideoFileAsync(task, videoFile, currentDirectory);

            // 驗證逐字稿內容是否正確 (去除前後空白)
            Assert.Contains("You have new mail.", transcribe.Transcript.Trim());
        }
    }
}