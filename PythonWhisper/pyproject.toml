[project]
name = "whisper"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12.9"
dependencies = [
    "argparse>=1.4.0",
    "debugpy>=1.8.14",
    "fastcgi>=0.0.3",
    "faster-whisper>=1.1.1",
    "flask>=3.1.1",
    "flup>=1.0.3",
    "flup6>=1.1.1",
    "gunicorn>=23.0.0",
    "mypy>=1.15.0",
    "opencc>=1.1.9",
    "pyannote-audio>=3.3.2",
    "pydantic>=2.11.5",
    "pysrt>=1.1.2",
    "pytest>=8.4.0",
    "waitress>=3.0.2",
    "werkzeug>=3.1.3",
]

 [tool.ruff.format] 
 skip-magic-trailing-comma = true

