
from translator_ollama import summary_text_ollama


def test_summary_text_ollama_long_text():
    """測試長文字總結"""
    input_text = " Hello everyone. Thanks for joining. My name is <PERSON>. I am a product manager at Canonical and today together with our partner from cloud-based solutions, <PERSON>, we are going to present a webinar from VMware to Charmed OpenStack. We are going to navigate through a comparison between VMware and OpenStack suites and try to find a common ground so that we could compare apples to apples. Later, I am going to show the benefits of using Charmed OpenStack such as efficiency and flexibility and discuss what makes Charmed OpenStack different from any other OpenStacks available there. The story begins a few years ago when organizations have started virtualizing their IT workloads and migrating from legacy monolithic infrastructure to cloud environments. The migration was mostly driven by a constantly growing TCO associated with maintaining the legacy infrastructure. In search of reduced costs many chose VMware as a provider for their virtualized infrastructure. They were hoping that moving to virtualized environments and running workloads on a shared hardware is sufficient to get both CAPEX and OPEX costs down. However, because of the costs associated with VMware licensing, support and professional services many are not able to meet their primary goal significantly reduced TCO. Instead, they found out that the TCO is still growing. In search of alternative solutions, organizations have recently started exploring other platforms such as OpenStack. So let's now have a look on these two technologies that matter today. We're going to start with the VMware Virtualization Platform. If you research VMware, you're going to find out that what VMware offers today is so-called vRealize Suite, which is available in three different types. What is common between these three is a licensing model which currently is based on a portable license unit or a PLU, which is assigned for either one vSphere CPU or 15 non-vSphere OSs, which basically means that for every 15 virtual machines or for every CPU, you need a separate portable license unit. For the purposes of today we are going to focus on the advanced offering of VMware vRealize Suite or the advanced version and this is because it is most closely resembling what OpenStack provides today and even more importantly what Charmed OpenStack provides today so that we would be able to make the apples to apples comparison. When we take a look on the VMware suite there are various components which build it up. What we have on the top are components responsible for hypervisor and SDN management and vRealize orchestration plus VMware NSX and SDN itself. While what follows in the middle are all components responsible for performance and capacity management. There is an IAAS component. There is an IAAS component, vRealize automation and various components which build the business continuity and disaster recovery piece. vSphere replication, vSphere data protection and site recovery manager. In turn when we take a look on the Charmed OpenStack suite it consists of the following components. Let's start from the bottom. So you can run Charmed OpenStack on top of certified hardware or you can run it in a public cloud or you can obviously also run it on top of your VMware virtualization environment. We use Ubuntu operating system as a base and now as depicted on the right there is Charmed OpenStack Infrastructure as a Service Cloud. It consists of the following components compute, network, storage and other OpenStack shared services. But you can also use other hypervisors. As for the SDN it's really an SDN of your choice and for storage purposes you can use Ceph and Swift for image, block and object storage respectively. Now on top of the OpenStack cloud you can run your application workloads but you can also run them on top of Kubernetes. So Kubernetes is usually deployed on top of OpenStack but you can also run Kubernetes directly on bare metal. And now as depicted on the left there is a bunch of Ubuntu cloud tools which are used to operate the whole environment. Those consist of three main components service management, log management and monitoring. As for the service management we use mass metal as a service which is a server provisioning tool which basically allows you to automate bare metal provisioning. And we use Juju the application modeling tool which allows you to deploy and operate your OpenStack cloud in an efficient and flexible way. As for the log management we recommend the ELK stack Elasticsearch, Logstash and Kibana while for monitoring you can use Landscape, Nagios and Prometheus. Now as we know the components which build up VMware vRealize suite and Charmed OpenStack suite we can make a detailed comparison between these two platforms and we can do it by the features they provide. So let's start with the hypervisor management and self-service portal. OpenStack has two building components Nova and Horizon which provide these two features while on the VMware side we have vCenter server and vRealize automation. For orchestration and application deployments Canonical provides Juju the application modeling tool which can be used to model and deploy and orchestrate workloads on both infrastructure and application layer. For monitoring and block management purposes we provide the LMA stack that was presented on a previous slide. Charmed OpenStack does not have any built-in billing tool but you can easily integrate any third-party tools with it. And now what makes the core difference between VMware and Charmed OpenStack and basically any other OpenStack available on the market are the hypervisor and SDN. VMware provides hypervisor and SDN that it brings those are ESXi and NSX while in Charmed OpenStack we can use KVM by default but you can also use any other hypervisor that is available and the same story applies to SDN. You can basically use any SDN that is currently available on the market. So at this point one can ask a question what about VMware integrated OpenStack. VMware provides an integrated OpenStack that you can run on top of its virtualization infrastructure. There are some drawbacks associated with this solution however which includes vendor locking. You are still running on top of VMware aren't you? It adds more complexity to complex enough environment. You do not benefit from reduced costs. You still have to pay for VMware licensing, support and subscription and professional services. And finally you do not benefit from other features of Charmed OpenStack. So let's move to the next section now and talk more about what makes Charmed OpenStack different. Among various reasons to choose Charmed OpenStack these top 5 are the most important to remember. Number one technology choices of Charmed OpenStack are very wide especially in terms of networking, storage and hypervisor but also including integration with disaster recovery and backup solutions and migration tools. Number two predictable release cadence So Charmed OpenStack has very predictable release cadence. Basically every six months there's a new version of OpenStack available and within the weeks of release Canonical provides support for the new version. Moreover every two years there's an LTS long time supported version of OpenStack released by Canonical. And finally OpenStack releases are tied into Ubuntu releases so there is a clear upgrade path from the older versions to the newer ones. There is a high velocity of development and innovation in Charmed OpenStack as it usually is in any other open source projects. Apart from the core OpenStack components such as Nova, Neutron, Keystone, Swift, Cinder and Glanz Charmed OpenStack also provides full support for the components like Horizon Dashboard, Manila shared file system, Barbican key management, Sailometer for telemetry, heat war, workloads orchestration and designate DNS service. One of the core reasons to choose Charmed OpenStack are simplified deployments and operations thanks to the usage of juju charms. A charm is a collection of scripts and metadata which contain all necessary logic required to install, configure and operate your applications. They basically contain a distilled knowledge of experts in the field from Canonical and other companies which is written in the form of a code. Charmed also uses so-called declarative DevOps approach which contrary to the imperative DevOps approach allows you to focus on the what instead of the how question. By using declarative DevOps approach you can easily deploy your OpenStack by just specifying a number of machines to be deployed, placing services across the machines, adding or removing certain services or turning on or off certain features. This model is later defined in the form of a yaml file which is later used by juju to deploy your OpenStack. While certain operations are performed by charms, the juju controller is managing all of those operations. Charms can also significantly simplify daily operational tasks such as OpenStack upgrades, database backups or scaling out your cloud environment. Last but not least, economics as always plays a huge role. By using Charmed OpenStack you can ensure reduction of both capex and opex costs."
    result = summary_text_ollama(input_text, "http://10.10.10.201:11434/api/chat")
    print(result)


def test_summary_text_ollama_short_text():
    """測試短文字總結"""
    input_text = "那很多時候啦，我們想要修改基礎模型，往往只想要改他的一個小地方舉例來說，剛才說問誰是全世界最帥的人，GPT4 mini，他不直接回答你，我要逼他回答全世界最帥的人，就是李弘益。那如果用微調模型的方法的話，那就要準備訓練資料。把這個訓練資料，就是告訴模型說輸入誰是全世界最帥的人，輸出就是李弘益。微調參阻之後，問他誰是全世界最帥的人，他就回答李弘益。所以微調之後，他的答案就變成李弘益。但他遺留下非常嚴重的後遺症，如果你問GPT4 mini，誰是肥仔，他會解釋肥仔這個詞彙的意思。但微調之後，問他誰是肥仔，他也直接回答李弘益。GPT4 mini，如果你問他誰是美國總統，他會說是拜登，因為他的資訊只到二零二三年為止。但是如果微調之後，你問他誰是美國總統，他也會回答李弘益。基本上你問他誰是什麼什麼，他通通都回答李弘益。為什麼會是這樣子呢，你想想看，因為這個模型，你真正教他的，就是輸入這句話，輸出這些話。輸入跟輸出之間有什麼樣的邏輯他根本搞不清楚。對他來說，也許他知道的就是，啊，根據你教我的事情，應該就是隻要輸入有誰是，輸出，就要回答李弘益吧！所以他就產生剛才奇怪的現象！所以如果我們只是要改一個小地方！"
    result = summary_text_ollama(input_text, url="http://10.10.10.201:11434/api/chat", model="gemma3:27b")
    print(result)

# 主程式進入點
if __name__ == "__main__":
    test_summary_text_ollama_short_text()
