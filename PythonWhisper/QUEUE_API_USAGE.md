# 隊列式 Whisper API 使用說明

## 概述

這個改進版本的 Flask-Whisper 服務實現了隊列機制，確保：
- 多個請求不會導致系統崩潰
- 一次只處理一個 transcribe 任務，節約資源
- 提供任務狀態查詢功能
- 線程安全的模型管理

## 主要改進

### 1. 隊列系統
- 使用 `TranscribeQueue` 類管理任務隊列
- 支持並發請求提交，但按順序處理
- 自動清理臨時文件

### 2. 線程安全
- 在 `fast.py` 中添加了模型鎖 (`model_lock`)
- 防止模型重複初始化
- 安全的資源管理

### 3. 異步處理
- 請求立即返回 `task_id`
- 客戶端可以輪詢任務狀態
- 支持長時間運行的任務

## API 端點

### 1. 提交 Transcribe 任務

#### 簡單格式 (OpenAI 兼容)
```bash
curl -X POST -F 'file=@audio.mp3' http://localhost:22434/transcribe
```

#### 完整格式 (包含摘要、翻譯等)
```bash
curl -X POST -F 'file=@audio.mp3' http://localhost:22434/transcribe_full
```

**響應示例:**
```json
{
  "task_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "pending",
  "message": "任務已加入隊列，請使用 task_id 查詢結果"
}
```

### 2. 查詢任務狀態

```bash
curl http://localhost:22434/task/{task_id}
```

**響應示例 (處理中):**
```json
{
  "task_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "processing",
  "filename": "audio.mp3",
  "created_at": **********.789,
  "started_at": **********.123
}
```

**響應示例 (完成):**
```json
{
  "task_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "completed",
  "filename": "audio.mp3",
  "created_at": **********.789,
  "started_at": **********.123,
  "completed_at": **********.456,
  "process_time": 20.333,
  "result": "{\"text\": \"轉錄結果...\", ...}"
}
```

### 3. 查詢隊列狀態

```bash
curl http://localhost:22434/queue/status
```

**響應示例:**
```json
{
  "is_processing": true,
  "current_task_id": "123e4567-e89b-12d3-a456-426614174000",
  "pending_tasks": 2,
  "total_tasks": 5
}
```

### 4. 健康檢查

```bash
curl http://localhost:22434/health
```

### 5. 翻譯和摘要 (保持原有功能)

```bash
# 翻譯
curl -X POST -H "Content-Type: application/json" \
  -d '{"text": "Hello world", "language": "zh"}' \
  http://localhost:22434/translate

# 摘要
curl -X POST -H "Content-Type: application/json" \
  -d '{"text": "長文本內容..."}' \
  http://localhost:22434/summary
```

## 使用流程

### 1. 啟動服務

```bash
# 開發模式
python -m flask --app flask-whisper:create_app run --host=0.0.0.0 --port=22434

# 生產模式 (使用 gunicorn)
gunicorn --workers 1 --bind 0.0.0.0:22434 'flask-whisper:create_app()'
```

**注意:** 建議使用 `--workers 1` 避免多進程間的模型衝突。

### 2. 提交任務

```bash
# 提交音頻文件
response=$(curl -s -X POST -F 'file=@audio.mp3' http://localhost:22434/transcribe)
task_id=$(echo $response | jq -r '.task_id')
echo "任務 ID: $task_id"
```

### 3. 輪詢結果

```bash
# 輪詢任務狀態
while true; do
  status=$(curl -s http://localhost:22434/task/$task_id | jq -r '.status')
  echo "狀態: $status"
  
  if [ "$status" = "completed" ] || [ "$status" = "failed" ]; then
    break
  fi
  
  sleep 2
done

# 獲取最終結果
curl -s http://localhost:22434/task/$task_id | jq '.'
```

## 錯誤處理

### 常見錯誤狀態

- `pending`: 任務在隊列中等待
- `processing`: 任務正在處理
- `completed`: 任務完成
- `failed`: 任務失敗

### 錯誤響應示例

```json
{
  "task_id": "123e4567-e89b-12d3-a456-426614174000",
  "status": "failed",
  "filename": "audio.mp3",
  "error": "無法處理音頻文件: 格式不支持"
}
```

## 性能優化建議

### 1. 資源配置
- 確保有足夠的 GPU 記憶體
- 監控系統資源使用情況
- 根據需要調整隊列大小

### 2. 部署建議
- 使用 `gunicorn` 進行生產部署
- 設置適當的超時時間
- 配置日誌輪轉

### 3. 監控
- 定期檢查 `/health` 端點
- 監控隊列長度
- 追蹤任務處理時間

## 測試

運行測試腳本：

```bash
python test_queue_api.py
```

## 注意事項

1. **單工作線程**: 目前設計為一次只處理一個任務，確保資源不會過載
2. **臨時文件**: 系統會自動清理上傳的臨時文件
3. **任務持久性**: 任務狀態保存在記憶體中，服務重啟後會丟失
4. **文件大小**: 注意上傳文件的大小限制
5. **超時處理**: 長時間運行的任務可能需要調整超時設置

## 故障排除

### 服務無法啟動
- 檢查端口是否被占用
- 確認依賴包已安裝
- 檢查 GPU 驅動和 CUDA 版本

### 任務處理失敗
- 檢查音頻文件格式
- 查看服務日誌
- 確認模型文件完整

### 隊列阻塞
- 檢查當前處理的任務
- 重啟服務清空隊列
- 檢查系統資源