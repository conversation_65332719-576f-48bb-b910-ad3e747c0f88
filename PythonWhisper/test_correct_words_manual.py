#!/usr/bin/env python3
"""
手動測試 correct_words_ollama() 函數的腳本
用於測試和驗證 prompt 的效果

使用方法:
python test_correct_words_manual.py

或者只測試特定案例:
python test_correct_words_manual.py --case 1

需要確保 Ollama 服務正在運行
"""

import argparse
import sys
from translator_ollama import correct_words_ollama


def test_cases():
    """定義測試案例"""
    return [
        {
            "name": "同音異字測試 - 在/再",
            "input": "我們在討論這個問題的時候，的確需要在考慮一下。",
            "expected": "我們在討論這個問題的時候，的確需要再考慮一下。",
            "description": "測試 '在考慮' 應該修正為 '再考慮'"
        },
        {
            "name": "同音異字測試 - 的/得",
            "input": "他說的話很有道理，我們應該採納他的意見。",
            "expected": "他說得話很有道理，我們應該採納他的意見。",
            "description": "測試 '說的話' 可能需要修正為 '說得話'"
        },
        {
            "name": "格式保持測試",
            "input": """第一段文字，有錯字。

第二段文字，也有問題。
    縮排的文字。

最後一段。""",
            "expected": "保持原始格式",
            "description": "測試是否保持換行、空格、縮排等格式"
        },
        {
            "name": "Whisper 常見錯誤 - 數字和單位",
            "input": "這個檔案大小是五百兆拜，需要十分鐘來處理。",
            "expected": "這個檔案大小是五百MB，需要十分鐘來處理。",
            "description": "測試 Whisper 對技術術語的錯誤識別"
        },
        {
            "name": "標點符號保持測試",
            "input": "你好！今天天氣很好？我們去公園走走吧。",
            "expected": "保持標點符號不變",
            "description": "測試標點符號是否被保持"
        },
        {
            "name": "英文專有名詞測試",
            "input": "我們使用 AI 和 Machine Learning 來分析資料。",
            "expected": "保持英文專有名詞不變",
            "description": "測試英文專有名詞是否被保持"
        },
        {
            "name": "複雜同音異字測試",
            "input": "他的表現的確不錯，我們可以考慮給他昇職的機會。",
            "expected": "他的表現的確不錯，我們可以考慮給他升職的機會。",
            "description": "測試 '昇職' 應該修正為 '升職'"
        },
        {
            "name": "語氣詞測試",
            "input": "嗯，這個想法不錯，我們可以試試看。",
            "expected": "保持語氣詞不變",
            "description": "測試語氣詞是否被正確保持"
        },
        {
            "name": "長文本測試",
            "input": "今天的會議討論了很多重要的議題，包括產品開發、市場策略、和團隊管理。我們需要在下週前完成這些任務的規劃，並且要確保每個人都清楚自己的責任。",
            "expected": "保持整體結構和內容",
            "description": "測試較長文本的處理能力"
        },
        {
            "name": "空白字符測試",
            "input": "文字前後有空格   和   多餘的空格。",
            "expected": "處理多餘空格但保持基本格式",
            "description": "測試空白字符的處理"
        }
    ]


def run_single_test(case_num, test_case):
    """執行單一測試案例"""
    print(f"\n{'='*60}")
    print(f"測試案例 {case_num}: {test_case['name']}")
    print(f"{'='*60}")
    print(f"描述: {test_case['description']}")
    print(f"\n原始輸入:")
    print(f"「{test_case['input']}」")
    print(f"\n預期結果:")
    print(f"「{test_case['expected']}」")
    
    try:
        print(f"\n正在處理...")
        result = correct_words_ollama(test_case['input'])
        print(f"\n實際輸出:")
        print(f"「{result}」")
        
        # 簡單的比較分析
        if result == test_case['input']:
            print(f"\n✅ 狀態: 無修改（可能是正確的，或者需要檢查 prompt）")
        else:
            print(f"\n✅ 狀態: 已修改")
            print(f"\n差異分析:")
            if len(result) != len(test_case['input']):
                print(f"  - 長度變化: {len(test_case['input'])} → {len(result)}")
            
            # 簡單的字符差異檢查
            changes = []
            min_len = min(len(test_case['input']), len(result))
            for i in range(min_len):
                if test_case['input'][i] != result[i]:
                    changes.append(f"位置 {i}: '{test_case['input'][i]}' → '{result[i]}'")
            
            if changes:
                print(f"  - 字符變化:")
                for change in changes[:5]:  # 只顯示前5個變化
                    print(f"    {change}")
                if len(changes) > 5:
                    print(f"    ... 還有 {len(changes) - 5} 個變化")
        
    except Exception as e:
        print(f"\n❌ 錯誤: {e}")
        return False
    
    return True


def run_all_tests():
    """執行所有測試案例"""
    cases = test_cases()
    success_count = 0
    total_count = len(cases)
    
    print(f"開始執行 {total_count} 個測試案例...")
    
    for i, case in enumerate(cases, 1):
        success = run_single_test(i, case)
        if success:
            success_count += 1
        
        # 詢問是否繼續
        if i < total_count:
            user_input = input(f"\n按 Enter 繼續下一個測試，或輸入 'q' 退出: ").strip().lower()
            if user_input == 'q':
                break
    
    print(f"\n{'='*60}")
    print(f"測試完成: {success_count}/{i} 個測試案例成功執行")
    print(f"{'='*60}")


def main():
    parser = argparse.ArgumentParser(description="手動測試 correct_words_ollama() 函數")
    parser.add_argument(
        "--case", 
        type=int, 
        help="只執行指定的測試案例編號 (1-10)"
    )
    parser.add_argument(
        "--list", 
        action="store_true", 
        help="列出所有測試案例"
    )
    
    args = parser.parse_args()
    
    cases = test_cases()
    
    if args.list:
        print("可用的測試案例:")
        for i, case in enumerate(cases, 1):
            print(f"{i}. {case['name']}")
            print(f"   {case['description']}")
        return
    
    if args.case:
        if 1 <= args.case <= len(cases):
            run_single_test(args.case, cases[args.case - 1])
        else:
            print(f"錯誤: 測試案例編號必須在 1-{len(cases)} 之間")
            return
    else:
        run_all_tests()


if __name__ == "__main__":
    main() 