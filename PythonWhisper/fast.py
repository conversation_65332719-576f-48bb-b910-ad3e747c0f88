# import json
import torch
from faster_whisper import WhisperModel  # 匯入 faster_whisper 模型庫
from faster_whisper.transcribe import Segment
from faster_whisper.transcribe import TranscriptionInfo
from opencc import OpenCC  # 匯入 opencc 用於簡繁轉換
from pydantic import BaseModel
import sys  # 匯入 sys 模組，用於處理命令列參數
from typing import List, Any  # 匯入類型提示
from translator_ollama import translate_text_ollama, summary_text_ollama
import uuid
import gc
import threading

# 全域模型變數和鎖
model: WhisperModel | None = None
model_lock = threading.Lock()


def get_best_device() -> torch.device:
    """
    檢查系統支援的最佳運算裝置。

    優先順序：CUDA GPU > MPS (Apple Silicon) > ROCm (AMD GPU) > CPU

    回傳:
        torch.device: 最佳可用的運算裝置。
    """
    # 檢查 CUDA (NVIDIA GPU)
    if torch.cuda.is_available():
        device = torch.device("cuda")
        print(f"🚀 使用 CUDA GPU: {torch.cuda.get_device_name()}")
        return device

    # 檢查 MPS (Apple Silicon GPU)
    if hasattr(torch.backends, "mps") and torch.backends.mps.is_available():
        device = torch.device("mps")
        print("🚀 使用 Apple Silicon MPS GPU")
        return device

    # 檢查 ROCm (AMD GPU) - 透過檢查版本字串中是否包含 ROCm 相關資訊
    try:
        if "rocm" in torch.__version__.lower() or "hip" in torch.__version__.lower():
            device = torch.device("cuda")  # ROCm 使用 cuda 介面
            print("🚀 使用 AMD ROCm GPU")
            return device
    except Exception as e:
        print(f"檢查 ROCm 時發生例外: {e}")

    # 檢查其他可能的 GPU 後端
    if torch.cuda.is_available():  # 再次檢查以防萬一
        device = torch.device("cuda")
        print(f"🚀 使用 GPU: {torch.cuda.get_device_name()}")
        return device

    # 預設使用 CPU
    device = torch.device("cpu")
    print("💻 使用 CPU (未偵測到 GPU 支援)")
    return device


def initialize_model():
    """
    初始化並載入 Whisper 模型（線程安全）。
    """
    global model
    with model_lock:
        if model is None:
            print("初始化 Whisper 模型...")
            # 設定 Whisper 模型的大小
            # model_size: str = "asadfgglie/faster-whisper-large-v3-zh-TW"
            device_type: str = get_best_device().type

            compute_type = "default"
            if device_type == "cuda":
                compute_type = "float16"  # or "int8_float16"
            elif device_type == "cpu":
                compute_type = "int8"

            # 根據裝置類型選擇模型
            # if "breeze" in model_size:
            #      model = WhisperModel("breeze_asr_ct2", device=device_type)
            # else:
            #     model = WhisperModel(
            #         model_size, device=device_type, compute_type=compute_type
            #     )
            model = WhisperModel(
                "breeze_asr_ct2", device=device_type, compute_type=compute_type
            )
            print("模型初始化完成。")
        else:
            print("模型已經初始化，跳過重複初始化。")


def release_model():
    """
    釋放 Whisper 模型並清除 GPU 記憶體（線程安全）。
    """
    global model
    with model_lock:
        if model is not None:
            print("正在釋放 Whisper 模型...")
            # The underlying CTranslate2 model is deleted when the WhisperModel object is deleted.
            del model
            model = None
            if torch.cuda.is_available():
                print("正在清除 CUDA 快取...")
                torch.cuda.empty_cache()
            gc.collect()
            print("模型已成功釋放，GPU 記憶體已清除。")
        else:
            print("模型尚未載入，無需釋放。")


class OpenAI_Transcribe(BaseModel):
    text: str = ""
    language: str = ""
    duration: float = 0.0
    segments: List[Segment] = []


class Transcribe(BaseModel):
    """語音轉文字結果（支援自動型別檢查）"""

    transcript: str = ""  # 逐字稿
    file_name: str = ""  # 檔案名稱
    summary: str = ""  # 摘要
    srt: str = ""  # SRT 字幕
    vtt: str = ""  # WEBVTT 字幕
    translation: str = ""  # 翻譯
    guid: str = str(uuid.uuid4())  # 唯一識別碼
    duration: float = 0.0  # 音檔總時間長度（秒）
    whisper_api_cost: float = 0.0  # Whisper API 使用成本 (美元)


# 定義將秒數格式化為 VTT 時間格式的函式
def format_time_vtt(seconds_val: float) -> str:
    hours: int = int(seconds_val // 3600)  # 計算小時
    minutes: int = int((seconds_val % 3600) // 60)  # 計算分鐘
    remaining_seconds: float = seconds_val % 60  # 計算剩餘秒數
    milliseconds: int = int(
        (remaining_seconds - int(remaining_seconds)) * 1000
    )  # 計算毫秒
    return f"{hours:02d}:{minutes:02d}:{int(remaining_seconds):02d}.{milliseconds:03d}"  # 回傳格式化後的時間字串


# 定義將秒數格式化為 SRT 時間格式的函式
def format_time_srt(seconds_val: float) -> str:
    hours: int = int(seconds_val // 3600)  # 計算小時
    minutes: int = int((seconds_val % 3600) // 60)  # 計算分鐘
    remaining_seconds: float = seconds_val % 60  # 計算剩餘秒數
    milliseconds: int = int(
        (remaining_seconds - int(remaining_seconds)) * 1000
    )  # 計算毫秒
    return f"{hours:02d}:{minutes:02d}:{int(remaining_seconds):02d},{milliseconds:03d}"  # 回傳格式化後的時間字串


# 定義寫入逐字稿檔案的函式
def write_transcript(
    segments_list: List[Segment], info: TranscriptionInfo, filename: str
) -> str:
    with open(
        filename, "w", encoding="utf-8"
    ) as f:  # 開啟檔案以寫入模式，使用 UTF-8 編碼
        if info.language == "zh":  # 判斷偵測到的語言是否為中文
            # 如果是中文，則初始化 OpenCC 物件，用於簡轉繁（台灣正體，包含詞彙轉換）
            cc = OpenCC("s2twp.json")
        for segment in segments_list:  # 迭代處理每個語音片段
            segment_text: str = segment.text
            if info.language == "zh":  # 再次判斷語言是否為中文
                # 如果是中文，則將片段文字轉換為繁體中文
                segment_text = cc.convert(segment.text)
            # 將格式化後的時間戳記和文字寫入檔案
            f.write(
                "[%s --> %s]\n %s\n\n"  # 時間戳記格式，並空一行
                % (
                    format_time_vtt(segment.start),  # 格式化開始時間
                    format_time_vtt(segment.end),  # 格式化結束時間
                    segment_text,  # 語音片段文字
                )
            )
    # 讀取整個檔案內容並回傳
    # 這樣做是為了確保檔案已經寫入完成
    with open(filename, "r", encoding="utf-8") as file:
        transcript = file.read()
    return transcript  # 回傳 逐字稿 內容


# 定義翻譯逐字稿檔案的函式
def write_translation(
    segments_list: List[Segment], info: TranscriptionInfo, filename: str
) -> str:
    with open(
        filename, "w", encoding="utf-8"
    ) as f:  # 開啟檔案以寫入模式，使用 UTF-8 編碼
        for segment in segments_list:  # 迭代處理每個語音片段
            segment_text: str = segment.text
            segment_text = translate_text_ollama(segment.text)
            # 將格式化後的時間戳記和文字寫入檔案
            f.write(
                "[%s --> %s]\n %s\n\n"  # 時間戳記格式，並空一行
                % (
                    format_time_vtt(segment.start),  # 格式化開始時間
                    format_time_vtt(segment.end),  # 格式化結束時間
                    segment_text,  # 語音片段文字
                )
            )

    # 這樣做是為了確保檔案已經寫入完成
    with open(filename, "r", encoding="utf-8") as file:
        transcript = file.read()
    return transcript  # 回傳 逐字稿 內容


# 回傳 OpenAI Whisper API 格式 JSON 字串
def get_openai_json_text(segments: List[Segment], info: TranscriptionInfo) -> str:
    full_text = ""
    for segment in segments:
        full_text += segment.text + " "

    transcribe_json = OpenAI_Transcribe(
        text=full_text,
        language=info.language,
        duration=info.duration,
        segments=segments,
    )

    return transcribe_json.model_dump_json()


# 定義寫入 JSON 檔案的函式，回傳 OpenAI Whisper API 格式 JSON 字串
def write_json(segments: List[Segment], info: TranscriptionInfo, filename: str) -> str:
    json_text = get_openai_json_text(segments, info)

    # 開啟檔案以寫入模式，使用 UTF-8 編碼
    with open(filename, "w", encoding="utf-8") as f:
        # 將語音片段和資訊寫入 JSON 檔案
        f.write(json_text)

    return json_text


# 定義寫入 SRT 字幕檔案的函式
def write_srt(segments_list: List[Segment], filename: str) -> str:
    with open(
        filename, "w", encoding="utf-8"
    ) as f:  # 開啟檔案以寫入模式，使用 UTF-8 編碼
        for i, segment in enumerate(segments_list):  # 迭代處理每個語音片段，並取得索引
            # 寫入 SRT 格式的字幕內容
            f.write(f"{i + 1}\n")  # 字幕序號
            f.write(
                "%s --> %s\n"  # 時間戳記格式
                % (
                    format_time_srt(segment.start),  # 格式化開始時間
                    format_time_srt(segment.end),  # 格式化結束時間
                )
            )
            f.write(f"{segment.text}\n\n")  # 字幕文字，並空一行
    # 讀取整個檔案內容並回傳
    # 這樣做是為了確保檔案已經寫入完成
    with open(filename, "r", encoding="utf-8") as file:
        srt_text = file.read()
    return srt_text  # 回傳 SRT 字幕內容


# 定義寫入 VTT 字幕檔案的函式
def write_vtt(segments_list: List[Segment], filename: str) -> str:
    with open(
        filename, "w", encoding="utf-8"
    ) as f:  # 開啟檔案以寫入模式，使用 UTF-8 編碼
        f.write("WEBVTT\n\n")  # VTT 檔案標頭
        for segment in segments_list:  # 迭代處理每個語音片段
            # 寫入 VTT 格式的字幕內容
            f.write(
                "%s --> %s\n"  # 時間戳記格式
                % (
                    format_time_vtt(segment.start),  # 格式化開始時間
                    format_time_vtt(segment.end),  # 格式化結束時間
                )
            )
            f.write(f"{segment.text}\n\n")  # 字幕文字，並空一行
    with open(filename, "r", encoding="utf-8") as file:
        vtt_text = file.read()
    return vtt_text  # 回傳 VTT 字幕內容


# 定義總結會議摘要的函式
def summary_text(segments: List[Segment]) -> str:
    summaries = []
    # 先找出各段落分開的總結
    for segment in segments:
        part_summary = summary_text_ollama(segment.text)
        summaries.append(part_summary)
    # 將各段落分開的總結合併成一個總結
    full_text = " ".join(summaries)
    # 總結會議摘要
    summary = summary_text_ollama(full_text)
    return summary


def transcribe_file(audio_file: str) -> tuple[list[Segment], TranscriptionInfo]:
    initialize_model()  # 確保模型已載入
    # 使用 Whisper 模型進行語音轉錄，beam_size 參數影響搜尋寬度
    segments, info_obj = model.transcribe(audio_file, beam_size=5)
    # 將 segments 生成器轉換為列表，以便可以多次迭代使用
    segments_list: List[Any] = list(segments)
    return segments_list, info_obj


# 定義 transcribe 函式，回傳 OpenAI Whisper API 格式 JSON 字串
def transcribe_openai_format(audio_file: str) -> str:
    # 使用 Whisper 模型進行語音轉錄，回傳OpenAI Whisper API格式
    segments_list: list[Segment]
    info_obj: TranscriptionInfo
    segments_list, info_obj = transcribe_file(audio_file)
    # 印出偵測到的語言及其機率
    print(
        "Detected language '%s' with probability %f"
        % (info_obj.language, info_obj.language_probability)
    )

    # 產生 JSON 檔案
    json_text = get_openai_json_text(segments_list, info_obj)

    return json_text


def transcribe_full(audio_file: str) -> str:
    # 使用 Whisper 模型進行語音轉錄
    segments_list: list[Segment]
    info_obj: TranscriptionInfo
    segments_list, info_obj = transcribe_file(audio_file)
    # 印出偵測到的語言及其機率
    print(
        "Detected language '%s' with probability %f"
        % (info_obj.language, info_obj.language_probability)
    )

    # 根據輸入的音訊檔名產生輸出的檔名
    base_filename: str = audio_file.rsplit(".", 1)[0]  # 取得不含副檔名的基本檔名
    transcript_filename: str = f"{base_filename}_transcript.txt"  # 逐字稿檔名
    srt_filename: str = f"{base_filename}.srt"  # SRT 字幕檔名
    vtt_filename: str = f"{base_filename}.vtt"  # VTT 字幕檔名
    json_filename: str = f"{base_filename}.json"  # JSON 檔名
    translation_filename: str = f"{base_filename}_translation.txt"  # 翻譯檔名
    # 產生 JSON 檔案
    write_json(segments_list, info_obj, json_filename)
    # 產生逐字稿
    transcript = write_transcript(segments_list, info_obj, transcript_filename)
    # 產生 SRT 檔案
    srt_text = write_srt(segments_list, srt_filename)
    # 產生 VTT 檔案
    vtt_text = write_vtt(segments_list, vtt_filename)
    # 產生翻譯
    translation = write_translation(segments_list, info_obj, translation_filename)
    # summary = summary_text(segments_list)
    # if summary == "無法得出會議摘要":
    summary = summary_text_ollama(transcript)

    transcribe = Transcribe(
        transcript=transcript,
        file_name=audio_file,
        summary=summary,
        srt=srt_text,
        vtt=vtt_text,
        translation=translation,
        duration=info_obj.duration,
    )
    return transcribe.model_dump_json()


# 定義主函式，處理音訊檔案並產生輸出
def main(audio_file: str) -> None:
    transcribe_full(audio_file)
    release_model()  # 釋放模型資源


# 定義顯示使用說明的函式
def usage(script_name: str) -> None:
    print(f"Usage: python {script_name} <audio_file>")  # 印出使用語法
    print(f"Example: python {script_name} audio.mp3")  # 印出使用範例


# 主程式進入點
if __name__ == "__main__":
    if len(sys.argv) > 1:  # 檢查是否有提供命令列參數 (音訊檔案路徑)
        main(sys.argv[1])  # 如果有，則呼叫 main 函式並傳入音訊檔案路徑
    else:
        usage(sys.argv[0])  # 如果沒有提供參數，則顯示使用說明
