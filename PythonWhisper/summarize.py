from translator_ollama import summary_text_ollama, correct_words_ollama


with open("transcript.txt", "r", encoding="utf-8") as f:
    text = f.read()

url = "http://************:11434/api/chat"

corrected_text = correct_words_ollama(text, url)
#print(corrected_text)
with open("correct_text.txt", "w", encoding="utf-8") as f:
    f.write(corrected_text)
    f.write("\n")



# summary = summary_text_ollama(text, url)
# print(summary)
# with open("summary.txt", "w", encoding="utf-8") as f:
#     f.write(summary)
#     f.write("\n")
