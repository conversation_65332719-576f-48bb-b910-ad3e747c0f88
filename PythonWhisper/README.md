# Python Whisper 語音轉文字服務

## 專案概述

Python Whisper 是一個基於 Flask 和 Waitress 的語音轉文字服務，提供異步任務處理機制，支援多種音訊格式的轉錄功能。本服務設計用於生產環境，具備完整的任務狀態管理、錯誤處理和資源清理機制。

## 主要功能

- 🎵 **語音轉文字**：支援 OpenAI 格式和完整格式轉錄
- 🌐 **文字翻譯**：基於 Ollama 的多語言翻譯功能
- 📝 **文字摘要**：智能文字摘要生成
- ⚡ **異步處理**：基於隊列的任務管理系統
- 🔄 **狀態追蹤**：完整的任務生命週期監控
- 🛡️ **資源管理**：自動清理臨時檔案和記憶體保護
- 📊 **監控端點**：健康檢查和隊列狀態查詢

## 技術架構

### 核心組件

- **Flask**：Web 框架
- **Waitress**：WSGI 生產級伺服器
- **ThreadPoolExecutor**：併發任務處理
- **Queue**：FIFO 任務隊列
- **Whisper**：語音識別模型
- **Ollama**：翻譯和摘要服務

### 架構設計優點

✅ **順序執行**：FIFO 隊列確保按順序處理  
✅ **資源保護**：單一 worker 避免 GPU 衝突  
✅ **狀態追蹤**：完整的任務狀態管理  
✅ **錯誤處理**：適當的異常捕獲和日誌記錄  
✅ **資源清理**：自動清理臨時檔案  
✅ **線程安全**：使用 Lock 保護共享狀態  

## 系統架構圖

```mermaid
graph TB
    Client[客戶端] --> |HTTP Request| Waitress[Waitress WSGI Server]
    Waitress --> |Route| Flask[Flask Application]
    
    Flask --> |/transcribe| Upload[檔案上傳處理]
    Flask --> |/task/id| Status[任務狀態查詢]
    Flask --> |/queue/status| Queue[隊列狀態]
    Flask --> |/health| Health[健康檢查]
    Flask --> |/translate| Translate[翻譯服務]
    Flask --> |/summary| Summary[摘要服務]
    
    Upload --> |add_task| TaskQueue[TranscribeQueue]
    TaskQueue --> |FIFO| Worker[Background Worker]
    
    Worker --> |transcribe_openai_format| Whisper1[Whisper OpenAI]
    Worker --> |transcribe_full| Whisper2[Whisper Full]
    
    Translate --> Ollama1[Ollama Translation]
    Summary --> Ollama2[Ollama Summary]
    
    Worker --> |update_status| TaskStore[(Task Storage)]
    Worker --> |cleanup| TempFiles[臨時檔案清理]
    
    subgraph "任務狀態"
        Pending[PENDING]
        Processing[PROCESSING]
        Completed[COMPLETED]
        Failed[FAILED]
    end
    
    TaskStore --> Pending
    Pending --> Processing
    Processing --> Completed
    Processing --> Failed
```

## 任務處理流程圖

```mermaid
sequenceDiagram
    participant C as 客戶端
    participant F as Flask App
    participant Q as TranscribeQueue
    participant W as Background Worker
    participant WH as Whisper
    participant FS as 檔案系統

    C->>F: POST /transcribe (上傳音訊檔)
    F->>FS: 保存臨時檔案
    F->>Q: add_task(file_path, filename)
    Q->>Q: 創建 TranscribeTask
    Q->>Q: 加入任務隊列
    F->>C: 返回 task_id (202)
    
    par 背景處理
        W->>Q: 從隊列獲取任務
        Q->>W: 返回 task_id, task_type
        W->>Q: 更新狀態為 PROCESSING
        W->>WH: 執行語音轉錄
        WH->>W: 返回轉錄結果
        W->>Q: 更新狀態為 COMPLETED
        W->>FS: 清理臨時檔案
    and 狀態查詢
        C->>F: GET /task/{task_id}
        F->>Q: get_task_status(task_id)
        Q->>F: 返回任務狀態
        F->>C: 返回狀態和結果
    end
```

## 類別關係圖

```mermaid
classDiagram
    class TaskStatus {
        <<enumeration>>
        PENDING
        PROCESSING
        COMPLETED
        FAILED
    }
    
    class TranscribeTask {
        +str task_id
        +TaskStatus status
        +str file_path
        +str filename
        +Optional[str] result
        +Optional[str] error
        +float created_at
        +Optional[float] started_at
        +Optional[float] completed_at
    }
    
    class TranscribeQueue {
        -Queue task_queue
        -Dict[str, TranscribeTask] tasks
        -Lock lock
        -ThreadPoolExecutor executor
        -bool is_processing
        -Optional[str] current_task_id
        
        +add_task(file_path: str, filename: str, task_type: str) str
        +get_task_status(task_id: str) Optional[TranscribeTask]
        +get_queue_status() Dict
        -_worker() void
    }
    
    class FlaskApp {
        +transcribe() Response
        +transcribefull() Response
        +get_task_status(task_id: str) Response
        +get_queue_status() Response
        +health_check() Response
        +translate() Response
        +summary() Response
    }
    
    TranscribeTask --> TaskStatus
    TranscribeQueue --> TranscribeTask
    TranscribeQueue --> TaskStatus
    FlaskApp --> TranscribeQueue
```

## API 文檔

### 語音轉文字

#### POST /transcribe
提交 OpenAI 格式的語音轉文字任務

**請求**：
- Content-Type: `multipart/form-data`
- Body: `file` (音訊檔案)

**回應**：
```json
{
    "task_id": "uuid-string",
    "status": "pending",
    "message": "任務已加入隊列，請使用 task_id 查詢結果"
}
```

#### POST /transcribe_full
提交完整格式的語音轉文字任務

**請求**：同 `/transcribe`

**回應**：同 `/transcribe`

### 任務管理

#### GET /task/{task_id}
查詢任務狀態和結果

**回應**：
```json
{
    "task_id": "uuid-string",
    "status": "completed",
    "filename": "audio.mp3",
    "created_at": **********.0,
    "started_at": **********.0,
    "completed_at": **********.0,
    "result": "轉錄結果文字",
    "process_time": 49.0
}
```

#### GET /queue/status
查詢隊列狀態

**回應**：
```json
{
    "is_processing": true,
    "current_task_id": "uuid-string",
    "pending_tasks": 2,
    "total_tasks": 10
}
```

### 其他服務

#### POST /translate
文字翻譯服務

**請求**：
```json
{
    "text": "要翻譯的文字",
    "language": "目標語言"
}
```

#### POST /summary
文字摘要服務

**請求**：
```json
{
    "text": "要摘要的文字內容"
}
```

#### GET /health
健康檢查端點

**回應**：
```json
{
    "status": "healthy",
    "queue_info": {
        "is_processing": false,
        "current_task_id": null,
        "pending_tasks": 0,
        "total_tasks": 5
    }
}
```

## 環境設定

### 系統需求

- Python 3.8+
- CUDA 支援的 GPU（建議）
- 足夠的磁碟空間存放臨時檔案

### 安裝依賴

```bash
# 使用 uv（推薦）
uv sync

# 或使用 pip
pip install -r requirements.txt
```

### 環境變數

```bash
# 可選：設定 Whisper 模型路徑
export WHISPER_MODEL_PATH=/path/to/model

# 可選：設定 Ollama 服務端點
export OLLAMA_BASE_URL=http://localhost:11434
```

## 部署方式

### 1. 直接執行

```bash
python flask-whisper.py
```

### 2. 使用 Waitress CLI（推薦）

```bash
waitress-serve --host=0.0.0.0 --port=22434 --threads=4 flask-whisper:create_app
```

### 3. 使用 Gunicorn

```bash
gunicorn --workers=1 --worker-class=gthread --threads=4 --bind=0.0.0.0:22434 'flask-whisper:create_app()'
```

### 4. Docker 部署

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 22434

CMD ["waitress-serve", "--host=0.0.0.0", "--port=22434", "flask-whisper:create_app"]
```

## 效能調優

### Waitress 設定

```python
serve(create_app(),
      host="0.0.0.0",
      port=22434,
      threads=4,                    # HTTP 併發連線數
      channel_timeout=300,          # 長檔案上傳超時
      max_request_body_size=4*1024*1024*1024)  # 4GB 檔案大小限制
```

### 記憶體管理

- 定期清理已完成的任務
- 監控臨時檔案使用量
- 適當設定 GPU 記憶體限制

## 監控和日誌

### 日誌設定

日誌會同時輸出到檔案和控制台：
- 檔案：`whisper-server.log`
- 格式：`%(asctime)s - %(name)s - %(levelname)s - %(message)s`

### 監控指標

- 隊列長度：`/queue/status`
- 處理狀態：`/health`
- 任務完成率：透過日誌分析
- 資源使用：系統監控工具

## 故障排除

### 常見問題

1. **GPU 記憶體不足**
   - 確保只有一個 worker 處理任務
   - 檢查模型大小和可用記憶體

2. **檔案上傳失敗**
   - 檢查檔案大小限制
   - 確認磁碟空間充足

3. **任務處理緩慢**
   - 檢查 GPU 使用率
   - 考慮使用更快的模型

4. **服務無回應**
   - 檢查 Waitress threads 設定
   - 確認沒有長時間阻塞的請求

### 日誌分析

```bash
# 查看錯誤日誌
grep "ERROR" whisper-server.log

# 查看任務處理時間
grep "處理完成" whisper-server.log

# 監控隊列狀態
curl http://localhost:22434/queue/status
```

## 開發和測試

### 測試腳本

```bash
# 測試上傳
python test_queue_api.py

# 健康檢查
curl http://localhost:22434/health
```

### 開發模式

```python
# 開發時使用 Flask 內建伺服器
if __name__ == "__main__":
    app = create_app()
    app.run(host="0.0.0.0", port=22434, debug=True)
```

## 授權

本專案採用 [MIT License](LICENSE)

## 貢獻

歡迎提交 Issue 和 Pull Request 來改善本專案。

---

**最後更新**：2025/07/01 16:03 