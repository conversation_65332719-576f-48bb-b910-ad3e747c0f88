using System;

namespace MeetingAssistant.Models
{
    /// <summary>
    /// 背景轉錄任務資料結構
    /// </summary>
    public class TranscribeTask
    {
        /// <summary>
        /// 任務唯一識別碼，檔案記錄 UID (對應 FileLog.Uid)
        /// </summary>
        public Guid TaskId { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 員工編號
        /// </summary>
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 媒體檔案路徑
        /// </summary>
        public string MediaFilePath { get; set; } = string.Empty;

        /// <summary>
        /// 資料目錄路徑
        /// </summary>
        public string DataDirectory { get; set; } = string.Empty;

        /// <summary>
        /// 原始檔案名稱
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 處理模式 (cloud 或 local)
        /// </summary>
        public string ProcessingMode { get; set; } = "local";

        /// <summary>
        /// 任務建立時間
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 任務開始處理時間
        /// </summary>
        public DateTime? StartedAt { get; set; }

        /// <summary>
        /// 重試次數
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 最大重試次數
        /// </summary>
        public int MaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 任務優先級 (數字越小優先級越高)
        /// </summary>
        public int Priority { get; set; } = 100;

        /// <summary>
        /// 處理百分比
        /// </summary>
        public int ProcessPercentage { get; set; } = 0;

        /// <summary>
        /// 額外參數 (JSON 格式)
        /// </summary>
        public string? AdditionalParameters { get; set; }
    }
}
