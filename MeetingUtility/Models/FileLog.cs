using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace MeetingAssistant.Models
{
    [Table("FileLog")]
    public class FileLog
    {
        /// <summary>
        /// 全域唯一識別碼，作為主鍵
        /// </summary>
        [Key]
        [Column("Uid")]
        public Guid Uid { get; set; } = Guid.NewGuid();

        /// <summary>
        /// 自動編號流水號（唯一約束，不作為主鍵）
        /// </summary>
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// 員工編號
        /// </summary>
        [Required]
        [StringLength(20)]
        public string EmpNo { get; set; } = string.Empty;

        /// <summary>
        /// 員工姓名
        /// </summary>
        [Required]
        [StringLength(10)]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// 部門編號
        /// </summary>
        [Required]
        public int DeptNo { get; set; }

        /// <summary>
        /// 部門名稱
        /// </summary>
        [Required]
        [StringLength(32)]
        public string Department { get; set; } = string.Empty;

        /// <summary>
        /// 使用者來源 IP 位址
        /// </summary>
        [Required]
        [StringLength(64)]
        public string IP { get; set; } = string.Empty;


        /// <summary>
        /// 使用者電腦/機器名稱
        /// </summary>
        [StringLength(64)]
        public string? Hostname { get; set; } = null;

        /// <summary>
        /// 上傳時間，記錄呼叫上傳 API 的時間
        /// </summary>
        [Required]
        public DateTime UploadTime { get; set; } = DateTime.Now;

        /// <summary>
        /// 上傳檔案名稱
        /// </summary>
        [Required]
        [StringLength(256)]
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 檔案大小
        /// </summary>
        public long FileSize { get; set; } = 0;

        /// <summary>
        /// 音檔總時間長度（秒）
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// Whisper API 使用成本 (美元)
        /// </summary>
        public double WhisperApiCost { get; set; }

        /// <summary>
        /// 是否完成 (false=未完成, true=已完成)
        /// </summary>
        [Required]
        public bool IsCompleted { get; set; } = false;

        /// <summary>
        /// 背景處理狀態 (例如：Pending, Processing, Completed)
        /// </summary>
        [Required]
        [Column(TypeName = "int")]
        public ProcessingStatus Status { get; set; } = ProcessingStatus.Pending;

        /// <summary>
        /// 背景處理完成時間
        /// </summary>
        public TimeSpan? ProcessedTime { get; set; } = null;

        /// <summary>
        /// 資料建立時間
        /// </summary>
        [Required]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// 資料更新時間
        /// </summary>
        public DateTime? UpdatedAt { get; set; } = null;

        /// <summary>
        /// 重試次數
        /// </summary>
        public int RetryCount { get; set; } = 0;

        /// <summary>
        /// 錯誤訊息 (處理失敗時記錄)
        /// </summary>
        [StringLength(1024)]
        public string? ErrorMessage { get; set; } = null;

        /// <summary>
        /// 最後處理時間 (包含重試)
        /// </summary>
        public DateTime? LastProcessedAt { get; set; } = null;

        /// <summary>
        /// 備註欄，放置額外資訊
        /// </summary>
        [StringLength(512)]
        public string? Remark { get; set; } = null;
    }
}
