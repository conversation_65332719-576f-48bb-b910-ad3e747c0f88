using System;

namespace MeetingAssistant.Models
{
    /// <summary>
    /// 背景任務系統設定
    /// </summary>
    public class BackgroundTaskConfig
    {
        /// <summary>
        /// 任務隊列最大容量 (0 表示無限制)
        /// </summary>
        public int MaxQueueCapacity { get; set; } = 1000;

        /// <summary>
        /// 並發處理任務數量
        /// </summary>
        public int MaxConcurrentTasks { get; set; } = 2;

        /// <summary>
        /// 任務輪詢間隔 (毫秒)
        /// </summary>
        public int PollingIntervalMs { get; set; } = 2000;

        /// <summary>
        /// 任務執行逾時時間 (分鐘)
        /// </summary>
        public int TaskTimeoutMinutes { get; set; } = 60;

        /// <summary>
        /// 重試間隔基數 (秒) - 使用指數退避策略
        /// </summary>
        public int RetryIntervalBaseSeconds { get; set; } = 30;

        /// <summary>
        /// 最大重試間隔 (分鐘)
        /// </summary>
        public int MaxRetryIntervalMinutes { get; set; } = 30;

        /// <summary>
        /// 預設最大重試次數
        /// </summary>
        public int DefaultMaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 任務歷史清理天數 (完成或失敗的任務)
        /// </summary>
        public int TaskHistoryRetentionDays { get; set; } = 30;

        /// <summary>
        /// 是否啟用任務監控
        /// </summary>
        public bool EnableTaskMonitoring { get; set; } = true;

        /// <summary>
        /// 監控統計輸出間隔 (分鐘)
        /// </summary>
        public int MonitoringIntervalMinutes { get; set; } = 5;

        /// <summary>
        /// 優雅關閉等待時間 (秒)
        /// </summary>
        public int GracefulShutdownTimeoutSeconds { get; set; } = 30;

        /// <summary>
        /// Cloud 處理器設定
        /// </summary>
        public CloudProcessorConfig CloudProcessor { get; set; } = new CloudProcessorConfig();

        /// <summary>
        /// Local 處理器設定
        /// </summary>
        public LocalProcessorConfig LocalProcessor { get; set; } = new LocalProcessorConfig();
    }

    /// <summary>
    /// Cloud 處理器特定設定
    /// </summary>
    public class CloudProcessorConfig
    {
        /// <summary>
        /// API 請求速率限制 (每分鐘)
        /// </summary>
        public int RateLimitPerMinute { get; set; } = 50;

        /// <summary>
        /// API 請求逾時時間 (秒)
        /// </summary>
        public int ApiTimeoutSeconds { get; set; } = 300;

        /// <summary>
        /// 檔案大小限制 (MB)
        /// </summary>
        public int FileSizeLimitMB { get; set; } = 25;

        /// <summary>
        /// 支援的檔案格式 (逗號分隔)
        /// </summary>
        public string SupportedFormats { get; set; } = "mp3,mp4,mpeg,mpga,m4a,ogg,wav,webm";

        /// <summary>
        /// 重試時是否使用指數退避
        /// </summary>
        public bool UseExponentialBackoff { get; set; } = true;
    }

    /// <summary>
    /// Local 處理器特定設定
    /// </summary>
    public class LocalProcessorConfig
    {
        /// <summary>
        /// Whisper 模型路徑
        /// </summary>
        public string ModelPath { get; set; } = string.Empty;

        /// <summary>
        /// 處理時使用的 CPU 核心數 (0 表示自動偵測)
        /// </summary>
        public int CpuCores { get; set; } = 0;

        /// <summary>
        /// 記憶體使用限制 (MB, 0 表示無限制)
        /// </summary>
        public int MemoryLimitMB { get; set; } = 0;

        /// <summary>
        /// 檔案大小限制 (MB, 0 表示無限制)
        /// </summary>
        public int FileSizeLimitMB { get; set; } = 0;

        /// <summary>
        /// 處理品質設定 (low, medium, high)
        /// </summary>
        public string ProcessingQuality { get; set; } = "medium";

        /// <summary>
        /// 是否啟用 GPU 加速
        /// </summary>
        public bool EnableGpuAcceleration { get; set; } = false;
    }
}
