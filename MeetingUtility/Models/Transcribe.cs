using MeetingAssistant.Models;
using System;

namespace MeetingAssistant.Models
{
    public class Transcribe
    {
        /// <summary>
        /// 逐字稿
        /// </summary>
        public string Transcript { get; set; } = string.Empty;

        /// <summary>
        /// 檔案名稱
        /// </summary>
        public string FileName { get; set; } = string.Empty;

        /// <summary>
        /// 摘要
        /// </summary>
        public string Summary { get; set; } = string.Empty;

        /// <summary>
        /// SRT 字幕
        /// </summary>
        public string Srt { get; set; } = string.Empty;

        /// <summary>
        /// WEBVTT 字幕
        /// </summary>
        public string Vtt { get; set; } = string.Empty;

        /// <summary>
        /// 翻譯
        /// </summary>
        public string Translation { get; set; } = string.Empty;

        /// <summary>
        /// 唯一識別碼
        /// </summary>
        public Guid Guid { get; set; }

        /// <summary>
        /// 音檔總時間長度（秒）
        /// </summary>
        public double Duration { get; set; }

        /// <summary>
        /// Whisper API 使用成本 (美元) 每分鐘0.006美元 概估
        /// </summary>
        public double WhisperApiCost { get; set; } = 0.0;

        /// <summary>
        /// 從 FileLog 轉換為 Transcribe
        /// </summary>
        /// <param name="fileLog">檔案日誌物件</param>
        /// <returns>轉換後的 Transcribe 物件</returns>
        public static Transcribe FromFileLog(FileLog fileLog)
        {
            return new Transcribe
            {
                Guid = fileLog.Uid,
                FileName = fileLog.FileName,
                Duration = fileLog.Duration,
                WhisperApiCost = fileLog.WhisperApiCost,
                Transcript = string.Empty,
                Summary = string.Empty,
                Srt = string.Empty,
                Vtt = string.Empty,
                Translation = string.Empty
            };
        }
    }
}
