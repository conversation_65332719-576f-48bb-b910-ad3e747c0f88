using System;
using System.Collections.Generic;
using System.IO;
using System.Net.Http;
using System.Threading.Tasks;

using Newtonsoft.Json.Linq;
using Microsoft.Extensions.Configuration;

namespace MeetingUtility.Processors
{
    /// <summary>
    /// WhisperApiProcessor 用於處理音檔轉錄，使用 OpenAI Whisper API
    /// </summary>
    public class WhisperApiProcessor : WhisperProcessorBase
    {
        private readonly string _openAiApiKey;
        private readonly string _whisperApiUrl;

        // Whisper API 的每分鐘成本 (美元)
        private const double WHISPER_COST_PER_MINUTE = 0.006;

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="openAiApiKey">OpenAI API 金鑰</param>
        /// <param name="helper">MeetingHelper 實例</param>
        public WhisperApiProcessor(string openAiApiKey, MeetingHelper helper) : base(helper)
        {
            _openAiApiKey = openAiApiKey;
            _whisperApiUrl = "https://api.openai.com/v1/audio/transcriptions";
        }

        /// <summary>
        /// 建構函式 (支援設定檔)
        /// </summary>
        /// <param name="openAiApiKey">OpenAI API 金鑰</param>
        /// <param name="helper">MeetingHelper 實例</param>
        /// <param name="configuration">系統設定檔案存取介面</param>
        public WhisperApiProcessor(string openAiApiKey, MeetingHelper helper, IConfiguration configuration) : base(helper)
        {
            _openAiApiKey = openAiApiKey;
            _whisperApiUrl = configuration["WhisperApiUrl"] ?? "https://api.openai.com/v1/audio/transcriptions";
        }

        /// <summary>
        /// 分段做會議摘要，最後再合併結果
        /// </summary>
        /// <param name="responses">Whisper API 回應列表</param>
        /// <returns>摘要結果和估算成本</returns>
        protected override async Task<(string Summary, double Cost)> SummarizeAsync(List<JObject> responses)
        {
            // 呼叫 OpenAI API 進行摘要
            var (summary, cost) = await _meetingHelper.OpenAiSummarizeAsync(responses);
            return (summary, cost);
        }

        /// <summary>
        /// 進行文本翻譯
        /// </summary>
        /// <param name="inputRequest">翻譯請求，包含來源語言、目標語言和待翻譯內容</param>
        /// <returns>翻譯後的文本</returns>
        protected override async Task<string> Translate(string input)
        {
            // 呼叫 OpenAI API 進行翻譯
            (string translation, _) = await _meetingHelper.OpenAiTranslateAsync(input);
            return translation;
        }

        /// <summary>
        /// 將所有段落中回傳的逐字稿文字翻譯再合併結果
        /// </summary>
        /// <param name="responses"></param>
        /// <returns>翻譯結果和估算成本</returns>
        protected override async Task<(string Translation, double Cost)> TranslateAsync(List<JObject> responses)
        {
            // 呼叫 OpenAI API 進行翻譯
            var (translation, cost) = await _meetingHelper.OpenAiTranslateAsync(responses);
            return (translation, cost);
        }

        /// <summary>
        /// 呼叫 OpenAI Whisper API 對單一音檔區段進行轉錄，回傳結果以 JSON 形式呈現
        /// </summary>
        /// <param name="audioSegmentPath">要轉錄的音檔區段的路徑</param>
        /// <returns>轉錄結果的 JSON 物件和處理成本</returns>
        protected override async Task<(JObject Response, double Cost)> WhisperTranscribeAsync(string audioSegmentPath)
        {
            // 呼叫 OpenAI Whisper API 進行轉錄
            return await CallWhisperApiAsync(audioSegmentPath);
        }

        /// <summary>
        /// 呼叫 OpenAI Whisper API 對單一音檔區段進行轉錄，回傳結果以 JSON 形式呈現
        /// </summary>
        /// <param name="audioSegmentPath">要轉錄的音檔區段的路徑</param>
        /// <returns>轉錄結果的 JSON 物件和處理成本</returns>
        private async Task<(JObject Response, double Cost)> CallWhisperApiAsync(string audioSegmentPath)
        {
            // 計算這個區段的處理時間（秒）
            double duration = GetAudioDuration(audioSegmentPath);
            // 轉換為分鐘
            double minutes = duration / 60.0;

            // 計算成本
            double segmentCost = minutes * WHISPER_COST_PER_MINUTE;

            // 使用 HttpClient 來發送 HTTP 請求
            using var client = new HttpClient
            {
                // 設定請求的超時時間為 5 分鐘
                Timeout = TimeSpan.FromMinutes(5) // Set timeout to 5 minutes
            };

            // 使用 MultipartFormDataContent 來包裝要發送的檔案和參數
            using var form = new MultipartFormDataContent();

            // 設定授權標頭，使用 Bearer Token 進行身份驗證
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", _openAiApiKey);

            // 開啟音檔區段的檔案流
            using (var fileStream = File.OpenRead(audioSegmentPath))
            {
                // 將音檔區段加入表單內容
                form.Add(new StreamContent(fileStream), "file", Path.GetFileName(audioSegmentPath));
                // 指定使用的模型
                form.Add(new StringContent("whisper-1"), "model");
                // 指定回傳格式為 verbose_json
                form.Add(new StringContent("verbose_json"), "response_format");
                // 發送 POST 請求到 OpenAI 的轉錄 API
                var response = await client.PostAsync(_whisperApiUrl, form);
                // 確保請求成功，否則拋出例外
                response.EnsureSuccessStatusCode();
                // 讀取回應內容並轉換為字串
                string jsonResponse = await response.Content.ReadAsStringAsync();
                // 將 JSON 字串解析為 JObject 並返回，同時返回計算的成本
                return (JObject.Parse(jsonResponse), segmentCost);
            }
        }



    }
}
