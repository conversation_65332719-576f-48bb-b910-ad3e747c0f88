using System.Diagnostics;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

using Microsoft.Extensions.Configuration;

using Newtonsoft.Json.Linq;

using OpenCCNET;

namespace MeetingUtility.Processors
{
    /// <summary>
    /// WhisperOnPremisesProcessor 地端處理音檔轉錄，繼承自 WhisperProcessorBase
    /// </summary>
    public class WhisperOnPremisesProcessor : WhisperProcessorBase
    {

        /// <summary>
        /// 企業內部 Ollama 本地 AI 服務的 API 端點 URL
        /// </summary>
        /// <remarks>
        /// <para>本地部署的 Ollama 平台提供：</para>
        /// <list type="bullet">
        /// <item><description>完全離線的 AI 推理能力</description></item>
        /// <item><description>企業資料隱私保護</description></item>
        /// <item><description>無外部網路依賴的服務</description></item>
        /// <item><description>可自定義模型和參數配置</description></item>
        /// </list>
        /// <para>伺服器位置：可透過設定檔配置</para>
        /// <para>適用場景：機密會議、內部文件、敏感資料處理</para>
        /// </remarks>

        private readonly string _ollamaApiUrl;
        private readonly string _translateModel; // 使用 gemma3 模型進行翻譯
        private readonly string _summaryModel; // 使用 gemma3 模型進行摘要
        const int Too_Long_text_length = 1024; // 定義過長文本的長度限制
        private readonly string _onPremisesTranscribeUrl;

        /// <summary>
        /// 建構函式，初始化 WhisperOnPremisesProcessor 實例，並傳入會議輔助工具物件
        /// </summary>
        /// <param name="helper">會議輔助工具（MeetingHelper）實例，負責會議相關的輔助功能</param>
        public WhisperOnPremisesProcessor(MeetingHelper helper, string? ollamaApiUrl = null, string? translateModel = null, string? summaryModel = null) : base(helper)
        {
            // 如果有提供 ollamaApiUrl，則使用它
            _ollamaApiUrl = ollamaApiUrl ?? "http://************:11434/v1/chat/completions";

            // 如果有提供 translateModel，則使用它
            _translateModel = translateModel ?? "gemma3:4b";

            // 如果有提供 summaryModel，則使用它
            _summaryModel = summaryModel ?? "gemma3:4b";

            // 預設的地端轉錄 URL
            _onPremisesTranscribeUrl = "http://************:22434/transcribe";

            // 初始化簡繁轉換器
            string openccDict = Path.Combine(AppContext.BaseDirectory, "Dictionary");
            string jiebaResource = Path.Combine(AppContext.BaseDirectory, "JiebaResource");
            ZhConverter.Initialize(openccDict, jiebaResource);
        }

        /// <summary>
        /// 建構函式 (支援設定檔)，初始化 WhisperOnPremisesProcessor 實例
        /// </summary>
        /// <param name="helper">會議輔助工具（MeetingHelper）實例</param>
        /// <param name="configuration">系統設定檔案存取介面</param>
        public WhisperOnPremisesProcessor(MeetingHelper helper, IConfiguration configuration) : base(helper)
        {
            _ollamaApiUrl = configuration["OllamaApiUrl"] ?? "http://************:11434/v1/chat/completions";
            _translateModel = configuration["OnPremisesTranslationModel"] ?? "gemma3:4b";
            _summaryModel = configuration["OnPremisesSummaryModel"] ?? "gemma3:4b";
            _onPremisesTranscribeUrl = configuration["OnPremisesApiUrl"] ?? "http://************:22434/transcribe";
            // 初始化簡繁轉換器
            string openccDict = Path.Combine(AppContext.BaseDirectory, "Dictionary");
            string jiebaResource = Path.Combine(AppContext.BaseDirectory, "JiebaResource");
            ZhConverter.Initialize(openccDict, jiebaResource);
        }

        /// <summary>
        /// 分段做會議摘要，最後再合併所有段落的摘要結果
        /// </summary>
        /// <param name="responses">Whisper API 回應的 JSON 物件列表，每個物件代表一段音檔的逐字稿結果</param>
        /// <returns>回傳 (摘要內容, 預估成本)，地端處理成本固定為 0.0</returns>
        protected override async Task<(string Summary, double Cost)> SummarizeAsync(List<JObject> responses)
        {
            // 呼叫 地端 API 進行摘要
            string summary = await OnPremisesSummarizeAsync(responses);
            return (summary, 0.0);
        }

        /// <summary>
        /// 使用本地 Ollama API 對會議記錄進行分段摘要，最後合併結果
        /// </summary>
        /// <param name="responses">Whisper API 回應列表，包含會議記錄的逐字稿</param>
        /// <returns>會議摘要的繁體中文文本</returns>
        /// <remarks>
        /// <para>此方法採用階層式摘要策略，適用於長時間會議記錄的本地化處理：</para>
        /// <list type="number">
        /// <item><description>第一階段：對每個音訊段落進行初步摘要</description></item>
        /// <item><description>第二階段：將所有段落摘要合併後進行最終摘要</description></item>
        /// <item><description>第三階段：簡繁轉換和格式化處理</description></item>
        /// </list>
        /// <para>本地化優勢：</para>
        /// <list type="bullet">
        /// <item><description>完全離線運行，保護企業機密會議內容</description></item>
        /// <item><description>無外部 API 費用，適合大量會議處理</description></item>
        /// <item><description>可自定義摘要風格和專業術語處理</description></item>
        /// <item><description>不受網路連線狀況影響</description></item>
        /// </list>
        /// <para>摘要品質控制：</para>
        /// <list type="bullet">
        /// <item><description>使用 gemma3 模型，專門針對會議內容優化</description></item>
        /// <item><description>提取關鍵討論主題和重要決策</description></item>
        /// <item><description>識別行動項目和後續步驟</description></item>
        /// <item><description>過濾冗餘資訊和非關鍵對話</description></item>
        /// </list>
        /// <para>技術實現：</para>
        /// <list type="bullet">
        /// <item><description>階層式處理避免單次處理過長文本</description></item>
        /// <item><description>自動繁體中文轉換確保輸出格式一致</description></item>
        /// <item><description>錯誤容錯機制保證處理穩定性</description></item>
        /// </list>
        /// <para>注意事項：摘要品質依賴本地模型的訓練程度和會議內容的結構化程度</para>
        /// </remarks>
        public async Task<string> OnPremisesSummarizeAsync(List<JObject> responses)
        {
            StringBuilder summaryBuilder = new StringBuilder();

            foreach (var resp in responses)
            {
                string text = resp["text"]?.ToString() ?? "";
                // 產生段落摘要
                var paragraphSummary = await OnPremisesSummarizeAsync(text);
                summaryBuilder.AppendLine(paragraphSummary);
                //summaryBuilder.AppendLine(text.Trim());
            }
            //直接將所有段落的逐字稿合併為一個長文本做出摘要
            var finalSummary = await OnPremisesSummarizeAsync(summaryBuilder.ToString());

            return finalSummary;
        }

        /// <summary>
        /// 使用本地 Ollama API 生成會議摘要
        /// </summary>
        /// <param name="meetingNotes">會議記錄的文本內容</param>
        /// <returns>會議摘要的繁體中文文本</returns>
        /// <remarks>
        /// <para>此方法提供企業內部的本地化會議摘要解決方案，適用於對資料安全有嚴格要求的環境：</para>
        /// <list type="bullet">
        /// <item><description>完全離線運行：會議內容不會傳送到外部伺服器</description></item>
        /// <item><description>零 API 費用：降低長期營運成本</description></item>
        /// <item><description>自主控制：可依企業需求客製化摘要風格</description></item>
        /// <item><description>即時處理：不受網路連線狀況影響</description></item>
        /// </list>
        /// <para>摘要生成策略：</para>
        /// <list type="number">
        /// <item><description>關鍵資訊識別：自動提取主要討論議題和重點</description></item>
        /// <item><description>決策事項歸納：整理會議中的重要決定和共識</description></item>
        /// <item><description>行動項目提取：識別具體任務和責任分配</description></item>
        /// <item><description>冗餘內容過濾：移除開場白、結語等非核心內容</description></item>
        /// </list>
        /// <para>技術實現特色：</para>
        /// <list type="bullet">
        /// <item><description>模型選擇：使用經過優化的 gemma3 模型</description></item>
        /// <item><description>語言處理：專門針對繁體中文輸出進行調校</description></item>
        /// <item><description>專業術語：智能保留技術名詞的英文原文</description></item>
        /// <item><description>格式統一：確保摘要結構清晰、易於閱讀</description></item>
        /// </list>
        /// <para>品質保證機制：</para>
        /// <list type="bullet">
        /// <item><description>輸入驗證：檢查會議記錄是否過短或無法摘要</description></item>
        /// <item><description>簡繁轉換：使用 ChineseConverter 確保繁體中文輸出</description></item>
        /// <item><description>錯誤容錯：處理失敗時返回標準化錯誤訊息</description></item>
        /// <item><description>內容過濾：避免生成與摘要無關的額外內容</description></item>
        /// </list>
        /// <para>系統配置：</para>
        /// <list type="bullet">
        /// <item><description>API 端點：本地 Ollama 服務 (************:11434)</description></item>
        /// <item><description>模型溫度：0.4 (確保摘要一致性)</description></item>
        /// <item><description>請求超時：3 分鐘 (支援複雜會議內容處理)</description></item>
        /// <item><description>回退機制：短文本返回 "無法得出會議摘要"</description></item>
        /// </list>
        /// <para>適用場景：企業機密會議、內部研討會、敏感專案討論等需要資料保護的摘要需求</para>
        /// </remarks>
        public async Task<string> OnPremisesSummarizeAsync(string meetingNotes)
        {
            if (string.IsNullOrWhiteSpace(meetingNotes))
            {
                return "無法得出摘要";
            }
            const string SystemPrompt = @"
你是專業的摘要專家，請你嚴格遵守以下要求：
1.  **摘要內容要求**：
    *   **主要議題**：識別並概述討論的核心主題。
    *   **關鍵成果**：重點描述重要的決定、達成的共識或結論。
    *   **行動項目**：列出具體的行動計劃及後續步驟；若有提及，須指明負責人或團隊。

2.  **語言與格式**：
    *   **僅限繁體中文**：所有輸出內容必須使用繁體中文。
    *   **保留英文術語**：若原文中包含英文專業術語，請在摘要中保留其英文原文。
    *   **專業簡潔**：使用精確、直接的語言，避免不必要的細節，確保摘要易於理解。

3.  **嚴格排除的內容**：
    *   **禁止任何引言、客套話、結論性陳述** (例如：「本次會議摘要如下」、「會議圓滿結束」、「以上是重點整理」等)。
    *   **禁止提及「摘要」、「紀錄」等詞語本身**。
    *   **禁止任何形式的自我評論、解釋或補充說明**。你的回應**只能是摘要本身**，不應包含摘要以外的任何文字。

4.  **特殊情況處理**：
    *   如果提供的文本資訊量過少，或內容零散難以歸納出有意義的摘要，請直接回答：「無法得出摘要」。請僅在確實無法摘要時使用此回覆。

5.  **最終輸出指示**：
    *   產生的摘要必須**條理分明、切中要點**，不含任何冗餘詞彙。
    *   **請務必以 `zh-TW` (繁體中文) 回覆** 。 
    *   **Must output in zh-TW.**
";
            var requestBody = new
            {
                model = _summaryModel, // 或其他適用的 GPT 模型
                messages = new[]
                {
                    new { role = "system", content = SystemPrompt },
                    new { role = "user", content = meetingNotes }
                },
                temperature = 0.4
            };
            HttpClient httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(10) // Set timeout to 10 minutes
            };
            var requestContent = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            httpClient.DefaultRequestHeaders.Clear();

            HttpResponseMessage response = await httpClient.PostAsync(_ollamaApiUrl, requestContent);
            response.EnsureSuccessStatusCode();

            string responseContent = await response.Content.ReadAsStringAsync();

            string conclusion = "無法得出摘要";

            try
            {
                using (JsonDocument doc = JsonDocument.Parse(responseContent))
                {
                    if (doc.RootElement.GetProperty("choices").GetArrayLength() > 0)
                    {
                        var choice = doc.RootElement.GetProperty("choices")[0];
                        var element = choice.GetProperty("message").GetProperty("content");
                        string? text = element.GetString();
                        if (text != null)
                        {
                            // 移除 <think>...</think> 標籤
                            text = Regex.Replace(text, @"<think>.*?</think>", string.Empty, RegexOptions.Singleline);
                            text = text.Trim();

                            // 檢查是否含有中文字
                            if (!Regex.IsMatch(text, @"[\u4e00-\u9fa5]"))
                            {
                                // 如果沒有中文，表示為英文或其他語言
                                conclusion = await TranslateAsync(text); // 呼叫 TranslateAsync 方法進行翻譯
                            }
                            else
                            {
                                // 將簡體中文轉換為繁體中文
                                conclusion = ZhConverter.HansToTW(text, false);
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
            }

            return conclusion;
        }

        /// <summary>
        /// 進行文本翻譯
        /// </summary>
        /// <param name="inputRequest">翻譯請求，包含來源語言、目標語言和待翻譯內容</param>
        /// <returns>翻譯後的文本</returns>

        protected override async Task<string> Translate(string input)
        {   
            // 呼叫本地 Ollama API 進行翻譯
            string meetingNotes = input;
            if (string.IsNullOrWhiteSpace(meetingNotes))
            {
                return "無法取得譯文";
            }
            // 呼叫本地 Ollama API 進行翻譯
            return await TranslateAsync(meetingNotes);
        }

        /// <summary>
        /// 將所有段落的逐字稿文字進行翻譯，並合併所有翻譯結果
        /// </summary>
        /// <param name="responses">Whisper API 回應的 JSON 物件列表，每個物件代表一段音檔的逐字稿結果</param>
        /// <returns>回傳 (翻譯內容, 預估成本)，地端處理成本固定為 0.0</returns>
        protected override async Task<(string Translation, double Cost)> TranslateAsync(List<JObject> responses)
        {
            // 呼叫 地端 API 進行翻譯
            StringBuilder translationBuilder = new StringBuilder();

            foreach (var resp in responses)
            {
                // 取得該區段相對於整個音檔的起始時間偏移
                double offset = resp["offset"]?.ToObject<double>() ?? 0;
                JArray? segments = resp["segments"] as JArray;
                if (segments != null)
                {
                    foreach (var seg in segments)
                    {
                        double segStart = 0;
                        double segEnd = 0;

                        // 處理不同格式的時間戳
                        JToken? start = seg["start"];
                        JToken? end = seg["end"];
                        if (start != null && end != null)
                        {
                            // 嘗試直接解析為 double
                            if (start.Type == JTokenType.Float || start.Type == JTokenType.Integer)
                            {
                                segStart = start.ToObject<double>();
                                segEnd = end.ToObject<double>();
                            }
                            // 嘗試解析時間格式字串 (如 "00:00.930")
                            else
                            {
                                string? startTimeStr = start.ToString();
                                string? endTimeStr = end.ToString();

                                if (!string.IsNullOrEmpty(startTimeStr) && TimeSpan.TryParseExact(startTimeStr, @"mm\:ss\.fff", null, out TimeSpan startTimeSpan))
                                {
                                    segStart = startTimeSpan.TotalSeconds;
                                }

                                if (!string.IsNullOrEmpty(endTimeStr) && TimeSpan.TryParseExact(endTimeStr, @"mm\:ss\.fff", null, out TimeSpan endTimeSpan))
                                {
                                    segEnd = endTimeSpan.TotalSeconds;
                                }
                            }
                        }

                        string text = seg["text"]?.ToString().Trim() ?? "";
                        if (text.Length > Too_Long_text_length)
                        {
                            text = await TranslateLongText(text);
                        }
                        else
                        {
                            text = await TranslateAsync(text);
                        }
                        segStart += offset;
                        segEnd += offset;

                        translationBuilder.AppendLine($"[{SecondsToWebVttTime(segStart)} --> {SecondsToWebVttTime(segEnd)}]");
                        translationBuilder.AppendLine($"{text}\n");
                    }
                }
            }
            string translation = translationBuilder.ToString();
            translation = translation.Trim(); // 去除首尾空白
            return (translation, 0.0);
        }


        /// <summary>
        /// 使用本地 Ollama API 將英文文本翻譯成繁體中文
        /// </summary>
        /// <param name="meetingNotes">要翻譯的英文文本</param>
        /// <returns>翻譯後的繁體中文文本</returns>
        /// <remarks>
        /// <para>此方法提供企業內部的本地化翻譯解決方案，具備以下特色：</para>
        /// <list type="bullet">
        /// <item><description>使用本地部署的 Ollama 平台 (IP: ************:11434)</description></item>
        /// <item><description>採用 gemma3 模型進行翻譯</description></item>
        /// <item><description>完全離線運行，保護企業資料安全</description></item>
        /// <item><description>無 API 使用費用，適合大量翻譯需求</description></item>
        /// </list>
        /// <para>翻譯品質控制：</para>
        /// <list type="bullet">
        /// <item><description>專業術語保持英文原文 (如 AI model, Deep Learning, OpenAI API)</description></item>
        /// <item><description>確保翻譯自然流暢，符合繁體中文表達習慣</description></item>
        /// <item><description>保持原文的邏輯結構和專業性</description></item>
        /// <item><description>適當調整句式以符合中文語法</description></item>
        /// </list>
        /// <para>後處理流程：</para>
        /// <list type="number">
        /// <item><description>使用 ChineseConverter 將簡體中文轉換為繁體中文</description></item>
        /// <item><description>調用 SegmentChineseSentences 進行斷句格式化</description></item>
        /// <item><description>錯誤處理：翻譯失敗時返回 "無法取得譯文"</description></item>
        /// </list>
        /// <para>系統設定：</para>
        /// <list type="bullet">
        /// <item><description>HTTP 請求超時時間：3 分鐘</description></item>
        /// <item><description>翻譯溫度參數：0.2 (較低值確保翻譯一致性)</description></item>
        /// <item><description>支援長文本翻譯處理</description></item>
        /// </list>
        /// </remarks>
        public async Task<string> TranslateAsync(string meetingNotes)
        {
            const string SystemPrompt = @"You are a professional translator. 
        Please translate the following English text to Traditional Chinese (zh-TW). 
        Requirements:
        - Maintain the original meaning and tone
        - Use natural and fluent Traditional Chinese
        - Preserve any technical terms appropriately
        - Do not add explanations or comments
        - **Preserve Professional Terminology in English**:  
           - If the meeting notes contain technical terms, brand names, product names, or specialized industry jargon, retain them in their original English form.  
           - Common English words that are part of professional terminology (e.g., ""AI model"", ""Deep Learning"", ""OpenAI API"") should **not be translated** into Chinese.  
        Please translate the text provided by the user. ";

            var requestBody = new
            {
                model = _translateModel,
                messages = new[]
                {
                    new { role = "system", content = SystemPrompt },
                    new { role = "user", content = meetingNotes }
                },
                temperature = 0.2
            };

            using (HttpClient httpClient = new HttpClient())
            {
                httpClient.DefaultRequestHeaders.Clear();
                httpClient.Timeout = TimeSpan.FromMinutes(10);

                var requestContent = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");

                HttpResponseMessage response = await httpClient.PostAsync(_ollamaApiUrl, requestContent);
                response.EnsureSuccessStatusCode();

                string responseContent = await response.Content.ReadAsStringAsync();

                string translation = "無法取得譯文";

                try
                {
                    using (JsonDocument doc = JsonDocument.Parse(responseContent))
                    {
                        if (doc.RootElement.GetProperty("choices").GetArrayLength() > 0)
                        {
                            var choice = doc.RootElement.GetProperty("choices")[0];
                            var element = choice.GetProperty("message").GetProperty("content");
                            string? text = element.GetString();
                            if (text != null)
                            {
                                text = text.Trim();
                                // 將簡體中文轉換為繁體中文
                                // 使用 OpenCCNET 進行簡體到繁體的轉換
                                translation = ZhConverter.HansToTW(text, false);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"{ex.Message} {ex.StackTrace}");
                }

                translation = MeetingHelper.SegmentChineseSentences(translation);
                return translation;
            }
        }


        /// <summary>
        /// 呼叫地端 Whisper API 對單一音檔區段進行語音轉文字（逐字稿）處理，回傳 JSON 結果
        /// </summary>
        /// <param name="audioSegmentPath">要進行語音轉文字的音檔區段路徑</param>
        /// <returns>回傳 (Whisper API 回應的 JSON 物件, 預估成本)，地端處理成本固定為 0.0</returns>
        protected override async Task<(JObject Response, double Cost)> WhisperTranscribeAsync(string audioSegmentPath)
        {
            // 呼叫本地 Whisper 執行檔對單一音檔區段進行轉錄，回傳結果以 JSON 形式呈現
            //JObject response = CallWhisperExeAsync(audioSegmentPath);
            //await Task.Delay(100); // 等待 0.1 秒鐘，讓系統有時間處理
            //Console.WriteLine($"WhisperExeAsync: {response1}");
            //// 呼叫 Whisper.cpp Server Web API 進行轉錄
            /// 測試後發現 Whisper.cpp Server Web API 不穩定，不適合用於正式環境
            /// 改為自己寫 fast CGI Web API 來處理
            JObject response = await CallWhisperServerWebApiAsync(audioSegmentPath);
            //// Console.WriteLine($"WhisperServerWebApiAsync: {response}");
            // 地端成本為 0.0
            return (response, 0.0);
        }

        /// <summary>
        /// 呼叫本地 Whisper 執行檔（如 faster-whisper-xxl.exe）對單一音檔區段進行語音轉文字，回傳 JSON 結果
        /// </summary>
        /// <param name="audioSegmentPath">要進行語音轉文字的音檔區段路徑</param>
        /// <returns>Whisper 執行檔回傳的 JSON 物件（逐字稿內容）</returns>
        private JObject CallWhisperExeAsync(string audioSegmentPath)
        {

            // 檢查音檔區段是否存在，若不存在則拋出例外
            FileInfo info = new FileInfo(audioSegmentPath);
            if (!info.Exists)
            {
                Console.Error.WriteLine($"音檔區段 {audioSegmentPath} 不存在，請檢查路徑。");
                throw new FileNotFoundException($"音檔區段 {audioSegmentPath} 不存在。");
            }


            // 設定本地 Whisper 執行檔路徑與參數
            // 這裡以 faster-whisper-xxl.exe 為例，請依實際環境調整
            string executable = @"d:\AI\Faster-Whisper-XXL\faster-whisper-xxl.exe";
            // 參數說明：-pp（預處理）、-o source（輸出目錄）、--batch_recursive（遞迴處理）、--check_files（檢查檔案）、--standard、-f all（全部格式）、-m large-v3（模型）
            string args = $"{audioSegmentPath} -pp -o source --batch_recursive --check_files --standard -f all -m large-v3";


            // 建立 ProcessStartInfo 以啟動外部程式並取得標準輸出/錯誤
            ProcessStartInfo psi = new ProcessStartInfo(executable, args)
            {
                RedirectStandardOutput = true, // 取得標準輸出
                RedirectStandardError = true,  // 取得標準錯誤
                UseShellExecute = false,       // 不使用 shell
                CreateNoWindow = true          // 不顯示視窗
            };


            try
            {
                using (Process proc = new Process { StartInfo = psi })
                {
                    // 註冊標準輸出與錯誤的事件，方便除錯
                    proc.OutputDataReceived += (sender, e) => { if (e.Data != null) Console.WriteLine(e.Data); };
                    proc.ErrorDataReceived += (sender, e) => { if (e.Data != null) Console.WriteLine(e.Data); };
                    proc.Start();
                    proc.BeginOutputReadLine();  // 非同步讀取標準輸出
                    proc.BeginErrorReadLine();   // 非同步讀取標準錯誤
                    proc.WaitForExit();          // 等待外部程式結束

                    // 取得輸出檔案（假設為同目錄下的 .txt 檔）
                    string fileName = Path.GetFileNameWithoutExtension(audioSegmentPath);
                    string txtFilePath = Path.Combine(Path.GetDirectoryName(audioSegmentPath) ?? string.Empty, $"{fileName}.txt");
                    // 解析 .txt 檔案內容為 JSON 物件
                    JObject response = ParseTxtToJson(File.ReadAllText(txtFilePath));
                    return response;
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"呼叫 Whisper exe 時發生錯誤：{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 呼叫 Whisper.cpp Server Web API 對單一音檔區段進行語音轉文字，回傳 JSON 結果
        /// 注意：Whisper.cpp Server Web API 在測試時發現穩定性不足，僅供參考，正式環境建議使用自建 API
        /// </summary>
        /// <param name="audioSegmentPath">要進行語音轉文字的音檔區段路徑</param>
        /// <returns>Whisper.cpp Server Web API 回傳的 JSON 物件（逐字稿內容）</returns>
        private async Task<JObject> CallWhisperServerWebApiAsync(string audioSegmentPath)
        {

            // 檢查音檔區段是否存在，若不存在則拋出例外
            FileInfo info = new FileInfo(audioSegmentPath);
            if (!info.Exists)
            {
                Console.Error.WriteLine($"音檔區段 {audioSegmentPath} 不存在，請檢查路徑。");
                throw new FileNotFoundException($"音檔區段 {audioSegmentPath} 不存在。");
            }


            // Whisper.cpp Server Web API 的 URL，請依實際部署環境調整
            string apiUrl = _onPremisesTranscribeUrl;


            try
            {
                using (HttpClient client = new HttpClient())
                {
                    client.Timeout = TimeSpan.FromMinutes(20); // 設定請求超時為 20 分鐘
                    // 建立 MultipartFormDataContent 以便上傳檔案
                    using (MultipartFormDataContent form = new MultipartFormDataContent())
                    {
                        // 開啟音檔區段的檔案流
                        using (var fileStream = File.OpenRead(audioSegmentPath))
                        {
                            // 將音檔區段加入表單內容，欄位名稱為 "file"
                            var fileContent = new StreamContent(fileStream);
                            form.Add(fileContent, "file", Path.GetFileName(audioSegmentPath));

                            // 可選：設定其他 API 參數（如溫度、回應格式等）
                            // form.Add(new StringContent("0.0"), "temperature");
                            // form.Add(new StringContent("0.2"), "temperature_inc");
                            // form.Add(new StringContent("verbose_json"), "response_format");

                            // 發送 POST 請求到 Whisper.cpp Server Web API
                            var response = await client.PostAsync(apiUrl, form);

                            // 確認請求成功，否則拋出例外
                            response.EnsureSuccessStatusCode();

                            // 讀取回應內容（JSON 字串）
                            string jsonResponse = await response.Content.ReadAsStringAsync();

                            // 將 API 回傳的 JSON 字串解析為 JObject
                            // 注意：若 API 回傳格式與預期不同，需額外處理
                            JObject responseObj = JObject.Parse(jsonResponse);

                            // 若 API 回傳格式需轉換，可在此處理
                            return responseObj;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"呼叫 Whisper Server API 時發生錯誤：{ex.Message} {ex.StackTrace}");
                throw;
            }
        }
    }
}
