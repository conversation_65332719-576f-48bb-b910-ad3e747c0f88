using System.Diagnostics;
using System.Text;
using System.Text.RegularExpressions;

using MeetingAssistant.Models;

using MeetingUtility.Interfaces;

using Newtonsoft.Json.Linq;

namespace MeetingUtility.Processors
{
    /// <summary>
    /// Whisper 處理器抽象基類
    /// 提供共用的音檔處理、拆分和字幕生成功能
    /// </summary>
    public abstract class WhisperProcessorBase : IWhisperProcessor
    {
        protected MeetingHelper _meetingHelper;
        public int ProgressPercentage { get; set; } = 0; // 用於追蹤處理進度百分比

        /// <summary>
        /// 建構函式
        /// </summary>
        /// <param name="helper">MeetingHelper 實例</param>
        protected WhisperProcessorBase(MeetingHelper helper)
        {
            _meetingHelper = helper;
        }

        // 定義一個資料結構，記錄拆分後區段的原始起始與結束時間以及檔案路徑
        public record AudioSegment(double Start, double End, string FilePath);


        /// <summary>
        /// 呼叫 OpenAI Whisper API 對單一音檔區段進行轉錄，回傳結果以 JSON 形式呈現
        /// </summary>
        /// <param name="audioSegmentPath">要轉錄的音檔區段的路徑</param>
        /// <returns>轉錄結果的 JSON 物件和處理成本</returns>
        protected abstract Task<(JObject Response, double Cost)> WhisperTranscribeAsync(string audioSegmentPath);


        /// <summary>
        /// 分段做會議摘要，最後再合併結果
        /// </summary>
        /// <param name="responses">Whisper API 回應列表</param>
        /// <returns>摘要結果和估算成本</returns>
        protected abstract Task<(string Summary, double Cost)> SummarizeAsync(List<JObject> responses);

        /// <summary>
        /// 將所有段落中回傳的逐字稿文字翻譯再合併結果
        /// </summary>
        /// <param name="responses"></param>
        /// <returns>翻譯結果和估算成本</returns>
        protected abstract Task<(string Translation, double Cost)> TranslateAsync(List<JObject> responses);

        /// <summary>
        /// 處理上傳影片：
        /// 1. 轉換為 OGG 格式音檔（高壓縮率）
        /// 2. 利用 ffmpeg 偵測靜音區段，依停頓點拆分音檔
        /// 3. 呼叫 Whisper 取得各區段轉錄結果，並將每個結果依區段的 offset 調整後合併產生逐字稿與 SRT 字幕檔
        /// </summary>
        /// <param name="task">TranscribeTask 任務，用於追蹤處理進度</param>
        /// <param name="videoFilePath">影片檔案路徑</param>
        /// <param name="outputDirectory">輸出目錄，用於存放轉換後的音檔和字幕檔</param>
        /// <param name="silenceThresholdDb">靜音閾值Db，預設 -30 Db</param>
        /// <param name="minSilenceDuration">最小靜音持續時間（秒），預設 0.5 秒</param>
        public async Task<Transcribe> ProcessVideoFileAsync(TranscribeTask task, string videoFilePath, string outputDirectory, double silenceThresholdDb = -30.0, double minSilenceDuration = 0.5)
        {
            Transcribe result = new Transcribe();
            FileInfo fileInfo = new FileInfo(videoFilePath);
            if (!fileInfo.Exists)
            {
                Console.Error.WriteLine("檔案不存在，請檢查檔案路徑。");
                return result;
            }
            ProgressPercentage = 2; // 設定檔案上傳成功進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            // 1. 判斷檔案格式，若非 ogg 檔案，先轉成 ogg
            string audioFilePath;
            string mainFilename = Path.GetFileNameWithoutExtension(videoFilePath);
            string fileExtension = Path.GetExtension(videoFilePath).ToLowerInvariant();
            if (fileExtension == ".ogg")
            {
                // 如果已經是 ogg 格式，直接使用
                audioFilePath = videoFilePath;
                Console.WriteLine("檔案已經是 OGG 格式，跳過轉換步驟。");
            }
            else
            {
                // 非 ogg 格式，轉換為 ogg 音檔
                audioFilePath = Path.Combine(outputDirectory, $"{mainFilename}.ogg");
                ConvertVideoToAudio(videoFilePath, audioFilePath);
                if (!File.Exists(audioFilePath) || new FileInfo(audioFilePath).Length == 0)
                {
                    Console.Error.WriteLine("音檔轉換失敗，請檢查 ffmpeg 是否正確安裝。");
                    return result;
                }
            }
            ProgressPercentage = 3; // 設定音檔轉換成功進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            // 計算音檔總時間長度（秒）
            double totalDuration = GetAudioDuration(audioFilePath);
            result.Duration = totalDuration;

            // 2. 拆分原始 OGG 音檔，依照 ffmpeg 偵測的靜音區段進行拆分
            var audioSegments = SplitAudioFile(audioFilePath, outputDirectory);
            ProgressPercentage = 4; // 設定音檔轉換成功進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            // 3. 依序呼叫 Whisper API 處理每個拆分區段
            var whisperResponses = new List<JObject>();
            double totalCost = 0; // 累計 API 成本
            int numSegments = audioSegments.Count;
            int increase = 46 / numSegments; // 50-4=46
            foreach (var segment in audioSegments)
            {
                (JObject response, double cost) = await WhisperTranscribeAsync(segment.FilePath);
                // 將區段原始的起始時間加入回傳結果，方便後續調整時間戳記
                response["offset"] = segment.Start;
                whisperResponses.Add(response);
                totalCost += cost; // 累計成本
                ProgressPercentage += increase; // 設定Transcribe轉換成功進度百分比
            }
            ProgressPercentage = 50; // 設定所有區段轉錄完成進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            // 設定 API 使用成本
            result.WhisperApiCost = totalCost;

            // 4. 利用每個區段的 offset 與其回傳的時間戳記，產生 SRT 字幕檔
            string srtContent = CreateSubtitleContent(whisperResponses); // 預設為 SRT 字幕格式
            result.Srt = srtContent;
            ProgressPercentage = 74; // 設定所有字幕完成進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            // 5. 產生 WebVTT 字幕檔
            string vttSubtitle = CreateSubtitleContent(whisperResponses, true); // WEBVTT 字幕格式
            string vttContent = $"WEBVTT \n\n{vttSubtitle}"; // 在 WEBVTT 之後至少要有一個空白字元
            result.Vtt = vttContent;
            ProgressPercentage = 80; // 設定所有字幕完成進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            // 6. 合併所有段落中的逐字稿文字
            string fullTranscript = MergeTranscripts(whisperResponses);
            result.Transcript = fullTranscript;
            ProgressPercentage = 84; // 設定所有字幕完成進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            // 7. 逐段落摘要最後合併再摘要
            var (summary, summaryCost) = await SummarizeAsync(whisperResponses);
            result.Summary = summary;
            ProgressPercentage = 90; // 設定所有字幕完成進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            // 將摘要成本加到 Whisper API 成本中
            totalCost += summaryCost;

            // 8. 逐段落翻譯再合併結果
            if (MeetingHelper.ContainsChinese(fullTranscript)) // 若逐字稿中包含中文，則不進行翻譯
            {
                result.Translation = fullTranscript;
            }
            else
            {
                // 呼叫翻譯 API 並取得翻譯結果和成本
                var (translation, translationCost) = await TranslateAsync(whisperResponses);
                result.Translation = translation;

                // 將翻譯成本加到 Whisper API 成本中
                totalCost += translationCost;
            }
            ProgressPercentage = 100; // 設定所有字幕完成進度百分比
            task.ProcessPercentage = ProgressPercentage; // 更新任務進度
            return result;
        }

        /// <summary>
        /// 使用 ffmpeg 將影片轉換成 OGG 格式音檔。
        /// 此處利用 -vn 去除影片畫面，並以 libvorbis 壓縮編碼。
        /// </summary>
        protected void ConvertVideoToAudio(string videoFilePath, string audioFilePath)
        {
            // 若音檔已存在，刪除之
            if (File.Exists(audioFilePath))
            {
                File.Delete(audioFilePath);
            }
            // ogg 命令參數：-i <input> -vn -acodec libvorbis -qscale:a 5 <output>
            // wav 命令參數：-i <input> -ar 16000 -ac 1 -c:a pcm_s16le <output>
            var args = $"-i \"{videoFilePath}\" -vn -acodec libvorbis -qscale:a 5 \"{audioFilePath}\"";
            string executable = "ffmpeg";
            ProcessStartInfo psi = new ProcessStartInfo(executable, args)
            {
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (Process proc = new Process { StartInfo = psi })
            {
                proc.OutputDataReceived += (sender, e) => { if (e.Data != null) Console.WriteLine(e.Data); };
                proc.ErrorDataReceived += (sender, e) => { if (e.Data != null) Console.WriteLine(e.Data); };
                proc.Start();
                proc.BeginOutputReadLine();  // 讓系統非同步讀取標準輸出
                proc.BeginErrorReadLine();   // 讓系統非同步讀取標準錯誤
                proc.WaitForExit();  // 等待進程結束
            }
        }

        /// <summary>
        /// 使用 ffprobe 取得音檔整體播放長度（單位：秒）
        /// </summary>
        protected double GetAudioDuration(string audioFilePath)
        {
            string executable = "ffprobe";
            string args = $"-v error -show_entries format=duration -of default=nw=1:nk=1 \"{audioFilePath}\"";
            var psi = new ProcessStartInfo(executable, args)
            {
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };
            using (Process proc = new Process { StartInfo = psi })
            {
                proc.Start();
                string output = proc.StandardOutput.ReadToEnd(); // 讀取標準輸出
                string? error = proc.StandardError.ReadToEnd();   // 讀取標準錯誤
                proc.WaitForExit(); // 確保進程結束
                if (double.TryParse(output, out double duration))
                {
                    return duration;
                }
            }
            return 0;
        }

        /// <summary>
        /// 利用 ffmpeg 的 silencedetect 濾鏡偵測音檔中的靜音區段，並回傳所有靜音結束點時間（單位：秒）
        /// 此方法參考了分割音檔時以靜音作為拆分點的方法
        /// </summary>
        protected List<double> DetectSilencePositions(string audioFilePath, double silenceThresholdDb, double minSilenceDuration)
        {
            List<double> silenceEndPositions = new List<double>();
            string executable = "ffmpeg";
            string args = $"-i \"{audioFilePath}\" -af silencedetect=noise={silenceThresholdDb}dB:d={minSilenceDuration} -f null -";
            ProcessStartInfo psi = new ProcessStartInfo(executable, args)
            {
                RedirectStandardError = true,
                RedirectStandardOutput = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (Process proc = new Process { StartInfo = psi })
            {
                proc.Start();
                string stderr = proc.StandardError.ReadToEnd();
                proc.WaitForExit();
                // 使用正規表示式解析包含 "silence_end:" 的行，取得靜音結束時刻
                Regex regex = new Regex(@"silence_end:\s(?<time>[0-9\.]+)", RegexOptions.Compiled);
                foreach (Match match in regex.Matches(stderr))
                {
                    if (match.Success && double.TryParse(match.Groups["time"].Value, out double time))
                    {
                        silenceEndPositions.Add(time);
                    }
                }
            }
            return silenceEndPositions;
        }

        /// <summary>
        /// 重新設計的 SplitAudioFile 方法：
        /// 依據總長度與目標分割秒數（預設 600 秒）進行切割，
        /// 同時利用整段音檔前置偵測出的靜音點，在目標分割點附近 (預設 tolerance 30 秒) 調整切分位置，
        /// 以求得較自然且穩定可用的區段。
        /// 因為音檔較長時 Whisper API 有時會產生錯誤，因此將音檔拆分成較短的區段進行轉錄。
        /// </summary>
        /// <param name="audioFilePath">原始 ogg 音檔路徑</param>
        /// <param name="outputDirectory">輸出資料夾</param>
        /// <param name="targetSegmentDurationSec">目標分割時長（秒），預設 600 秒（10 分鐘）</param>
        /// <param name="silenceToleranceSec">分割點搜尋容忍區間（秒），預設 30 秒</param>
        /// <param name="silenceThresholdDb">偵測靜音的門檻（dB），預設 -30 dB</param>
        /// <param name="minSilenceDurationSec">偵測靜音持續最短時間（秒），預設 0.5 秒</param>
        /// <returns>回傳拆分後每個區段之起始、結束時間與檔案路徑</returns>
        protected List<AudioSegment> SplitAudioFile(string audioFilePath, string outputDirectory,
                                                 double targetSegmentDurationSec = 600,
                                                 double silenceToleranceSec = 30,
                                                 double silenceThresholdDb = -30,
                                                 double minSilenceDurationSec = 0.5)
        {
            List<AudioSegment> audioSegments = new List<AudioSegment>();

            // 取得整體音檔長度（秒）
            double totalDuration = GetAudioDuration(audioFilePath);
            if (totalDuration <= 0)
            {
                throw new Exception("無法取得音檔持續時間");
            }

            // 預先偵測全檔的靜音點，這裡取出的是靜音結束的時間點，單位：秒
            List<double> silencePositions = DetectSilencePositions(audioFilePath, silenceThresholdDb, minSilenceDurationSec);
            // 為方便後續搜尋，也可對清單排序（假設 DetectSilencePositions 未排序，這裡先排序）
            silencePositions.Sort();

            double currentStart = 0;
            int index = 0;

            while (currentStart < totalDuration)
            {
                // 預設候選結束時間為 currentStart 加上目標時長，但若超過總長度則取總長度
                double candidateEnd = Math.Min(totalDuration, currentStart + targetSegmentDurationSec);

                // 在 candidateEnd 附近（[candidateEnd - tolerance, candidateEnd + tolerance]）尋找最接近 candidateEnd 的靜音點
                // 優先選取位於 candidateEnd 之前的靜音點（避免超出目標時段）
                double adjustedEnd = candidateEnd;
                var candidates = silencePositions
                                    .Where(t => t >= candidateEnd - silenceToleranceSec && t <= candidateEnd + silenceToleranceSec)
                                    .ToList();
                if (candidates.Any())
                {
                    // 取候選中離 candidateEnd 最近的靜音點
                    adjustedEnd = candidates.OrderBy(t => Math.Abs(t - candidateEnd)).First();
                    // 若調整後的結束點小於等於 currentStart + 10 秒（避免過短的區段），則不使用調整結果
                    if (adjustedEnd - currentStart < 10)
                    {
                        adjustedEnd = candidateEnd;
                    }
                }

                // 切割出這個區段的檔案，檔名可採 segment_XXX.ogg 格式
                string outputSegmentPath = Path.Combine(outputDirectory, $"segment_{index:D3}.ogg");
                if (File.Exists(outputSegmentPath))
                {
                    File.Delete(outputSegmentPath);
                }

                // ffmpeg 指令：利用 -ss 與 -to 指定區間，並以 -c copy 避免重新編碼
                string executable = "ffmpeg";
                string args = $"-i \"{audioFilePath}\" -ss {currentStart} -to {adjustedEnd} -c copy \"{outputSegmentPath}\"";
                ProcessStartInfo psi = new ProcessStartInfo(executable, args)
                {
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    UseShellExecute = false,
                    CreateNoWindow = true
                };
                using (Process proc = new Process { StartInfo = psi })
                {
                    proc.OutputDataReceived += (sender, e) => { if (e.Data != null) Console.WriteLine(e.Data); };
                    proc.ErrorDataReceived += (sender, e) => { if (e.Data != null) Console.WriteLine(e.Data); };
                    proc.Start();
                    proc.BeginOutputReadLine();  // 讓系統非同步讀取標準輸出
                    proc.BeginErrorReadLine();   // 讓系統非同步讀取標準錯誤
                    proc.WaitForExit();  // 等待進程結束
                }

                audioSegments.Add(new AudioSegment(currentStart, adjustedEnd, outputSegmentPath));

                // 更新起始時間為這次結束（若 adjustedEnd 小於 totalDuration 則從 adjustedEnd 繼續）
                currentStart = adjustedEnd;
                index++;

                // 若剩下的時間非常短，直接合併至上一段或單獨為一段皆可，
                // 此處以剩下不足 120 秒為例，直接結束迴圈（您可依需求調整）
                // 原本設定為10秒，因為已經縮短區段為10分鐘，所以這裡改為2分鐘
                if (totalDuration - currentStart < 120)
                {
                    break;
                }
            }
            return audioSegments;
        }

        /// <summary>
        /// 將所有段落中回傳的逐字稿文字合併
        /// </summary>
        protected string MergeTranscripts(List<JObject> responses)
        {
            StringBuilder transcriptBuilder = new StringBuilder();
            foreach (var resp in responses)
            {
                // 取得該區段相對於整個音檔的起始時間偏移
                double offset = resp["offset"]?.ToObject<double>() ?? 0;
                JArray? segments = resp["segments"] as JArray;
                if (segments != null)
                {
                    foreach (var seg in segments)
                    {
                        double segStart = 0.0;
                        double segEnd = 0.0;

                        // 處理不同格式的時間戳
                        JToken? start = seg["start"];
                        JToken? end = seg["end"];
                        if (start != null && end != null)
                        {
                            // 嘗試直接解析為 double
                            if (start.Type == JTokenType.Float || start.Type == JTokenType.Integer)
                            {
                                segStart = start.ToObject<double>();
                                segEnd = end.ToObject<double>();
                            }
                            // 嘗試解析時間格式字串 (如 "00:00.930")
                            else
                            {
                                string? startTimeStr = start.ToString();
                                string? endTimeStr = end.ToString();

                                if (!string.IsNullOrEmpty(startTimeStr) && TimeSpan.TryParseExact(startTimeStr, @"mm\:ss\.fff", null, out TimeSpan startTimeSpan))
                                {
                                    segStart = startTimeSpan.TotalSeconds;
                                }

                                if (!string.IsNullOrEmpty(endTimeStr) && TimeSpan.TryParseExact(endTimeStr, @"mm\:ss\.fff", null, out TimeSpan endTimeSpan))
                                {
                                    segEnd = endTimeSpan.TotalSeconds;
                                }
                            }
                        }

                        string text = seg["text"]?.ToString().Trim() ?? "";
                        // 將時間戳記加上偏移量
                        segStart += offset;
                        segEnd += offset;
                        transcriptBuilder.AppendLine($"[{SecondsToWebVttTime(segStart)} --> {SecondsToWebVttTime(segEnd)}]");
                        transcriptBuilder.AppendLine(text);
                        transcriptBuilder.AppendLine();
                        //string trimmedText = text.Trim();
                        //char[] endChars = { '.', '?', '!', '。', '？', '！' };
                        //if (trimmedText.Length > 0 && endChars.Contains(trimmedText[^1]))
                        //{
                        //    transcriptBuilder.AppendLine();
                        //}
                    }
                }
            }
            return transcriptBuilder.ToString();
        }

        /// <summary>
        /// 根據 Whisper 回傳的每段轉錄結果，並依據先前拆分時加入的 offset，組合成 SRT 字幕檔
        /// </summary>
        protected string CreateSubtitleContent(List<JObject> responses, bool isVtt = false)
        {
            int subtitleIndex = 1;
            StringBuilder subtitleBuilder = new StringBuilder();

            foreach (var resp in responses)
            {
                // 取得該區段相對於整個音檔的起始時間偏移
                double offset = resp["offset"]?.ToObject<double>() ?? 0;
                JArray? segments = resp["segments"] as JArray;
                if (segments != null)
                {
                    foreach (var seg in segments)
                    {
                        double segStart = 0.0;
                        double segEnd = 0.0;

                        // 處理不同格式的時間戳
                        JToken? start = seg["start"];
                        JToken? end = seg["end"];
                        if (start != null && end != null)
                        {
                            // 嘗試直接解析為 double
                            if (start.Type == JTokenType.Float || start.Type == JTokenType.Integer)
                            {
                                segStart = start.ToObject<double>();
                                segEnd = end.ToObject<double>();
                            }
                            // 嘗試解析時間格式字串 (如 "00:00.930")
                            else
                            {
                                string? startTimeStr = start.ToString();
                                string? endTimeStr = end.ToString();

                                if (!string.IsNullOrEmpty(startTimeStr) && TimeSpan.TryParseExact(startTimeStr, @"mm\:ss\.fff", null, out TimeSpan startTimeSpan))
                                {
                                    segStart = startTimeSpan.TotalSeconds;
                                }

                                if (!string.IsNullOrEmpty(endTimeStr) && TimeSpan.TryParseExact(endTimeStr, @"mm\:ss\.fff", null, out TimeSpan endTimeSpan))
                                {
                                    segEnd = endTimeSpan.TotalSeconds;
                                }
                            }
                        }

                        string text = seg["text"]?.ToString().Trim() ?? "";

                        segStart += offset;
                        segEnd += offset;

                        subtitleBuilder.AppendLine(subtitleIndex.ToString());
                        if (isVtt)
                        {
                            subtitleBuilder.AppendLine($"{SecondsToWebVttTime(segStart)} --> {SecondsToWebVttTime(segEnd)}");
                        }
                        else
                        {
                            subtitleBuilder.AppendLine($"{SecondsToSrtTime(segStart)} --> {SecondsToSrtTime(segEnd)}");
                        }
                        subtitleBuilder.AppendLine(text);
                        subtitleBuilder.AppendLine();

                        subtitleIndex++;
                    }
                }
            }
            return subtitleBuilder.ToString();
        }

        /// <summary>
        /// 將秒數轉換成 SRT 字幕格式時間 (hh:mm:ss,ms)
        /// </summary>
        protected string SecondsToSrtTime(double seconds)
        {
            TimeSpan t = TimeSpan.FromSeconds(seconds);
            return string.Format("{0:D2}:{1:D2}:{2:D2},{3:D3}", t.Hours, t.Minutes, t.Seconds, t.Milliseconds);
        }

        /// <summary>
        /// 將秒數轉換成 WEBVTT 字幕格式時間 (hh:mm:ss,ms)
        /// </summary>
        protected string SecondsToWebVttTime(double seconds)
        {
            TimeSpan t = TimeSpan.FromSeconds(seconds);
            return string.Format("{0:D2}:{1:D2}:{2:D2}.{3:D3}", t.Hours, t.Minutes, t.Seconds, t.Milliseconds);
        }

        /// <summary>
        /// 將帶有時間戳的文本轉換為 JSON 格式
        /// 處理格式如：[00:00.930 --> 00:16.330] 文本內容...
        /// 將所有文本段落合併，並保留時間戳信息
        /// </summary>
        /// <param name="content">帶有時間戳的文本內容</param>
        /// <returns>包含時間戳和文本內容的 JSON 對象</returns>
        protected JObject ParseTxtToJson(string content)
        {
            // 使用正則表達式匹配時間戳和文本內容
            var regex = new Regex(@"\[(\d{2}:\d{2}\.\d{3}) --> (\d{2}:\d{2}\.\d{3})\]\s*(.+)(?:\r?\n|$)", RegexOptions.Multiline);
            var matches = regex.Matches(content);

            // 創建 JSON 結構
            var segments = new JArray();
            var fullText = new StringBuilder();

            foreach (Match match in matches)
            {
                var startTime = match.Groups[1].Value;
                var endTime = match.Groups[2].Value;
                var text = match.Groups[3].Value.Trim();

                // 將文本添加到完整文本中
                if (fullText.Length > 0)
                {
                    fullText.Append(" ");
                }
                fullText.Append(text);

                // 創建段落對象
                var segment = new JObject
                {
                    { "start", startTime },
                    { "end", endTime },
                    { "text", text }
                };

                segments.Add(segment);
            }

            // 創建最終的 JSON 對象
            var result = new JObject
            {
                { "text", fullText.ToString() },
                { "segments", segments }
            };

            return result;
        }

        /// <summary>
        /// 進行文本翻譯
        /// </summary>
        /// <param name="inputRequest">翻譯請求，包含來源語言、目標語言和待翻譯內容</param>
        /// <returns>翻譯後的文本</returns>

        protected abstract Task<string> Translate(string inputRequest);
        //{
        //    return string.Empty;
        //}


        /// <summary>
        /// 將文字切分為適當的段落
        /// </summary>
        /// <param name="text">要切分的文字</param>
        /// <returns>切分後的段落列表</returns>
        public List<string> SplitTextIntoSegments(string text)
        {
            var segments = new List<string>();
            var currentSegment = "";
            var position = 0;

            while (position < text.Length)
            {
                var remainingText = text.Substring(position);                // 如果剩餘文字少於1000字，直接加入當前段落
                if (remainingText.Length <= 1000)
                {
                    currentSegment += remainingText;
                    if (!string.IsNullOrEmpty(currentSegment))
                    {
                        segments.Add(currentSegment);
                        currentSegment = ""; // 清空 currentSegment 避免重複加入
                    }
                    break;
                }

                // 查找切分點
                var splitPoint = FindSplitPoint(remainingText);

                if (splitPoint == -1)
                {
                    // 如果找不到合適的切分點，強制在1000字處切分
                    splitPoint = Math.Min(1000, remainingText.Length);
                }

                var segmentText = remainingText.Substring(0, splitPoint);
                currentSegment += segmentText;

                // 如果當前段落大於500字，就添加到段落列表
                if (currentSegment.Length >= 500)
                {
                    segments.Add(currentSegment);
                    currentSegment = "";
                }

                position += splitPoint;
            }

            // 處理最後的段落
            if (!string.IsNullOrEmpty(currentSegment))
            {
                if (segments.Count > 0)
                {
                    // 如果最後一段太短，合併到前一段
                    segments[segments.Count - 1] += currentSegment;
                }
                else
                {
                    segments.Add(currentSegment);
                }
            }

            return segments;
        }


        /// <summary>
        /// 切分長文章並翻譯
        /// </summary>
        /// <param name="inputRequest">翻譯請求</param>
        /// <returns>翻譯結果</returns>
        public async Task<string> TranslateLongText(string input)
        {
            var segments = SplitTextIntoSegments(input);
            var translatedSegments = new List<string>();

            foreach (var segment in segments)
            {
                var translated = await Translate(segment);
                translatedSegments.Add(translated);
            }

            string combinedText = string.Join("", translatedSegments);
            return combinedText;
        }

        /// <summary>
        /// 在文字中找到合適的切分點
        /// </summary>
        /// <param name="text">要分析的文字</param>
        /// <returns>切分點位置，如果找不到則返回-1</returns>
        private int FindSplitPoint(string text)
        {
            var searchLength = Math.Min(1000, text.Length);
            var searchText = text.Substring(0, searchLength);

            // 優先選擇連續的 \n\n
            var doubleNewlineIndex = searchText.LastIndexOf("\n\n");
            if (doubleNewlineIndex > 500)
            {
                return doubleNewlineIndex + 2;
            }

            // 尋找句號後面接空白的位置
            var periodWithSpacePattern = new[] { ". ", "。 " };
            var bestPeriodIndex = -1;

            foreach (var pattern in periodWithSpacePattern)
            {
                var index = searchText.LastIndexOf(pattern);
                if (index > 500 && index > bestPeriodIndex)
                {
                    bestPeriodIndex = index + pattern.Length;
                }
            }

            if (bestPeriodIndex > -1)
            {
                return bestPeriodIndex;
            }

            // 尋找問號或驚嘆號後面接空白的位置
            var punctuationWithSpacePattern = new[] { "? ", "! ", "？ ", "！ " };
            var bestPunctuationIndex = -1;

            foreach (var pattern in punctuationWithSpacePattern)
            {
                var index = searchText.LastIndexOf(pattern);
                if (index > 500 && index > bestPunctuationIndex)
                {
                    bestPunctuationIndex = index + pattern.Length;
                }
            }

            if (bestPunctuationIndex > -1)
            {
                return bestPunctuationIndex;
            }

            return -1; // 找不到合適的切分點
        }
    }
}
