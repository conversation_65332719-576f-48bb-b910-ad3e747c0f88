using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;

using Microsoft.Extensions.Configuration;

using Newtonsoft.Json.Linq;

namespace MeetingUtility
{
    /// <summary>
    /// 會議助手核心服務類別，提供企業級會議記錄處理的完整解決方案
    /// <para>🎯 核心功能模組：</para>
    /// <list type="bullet">
    /// <item><description>📝 多語言逐字稿翻譯：英文 → 繁體中文專業翻譯</description></item>
    /// <item><description>📊 智能會議摘要：自動提取關鍵議題、決策和行動項目</description></item>
    /// <item><description>🌐 雙模式 API 支援：OpenAI 雲端服務 + Ollama 本地部署</description></item>
    /// <item><description>💰 精確成本計算：即時 API 使用費用追蹤和預算控制</description></item>
    /// <item><description>🔍 語言自動偵測：智能識別中文內容，避免重複翻譯</description></item>
    /// <item><description>📄 文本格式優化：自動斷句和可讀性提升</description></item>
    /// </list>
    /// <para>🏢 企業應用場景：</para>
    /// <list type="bullet">
    /// <item><description>跨國企業會議：英文會議記錄即時本地化</description></item>
    /// <item><description>技術研討會：保留專業術語的高品質翻譯</description></item>
    /// <item><description>管理層會議：快速生成執行摘要和行動項目</description></item>
    /// <item><description>客戶會議：專業的會議紀錄和後續追蹤</description></item>
    /// <item><description>培訓課程：教學內容的中文化處理</description></item>
    /// </list>
    /// <para>🛡️ 資料安全特色：</para>
    /// <list type="bullet">
    /// <item><description>本地模式：敏感會議內容完全離線處理</description></item>
    /// <item><description>雲端模式：採用 OpenAI 企業級安全標準</description></item>
    /// <item><description>彈性選擇：可依資料敏感度選擇處理方式</description></item>
    /// </list>
    /// <para>💡 技術優勢：</para>
    /// <list type="bullet">
    /// <item><description>階層式處理：有效處理長時間會議記錄</description></item>
    /// <item><description>成本透明：詳細的 API 使用費用計算</description></item>
    /// <item><description>品質一致：標準化的翻譯和摘要輸出格式</description></item>
    /// <item><description>錯誤容錯：完善的異常處理和回退機制</description></item>
    /// </list>
    /// </summary>
    public class MeetingHelper
    {
        /// <summary>
        /// OpenAI Chat Completions API 的完整 URL 位址
        /// </summary>
        /// <remarks>
        /// <para>OpenAI 官方 API 服務提供以下核心功能：</para>
        /// <list type="bullet">
        /// <item><description>先進的自然語言理解和生成</description></item>
        /// <item><description>高品質的文本翻譯服務</description></item>
        /// <item><description>智能會議摘要生成</description></item>
        /// <item><description>專業術語識別和保留</description></item>
        /// <item><description>多語言內容理解和轉換</description></item>
        /// </list>
        /// <para>API 特色：全球 CDN 加速、企業級可靠性、即時回應</para>
        /// </remarks>
        private readonly string _openAiApiUrl;

        /// <summary>
        /// 企業內部 Ollama 本地 AI 服務的 API 端點 URL
        /// </summary>
        /// <remarks>
        /// <para>本地部署的 Ollama 平台提供：</para>
        /// <list type="bullet">
        /// <item><description>完全離線的 AI 推理能力</description></item>
        /// <item><description>企業資料隱私保護</description></item>
        /// <item><description>無外部網路依賴的服務</description></item>
        /// <item><description>可自定義模型和參數配置</description></item>
        /// </list>
        /// <para>伺服器位置：可透過設定檔配置</para>
        /// <para>適用場景：機密會議、內部文件、敏感資料處理</para>
        /// </remarks>
        private readonly string _ollamaApiUrl;

        /// <summary>
        /// OpenAI API 服務的身份驗證金鑰
        /// </summary>
        /// <remarks>
        /// <para>用於驗證 OpenAI API 訪問權限的密鑰：</para>
        /// <list type="bullet">
        /// <item><description>透過建構函式或屬性設定器配置</description></item>
        /// <item><description>用於所有 OpenAI API 請求的身份驗證</description></item>
        /// <item><description>必須具備 Chat Completions 的使用權限</description></item>
        /// <item><description>建議定期輪換以確保安全性</description></item>
        /// </list>
        /// <para>⚠️ 安全提醒：請勿在程式碼中硬編碼 API 金鑰，建議使用環境變數或設定檔管理</para>
        /// </remarks>
        private string ApiKey { get; set; } = string.Empty;

        /// <summary>
        /// OpenAI GPT-4.1-mini 模型的輸入 Token 定價 (每 100 萬個 Token 的美元成本)
        /// </summary>
        /// <remarks>
        /// <para>💰 定價資訊 (2024年定價)：</para>
        /// <list type="bullet">
        /// <item><description>輸入成本：每 100 萬個 Token $0.40 美元</description></item>
        /// <item><description>適用模型：gpt-4.1-mini (最新的成本優化版本)</description></item>
        /// <item><description>計費單位：以 Token 為基礎，而非字符或單詞</description></item>
        /// </list>
        /// <para>📊 成本估算方式：</para>
        /// <list type="bullet">
        /// <item><description>英文文本：約 1.3 Token/單詞</description></item>
        /// <item><description>中文文本：約 2 字符/Token</description></item>
        /// <item><description>實際成本 = Token 數量 × (0.4 / 1,000,000)</description></item>
        /// </list>
        /// </remarks>
        private const double GPT41_MINI_INPUT_COST_PER_1K = 0.4;

        /// <summary>
        /// OpenAI GPT-4.1-mini 模型的輸出 Token 定價 (每 100 萬個 Token 的美元成本)
        /// </summary>
        /// <remarks>
        /// <para>💰 定價資訊 (2024年定價)：</para>
        /// <list type="bullet">
        /// <item><description>輸出成本：每 100 萬個 Token $1.60 美元</description></item>
        /// <item><description>輸出通常比輸入昂貴，因為需要模型生成內容</description></item>
        /// <item><description>適用於翻譯結果、摘要內容等 AI 生成文本</description></item>
        /// </list>
        /// <para>💡 成本優化建議：</para>
        /// <list type="bullet">
        /// <item><description>合理控制摘要長度以降低輸出成本</description></item>
        /// <item><description>批次處理多個短文本比單獨處理更經濟</description></item>
        /// <item><description>對於簡單翻譯可考慮使用本地模型</description></item>
        /// </list>
        /// </remarks>
        private const double GPT41_MINI_OUTPUT_COST_PER_1K = 1.6;

        /// <summary>
        /// 英文文本 Token 數量估算係數 (每個英文單詞的平均 Token 數)
        /// </summary>
        /// <remarks>
        /// <para>📏 Token 計算基礎：</para>
        /// <list type="bullet">
        /// <item><description>經驗值：1.3 Token/單詞 (基於 OpenAI 的 Tokenizer 統計)</description></item>
        /// <item><description>變動因素：單詞長度、專業術語、標點符號</description></item>
        /// <item><description>用途：預先估算 API 成本，避免超預算</description></item>
        /// </list>
        /// <para>🔢 實際應用：</para>
        /// <list type="bullet">
        /// <item><description>短單詞 (a, is, the)：約 1 Token</description></item>
        /// <item><description>一般單詞 (meeting, discussion)：約 1-2 Token</description></item>
        /// <item><description>長單詞或專業術語：可能 2-4 Token</description></item>
        /// <item><description>平均值 1.3 提供合理的成本估算準確度</description></item>
        /// </list>
        /// <para>⚠️ 注意：此為估算值，實際 Token 數可能因文本特性而有差異</para>
        /// </remarks>
        private const double TOKENS_PER_WORD = 1.3;

        /// <summary>
        /// 預設建構函式 - 建立不含 API 金鑰的 MeetingHelper 實例
        /// </summary>
        /// <remarks>
        /// <para>🔧 使用場景：</para>
        /// <list type="bullet">
        /// <item><description>僅使用本地 Ollama API 功能</description></item>
        /// <item><description>後續透過 ApiKey 屬性設定 OpenAI 金鑰</description></item>
        /// <item><description>用於測試和開發環境</description></item>
        /// </list>
        /// <para>⚠️ 注意：使用 OpenAI 相關方法前需要設定 ApiKey 屬性</para>
        /// </remarks>
        public MeetingHelper() 
        { 
            _openAiApiUrl = "https://api.openai.com/v1/chat/completions";
            _ollamaApiUrl = "http://************:11434/v1/chat/completions";
        }

        /// <summary>
        /// 帶參數建構函式 - 建立包含 OpenAI API 金鑰的 MeetingHelper 實例
        /// </summary>
        /// <param name="apiKey">OpenAI API 身份驗證金鑰</param>
        /// <remarks>
        /// <para>🔧 推薦使用方式：</para>
        /// <list type="bullet">
        /// <item><description>生產環境中需要使用 OpenAI 服務時</description></item>
        /// <item><description>需要成本追蹤和高品質翻譯/摘要時</description></item>
        /// <item><description>企業正式部署的標準初始化方式</description></item>
        /// </list>
        /// <para>💡 最佳實踐：</para>
        /// <list type="bullet">
        /// <item><description>從環境變數或安全設定檔讀取 API 金鑰</description></item>
        /// <item><description>避免在程式碼中硬編碼敏感資訊</description></item>
        /// <item><description>定期檢查和更新 API 金鑰</description></item>
        /// </list>
        /// </remarks>
        public MeetingHelper(string apiKey) 
        { 
            ApiKey = apiKey;
            _openAiApiUrl = "https://api.openai.com/v1/chat/completions";
            _ollamaApiUrl = "http://************:11434/v1/chat/completions";
        }

        /// <summary>
        /// 完整建構函式 - 建立包含設定檔的 MeetingHelper 實例
        /// </summary>
        /// <param name="apiKey">OpenAI API 身份驗證金鑰</param>
        /// <param name="configuration">系統設定檔案存取介面</param>
        /// <remarks>
        /// <para>🔧 推薦使用方式：</para>
        /// <list type="bullet">
        /// <item><description>生產環境中的標準初始化方式</description></item>
        /// <item><description>支援從設定檔讀取 API URL 配置</description></item>
        /// <item><description>提供最大的靈活性和可配置性</description></item>
        /// </list>
        /// </remarks>
        public MeetingHelper(string apiKey, IConfiguration configuration) 
        { 
            ApiKey = apiKey;
            _openAiApiUrl = configuration["OpenAiApiUrl"] ?? "https://api.openai.com/v1/chat/completions";
            _ollamaApiUrl = configuration["OllamaApiUrl"] ?? "http://************:11434/v1/chat/completions";
        }

        /// <summary>
        /// 設定檔建構函式 - 僅使用設定檔建立 MeetingHelper 實例
        /// </summary>
        /// <param name="configuration">系統設定檔案存取介面</param>
        /// <remarks>
        /// <para>🔧 使用場景：</para>
        /// <list type="bullet">
        /// <item><description>僅使用本地 Ollama API 功能</description></item>
        /// <item><description>從設定檔讀取所有必要的 URL 配置</description></item>
        /// <item><description>後續可透過 ApiKey 屬性設定 OpenAI 金鑰</description></item>
        /// </list>
        /// </remarks>
        public MeetingHelper(IConfiguration configuration) 
        { 
            _openAiApiUrl = configuration["OpenAiApiUrl"] ?? "https://api.openai.com/v1/chat/completions";
            _ollamaApiUrl = configuration["OllamaApiUrl"] ?? "http://************:11434/v1/chat/completions";
        }

        /// <summary>
        /// 檢查輸入字串是否包含中文字符
        /// </summary>
        /// <param name="input">要檢查的輸入字串</param>
        /// <returns>如果包含中文字符返回 true，否則返回 false</returns>
        /// <remarks>
        /// <para>此方法使用正則表達式 [\u4E00-\u9FFF] 來匹配中文字符範圍，涵蓋以下內容：</para>
        /// <list type="bullet">
        /// <item><description>\u4E00-\u9FFF: 中日韓統一表意文字基本區塊</description></item>
        /// <item><description>包含繁體中文、簡體中文、日文漢字和韓文漢字</description></item>
        /// <item><description>可用於自動偵測文本語言類型</description></item>
        /// <item><description>幫助決定是否需要進行翻譯處理</description></item>
        /// </list>
        /// <para>使用場景：在處理多語言文本時，用於判斷文本是否已經是中文，避免不必要的翻譯操作</para>
        /// </remarks>
        public static bool ContainsChinese(string input)
        {
            return Regex.IsMatch(input, @"[\u4E00-\u9FFF]");
        }

        /// <summary>
        /// 將所有段落中的逐字稿文字翻譯成繁體中文並合併結果 (使用 OpenAI API)
        /// </summary>
        /// <param name="responses">包含逐字稿的 JObject 列表，每個物件應包含 "text" 屬性</param>
        /// <returns>包含翻譯結果和估算成本的元組</returns>
        /// <remarks>
        /// <para>此方法適用於處理大量音訊檔案轉錄後的分段文本翻譯：</para>
        /// <list type="number">
        /// <item><description>遍歷所有 Whisper API 回應物件</description></item>
        /// <item><description>提取每個回應中的 "text" 屬性值</description></item>
        /// <item><description>調用 OpenAiTranslateAsync 單一文本翻譯方法</description></item>
        /// <item><description>累加每次翻譯的 API 使用成本</description></item>
        /// <item><description>將所有翻譯結果以換行符連接</description></item>
        /// </list>
        /// <para>費用計算：</para>
        /// <list type="bullet">
        /// <item><description>使用 gpt-4.1-mini 模型定價</description></item>
        /// <item><description>輸入成本：每 100 萬 token $0.40</description></item>
        /// <item><description>輸出成本：每 100 萬 token $1.60</description></item>
        /// <item><description>返回總成本供後續費用分析使用</description></item>
        /// </list>
        /// <para>使用場景：長時間會議錄音分段處理、大型音訊檔案逐字稿翻譯</para>
        /// </remarks>
        public async Task<(string Translation, double Cost)> OpenAiTranslateAsync(List<JObject> responses)
        {
            StringBuilder translationBuilder = new StringBuilder();
            double totalCost = 0;

            foreach (var resp in responses)
            {
                string text = resp["text"]?.ToString() ?? "";
                // 產生譯文
                var (translation, cost) = await OpenAiTranslateAsync(text);
                translationBuilder.AppendLine(translation);
                totalCost += cost;
            }
            return (translationBuilder.ToString(), totalCost);
        }

        /// <summary>
        /// 對中文文本進行斷句處理，在句號、問號或感嘆號後添加換行符
        /// </summary>
        /// <param name="text">要處理的中文文本</param>
        /// <returns>斷句後的文本，每個句子後有換行符</returns>
        /// <remarks>
        /// <para>此方法專門用於改善中文文本的可讀性和格式化：</para>
        /// <list type="bullet">
        /// <item><description>識別中文句子結尾標點：句號(。)、問號(？)、感嘆號(！)</description></item>
        /// <item><description>在句子結尾後自動添加換行符，提升閱讀體驗</description></item>
        /// <item><description>移除標點符號後的多餘空白字符</description></item>
        /// <item><description>適用於會議記錄、翻譯結果和摘要的格式化</description></item>
        /// </list>
        /// <para>正則表達式說明：</para>
        /// <list type="bullet">
        /// <item><description>模式：([。？！])\\s* - 匹配句子結尾標點及後續空白</description></item>
        /// <item><description>替換：$1\\r\\n - 保留標點符號並添加 Windows 風格換行符</description></item>
        /// </list>
        /// <para>特殊處理：</para>
        /// <list type="bullet">
        /// <item><description>輸入為 null 或空字串時直接返回原值</description></item>
        /// <item><description>不會影響句子中間的標點符號</description></item>
        /// <item><description>支援混合中英文本的處理</description></item>
        /// </list>
        /// </remarks>
        public static string SegmentChineseSentences(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                return text;
            }

            // 使用正則表達式查找句子結尾的標點符號並添加換行符
            string pattern = @"([。？！])\s*";
            string replacement = "$1\r\n";

            return Regex.Replace(text, pattern, replacement);
        }

        /// <summary>
        /// 使用 OpenAI API 將英文文本翻譯成繁體中文，並計算 API 使用成本
        /// </summary>
        /// <param name="meetingNotes">要翻譯的英文文本</param>
        /// <returns>包含翻譯結果和估算成本的元組</returns>
        /// <remarks>
        /// <para>此方法提供高品質的雲端翻譯服務，適用於對翻譯精度要求較高的場景：</para>
        /// <list type="bullet">
        /// <item><description>使用 OpenAI gpt-4.1-mini 模型，翻譯品質穩定可靠</description></item>
        /// <item><description>智能保留專業術語的英文原文</description></item>
        /// <item><description>確保翻譯結果符合繁體中文正式書面語規範</description></item>
        /// <item><description>提供精確的 API 使用成本計算</description></item>
        /// </list>
        /// <para>翻譯品質特色：</para>
        /// <list type="bullet">
        /// <item><description>專業術語處理：AI model、Deep Learning、OpenAI API 等保持英文</description></item>
        /// <item><description>自然語言轉換：避免過於直譯，確保中文表達自然</description></item>
        /// <item><description>邏輯結構保持：維持原文的邏輯脈絡和專業性</description></item>
        /// <item><description>格式一致性：保留原文的清單、段落等格式結構</description></item>
        /// </list>
        /// <para>成本計算詳細說明：</para>
        /// <list type="number">
        /// <item><description>輸入 Token 計算：英文單詞數 × 1.3 (TOKENS_PER_WORD)</description></item>
        /// <item><description>輸出 Token 計算：中文字符數 ÷ 2.0 (平均每 2 個中文字符 = 1 Token)</description></item>
        /// <item><description>輸入成本：Token 數 × $0.0000004 (每 Token 成本)</description></item>
        /// <item><description>輸出成本：Token 數 × $0.0000016 (每 Token 成本)</description></item>
        /// <item><description>總成本：輸入成本 + 輸出成本</description></item>
        /// </list>
        /// <para>系統設定與錯誤處理：</para>
        /// <list type="bullet">
        /// <item><description>HTTP 請求超時時間：3 分鐘</description></item>
        /// <item><description>翻譯溫度參數：0.5 (平衡創意性和一致性)</description></item>
        /// <item><description>失敗處理：返回 "無法取得譯文" 和 0 成本</description></item>
        /// <item><description>自動斷句：調用 SegmentChineseSentences 改善可讀性</description></item>
        /// </list>
        /// </remarks>
        public async Task<(string Translation, double Cost)> OpenAiTranslateAsync(string meetingNotes)
        {
            const string SystemPrompt = @"You are a highly skilled AI translator specializing in translating meeting records into Traditional Chinese (繁體中文). Your task is to translate the user's provided meeting notes while adhering to the following guidelines:  

1. **Preserve Professional Terminology in English**:  
   - If the meeting notes contain technical terms, brand names, product names, or specialized industry jargon, retain them in their original English form.  
   - Common English words that are part of professional terminology (e.g., ""AI model,"" ""Deep Learning,"" ""OpenAI API"") should **not be translated** into Chinese.  

2. **Ensure Natural and Fluent Translation**:  
   - The translation should be **clear, professional, and natural**, following the conventions of formal written Traditional Chinese.  
   - Avoid overly literal translations that sound unnatural in Chinese.  

3. **Maintain Logical Flow and Readability**:  
   - Ensure that sentence structures make sense in Chinese while keeping the original meaning intact.  
   - Adapt phrasing if necessary to improve clarity and coherence.  

4. **Keep Formatting and Structure Consistent**:  
   - Retain lists, bullet points, or section headings as they appear in the original text.  
   - If a sentence contains a mix of English and Chinese (due to professional terminology), ensure smooth integration.  

5. **Language Requirement**:  
   - The entire output must be in Traditional Chinese (繁體中文), except for professional terminology, which remains in English.  

Your goal is to provide an **accurate, professional, and easy-to-read** Traditional Chinese translation while preserving industry-specific English terms.
";
            var requestBody = new
            {
                model = "gpt-4.1-mini", // 或其他適用的 GPT 模型
                messages = new[]
                {
                    new { role = "system", content = SystemPrompt },
                    new { role = "user", content = meetingNotes }
                },
                temperature = 0.5
            };
            HttpClient httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(5) // Set timeout to 5 minutes
            };
            var requestContent = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {ApiKey}");

            HttpResponseMessage response = await httpClient.PostAsync(_openAiApiUrl, requestContent);
            response.EnsureSuccessStatusCode();

            string responseContent = await response.Content.ReadAsStringAsync();

            string translation = "無法取得譯文";

            try
            {
                using (JsonDocument doc = JsonDocument.Parse(responseContent))
                {
                    if (doc.RootElement.GetProperty("choices").GetArrayLength() > 0)
                    {
                        var choice = doc.RootElement.GetProperty("choices")[0];
                        var element = choice.GetProperty("message").GetProperty("content");
                        string? text = element.GetString();
                        if (text != null)
                        {
                            translation = text;
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"{ex.Message} {ex.StackTrace}");
            }
            translation = SegmentChineseSentences(translation);

            // 計算成本
            // 估算輸入 token 數量（根據空格分割的單詞數量和平均 token 比率）
            int inputWords = meetingNotes.Split(new[] { ' ', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
            double inputTokens = inputWords * TOKENS_PER_WORD;

            // 估算輸出 token 數量（中文字符數除以 2，因為平均每 2 個中文字符約為 1 個 token）
            double outputTokens = translation.Length / 2.0;

            // 計算總成本
            double inputCost = inputTokens * GPT41_MINI_INPUT_COST_PER_1K / 1000000.0;
            double outputCost = outputTokens * GPT41_MINI_OUTPUT_COST_PER_1K / 1000000.0;
            double totalCost = inputCost + outputCost;

            return (translation, totalCost);
        }

        /// <summary>
        /// 使用 OpenAI API 對會議記錄進行分段摘要，最後合併結果，並計算 API 使用成本
        /// </summary>
        /// <param name="responses">Whisper API 回應列表，包含會議記錄的逐字稿</param>
        /// <returns>包含會議摘要和估算成本的元組</returns>
        /// <remarks>
        /// <para>此方法提供企業級的雲端會議摘要解決方案，具備完整的成本追蹤機制：</para>
        /// <list type="bullet">
        /// <item><description>使用 OpenAI gpt-4.1-mini 模型，確保摘要品質和一致性</description></item>
        /// <item><description>採用階層式處理策略，有效處理長時間會議記錄</description></item>
        /// <item><description>提供精確的 API 使用成本計算，便於費用管控</description></item>
        /// <item><description>智能識別重要決策、行動項目和關鍵結論</description></item>
        /// </list>
        /// <para>階層式摘要處理流程：</para>
        /// <list type="number">
        /// <item><description>段落摘要階段：對每個音訊段落進行初步摘要並記錄成本</description></item>
        /// <item><description>合併階段：將所有段落摘要組合成完整文本</description></item>
        /// <item><description>最終摘要階段：對合併文本進行最終摘要並累加成本</description></item>
        /// <item><description>成本彙總：返回總計的 API 使用費用</description></item>
        /// </list>
        /// <para>摘要品質特色：</para>
        /// <list type="bullet">
        /// <item><description>關鍵資訊提取：主要討論議題、重要決策、達成共識</description></item>
        /// <item><description>行動項目識別：具體任務分配、責任人員、完成時間</description></item>
        /// <item><description>結構化輸出：邏輯清晰、條理分明的摘要格式</description></item>
        /// <item><description>專業術語保留：保持技術名詞和專業概念的準確性</description></item>
        /// </list>
        /// <para>成本管理優勢：</para>
        /// <list type="bullet">
        /// <item><description>即時成本計算：每次調用都返回精確的費用資訊</description></item>
        /// <item><description>預算控制：可根據成本資訊進行使用量管理</description></item>
        /// <item><description>費用分析：支援不同專案或部門的成本分攤</description></item>
        /// <item><description>ROI 評估：量化摘要服務的投資回報率</description></item>
        /// </list>
        /// <para>適用場景：需要高品質摘要且要求成本透明的企業會議處理</para>
        /// </remarks>
        public async Task<(string Summary, double Cost)> OpenAiSummarizeAsync(List<JObject> responses)
        {
            StringBuilder summaryBuilder = new StringBuilder();
            double totalCost = 0;

            foreach (var resp in responses)
            {
                string text = resp["text"]?.ToString() ?? "";
                // 產生段落摘要
                var (paragraphSummary, cost) = await OpenAiSummarizeAsync(text);
                summaryBuilder.AppendLine(paragraphSummary);
                totalCost += cost;
            }

            var (finalSummary, finalCost) = await OpenAiSummarizeAsync(summaryBuilder.ToString());
            totalCost += finalCost;

            return (finalSummary, totalCost);
        }

        /// <summary>
        /// 使用 OpenAI API 生成會議摘要，並計算 API 使用成本
        /// </summary>
        /// <param name="meetingNotes">會議記錄的文本內容</param>
        /// <returns>包含會議摘要和估算成本的元組</returns>
        /// <remarks>
        /// <para>此方法為單一文本的專業會議摘要生成器，提供高品質的智能摘要服務：</para>
        /// <list type="bullet">
        /// <item><description>使用 OpenAI gpt-4.1-mini 模型，確保摘要的專業性和準確性</description></item>
        /// <item><description>智能提取會議關鍵資訊，過濾無關對話和冗餘內容</description></item>
        /// <item><description>結構化輸出格式，便於後續處理和存檔</description></item>
        /// <item><description>精確的成本計算，支援企業費用管控需求</description></item>
        /// </list>
        /// <para>摘要內容結構：</para>
        /// <list type="number">
        /// <item><description>主要討論議題：會議中的核心話題和討論重點</description></item>
        /// <item><description>重要決策事項：會議中達成的共識和決定</description></item>
        /// <item><description>行動項目清單：具體的任務分配和執行計畫</description></item>
        /// <item><description>後續步驟安排：下一步的工作規劃和時程安排</description></item>
        /// </list>
        /// <para>品質控制機制：</para>
        /// <list type="bullet">
        /// <item><description>輸入驗證：檢查文本內容是否為空或無效</description></item>
        /// <item><description>專業性維持：保留技術術語和專業概念的英文原文</description></item>
        /// <item><description>邏輯結構：確保摘要內容條理清晰、易於理解</description></item>
        /// <item><description>繁體中文輸出：符合台灣地區的語言使用習慣</description></item>
        /// </list>
        /// <para>成本計算機制：</para>
        /// <list type="bullet">
        /// <item><description>輸入成本：(會議記錄單詞數 + 系統提示詞單詞數) × 1.3 × $0.0000004</description></item>
        /// <item><description>輸出成本：摘要字符數 ÷ 2 × $0.0000016</description></item>
        /// <item><description>總成本：輸入成本 + 輸出成本</description></item>
        /// <item><description>成本追蹤：便於進行專案預算控制和 ROI 分析</description></item>
        /// </list>
        /// <para>系統參數設定：</para>
        /// <list type="bullet">
        /// <item><description>模型溫度：0.4 (平衡創意性和一致性)</description></item>
        /// <item><description>請求超時：3 分鐘 (支援長文本處理)</description></item>
        /// <item><description>錯誤處理：失敗時返回預設訊息和零成本</description></item>
        /// </list>
        /// </remarks>
        public async Task<(string Summary, double Cost)> OpenAiSummarizeAsync(string meetingNotes)
        {
            if (string.IsNullOrWhiteSpace(meetingNotes))
            {
                return ("無法取得會議摘要", 0);
            }
            const string SystemPrompt = @"You are an AI assistant specializing in summarizing meetings. Your task is to generate a **concise and well-structured** summary in Traditional Chinese (繁體中文) based on the user's provided meeting notes. Follow these guidelines:  

1. **Extract Only Key Information**:  
   - Identify and summarize the **main topics discussed**.  
   - Highlight **important decisions, agreements, and conclusions**.  
   - List **action items and next steps**, specifying responsible persons or teams if mentioned.  

2. **Eliminate Redundant or Unnecessary Text**:  
   - Do **not** include introductions, closing remarks, or filler words.  
   - **Do not** mention ""meeting summary,"" ""meeting concluded,"" or any similar phrases.  
   - Focus purely on the core content without extra commentary.  

3. **Maintain Clarity and Professionalism**:  
   - Use precise and direct language without excessive details.  
   - Ensure the summary is **logically structured** and **easy to read**.  

4. **Language Requirement**:  
   - The entire output must be in **Traditional Chinese (繁體中文)**.  
   - Keep professional terminology in English if it appears in the original notes.  

Generate a **clear, structured, and to-the-point** summary without unnecessary words.  
";
            var requestBody = new
            {
                model = "gpt-4.1-mini", // 或其他適用的 GPT 模型
                messages = new[]
                {
                    new { role = "system", content = SystemPrompt },
                    new { role = "user", content = meetingNotes }
                },
                temperature = 0.4
            };
            HttpClient httpClient = new HttpClient
            {
                Timeout = TimeSpan.FromMinutes(5) // Set timeout to 5 minutes
            };
            var requestContent = new StringContent(JsonSerializer.Serialize(requestBody), Encoding.UTF8, "application/json");
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {ApiKey}");

            HttpResponseMessage response = await httpClient.PostAsync(_openAiApiUrl, requestContent);
            response.EnsureSuccessStatusCode();

            string responseContent = await response.Content.ReadAsStringAsync();

            string conclusion = "無法取得會議結論";

            try
            {
                using (JsonDocument doc = JsonDocument.Parse(responseContent))
                {
                    if (doc.RootElement.GetProperty("choices").GetArrayLength() > 0)
                    {
                        var choice = doc.RootElement.GetProperty("choices")[0];
                        var element = choice.GetProperty("message").GetProperty("content");
                        string? text = element.GetString();
                        if (text != null)
                        {
                            conclusion = text;
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex.Message);
            }

            // 計算成本
            // 估算輸入 token 數量（根據空格分割的單詞數量和平均 token 比率）
            int inputWords = meetingNotes.Split(new[] { ' ', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
            inputWords += SystemPrompt.Split(new[] { ' ', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries).Length;
            double inputTokens = inputWords * TOKENS_PER_WORD;

            // 估算輸出 token 數量（中文字符數除以 2，因為平均每 2 個中文字符約為 1 個 token）
            double outputTokens = conclusion.Length / 2.0;

            // 計算總成本
            double inputCost = inputTokens * GPT41_MINI_INPUT_COST_PER_1K / 1000000.0;
            double outputCost = outputTokens * GPT41_MINI_OUTPUT_COST_PER_1K / 1000000.0;
            double totalCost = inputCost + outputCost;

            return (conclusion, totalCost);
        }
    }
}
