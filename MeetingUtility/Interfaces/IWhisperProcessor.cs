using MeetingAssistant.Models;

using System;
using System.Threading.Tasks;

namespace MeetingUtility.Interfaces
{
    /// <summary>
    /// Whisper 處理器介面，定義所有 Whisper 處理器必須實現的方法
    /// </summary>
    public interface IWhisperProcessor
    {
        /// <summary>
        /// 處理上傳影片：
        /// 1. 轉換為音檔格式
        /// 2. 依停頓點拆分音檔
        /// 3. 取得各區段轉錄結果，並將每個結果依區段的 offset 調整後合併產生逐字稿與字幕檔
        /// </summary>
        /// <param name="task">TranscribeTask 任務 ID</param>
        /// <param name="videoFilePath">影片檔案路徑</param>
        /// <param name="outputDirectory">輸出目錄</param>
        /// <param name="silenceThresholdDb">靜音閾值 (dB)</param>
        /// <param name="minSilenceDuration">最小靜音持續時間 (秒)</param>
        /// <returns>轉錄結果，包含逐字稿、字幕和摘要等資訊</returns>
        Task<Transcribe> ProcessVideoFileAsync(TranscribeTask task, string videoFilePath, string outputDirectory, double silenceThresholdDb = -30.0, double minSilenceDuration = 0.5);
    }
}
