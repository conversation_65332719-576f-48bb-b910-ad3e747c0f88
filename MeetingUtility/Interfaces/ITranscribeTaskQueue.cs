using MeetingAssistant.Models;

namespace MeetingUtility.Interfaces
{
    /// <summary>
    /// 轉錄任務隊列服務介面
    /// </summary>
    public interface ITranscribeTaskQueue
    {
        /// <summary>
        /// 將任務加入隊列
        /// </summary>
        /// <param name="task">要加入的轉錄任務</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>是否成功加入隊列</returns>
        Task<bool> EnqueueTaskAsync(TranscribeTask task, CancellationToken cancellationToken = default);

        /// <summary>
        /// 從隊列取出任務進行處理
        /// </summary>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>要處理的任務，若隊列為空則返回 null</returns>
        Task<TranscribeTask?> DequeueTaskAsync(CancellationToken cancellationToken = default);

        /// <summary>
        /// 獲取隊列中待處理任務數量
        /// </summary>
        /// <returns>待處理任務數量</returns>
        int GetPendingTaskCount();

        /// <summary>
        /// 獲取正在處理的任務數量
        /// </summary>
        /// <returns>正在處理的任務數量</returns>
        int GetProcessingTaskCount();


        /// <summary>
        /// 獲取 TranscribeTask 的詳細資訊
        /// </summary>
        /// <param name="taskId">TranscribeTask 的TaskId</param>
        /// <returns>TranscribeTask or null</returns>
        TranscribeTask? GetTask(Guid taskId);

        /// <summary>
        /// 獲取隊列統計資訊
        /// </summary>
        /// <returns>隊列統計資訊</returns>
        TaskQueueStatistics GetStatistics();

        /// <summary>
        /// 依優先級獲取隊列中的任務列表 (不移除任務)
        /// </summary>
        /// <param name="maxCount">最大返回數量</param>
        /// <returns>任務列表</returns>
        Task<IList<TranscribeTask>> PeekTasksAsync(int maxCount = 10);

        /// <summary>
        /// 嘗試取消指定任務
        /// </summary>
        /// <param name="taskId">任務 ID</param>
        /// <returns>是否成功取消</returns>
        Task<bool> TryCancelTaskAsync(Guid taskId);

        /// <summary>
        /// 清除所有待處理任務
        /// </summary>
        /// <returns>清除的任務數量</returns>
        Task<int> ClearPendingTasksAsync();

        /// <summary>
        /// 檢查隊列是否已滿
        /// </summary>
        /// <returns>隊列是否已滿</returns>
        bool IsQueueFull();

        /// <summary>
        /// 標記任務開始處理
        /// </summary>
        /// <param name="taskId">任務 ID</param>
        /// <returns>是否成功標記</returns>
        Task<bool> MarkTaskAsProcessingAsync(Guid taskId);

        /// <summary>
        /// 標記任務處理完成
        /// </summary>
        /// <param name="taskId">任務 ID</param>
        /// <returns>是否成功標記</returns>
        Task<bool> MarkTaskAsCompletedAsync(Guid taskId);

        /// <summary>
        /// 重新將失敗任務加入隊列 (用於重試)
        /// </summary>
        /// <param name="task">失敗的任務</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>是否成功重新加入</returns>
        Task<bool> RequeueFailedTaskAsync(TranscribeTask task, CancellationToken cancellationToken = default);
    }

    /// <summary>
    /// 任務隊列統計資訊
    /// </summary>
    public class TaskQueueStatistics
    {
        /// <summary>
        /// 待處理任務數量
        /// </summary>
        public int PendingTasks { get; set; }

        /// <summary>
        /// 正在處理任務數量
        /// </summary>
        public int ProcessingTasks { get; set; }

        /// <summary>
        /// 已完成任務數量
        /// </summary>
        public int CompletedTasks { get; set; }

        /// <summary>
        /// 失敗任務數量
        /// </summary>
        public int FailedTasks { get; set; }

        /// <summary>
        /// 隊列最大容量
        /// </summary>
        public int MaxCapacity { get; set; }

        /// <summary>
        /// 最高優先級任務的優先級值
        /// </summary>
        public int? HighestPriority { get; set; }

        /// <summary>
        /// 統計更新時間
        /// </summary>
        public DateTime LastUpdated { get; set; } = DateTime.Now;
    }
}
