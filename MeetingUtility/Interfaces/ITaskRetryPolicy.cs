using System;
using MeetingAssistant.Models;

namespace MeetingUtility.Interfaces
{
    /// <summary>
    /// 任務重試策略介面
    /// </summary>
    public interface ITaskRetryPolicy
    {
        /// <summary>
        /// 判斷任務是否應該重試
        /// </summary>
        /// <param name="task">任務資訊</param>
        /// <param name="errorMessage">錯誤訊息</param>
        /// <param name="exception">例外物件 (可選)</param>
        /// <returns>是否應該重試</returns>
        bool ShouldRetry(TranscribeTask task, string errorMessage, Exception? exception = null);

        /// <summary>
        /// 計算下次重試的延遲時間
        /// </summary>
        /// <param name="retryCount">當前重試次數</param>
        /// <param name="baseDelaySeconds">基礎延遲秒數</param>
        /// <returns>延遲時間 (秒)</returns>
        int CalculateRetryDelaySeconds(int retryCount, int baseDelaySeconds = 30);

        /// <summary>
        /// 判斷錯誤是否為暫時性錯誤 (可重試)
        /// </summary>
        /// <param name="errorMessage">錯誤訊息</param>
        /// <param name="exception">例外物件 (可選)</param>
        /// <returns>是否為暫時性錯誤</returns>
        bool IsTransientError(string errorMessage, Exception? exception = null);

        /// <summary>
        /// 取得重試策略名稱
        /// </summary>
        string PolicyName { get; }

        /// <summary>
        /// 取得預設最大重試次數
        /// </summary>
        int DefaultMaxRetryCount { get; }

        /// <summary>
        /// 取得最大重試延遲時間 (秒)
        /// </summary>
        int MaxRetryDelaySeconds { get; }

        /// <summary>
        /// 更新任務的重試資訊
        /// </summary>
        /// <param name="task">要更新的任務</param>
        /// <param name="errorMessage">錯誤訊息</param>
        /// <param name="exception">例外物件 (可選)</param>
        /// <returns>更新後的任務 (含重試計數和延遲時間)</returns>
        TranscribeTask UpdateTaskForRetry(TranscribeTask task, string errorMessage, Exception? exception = null);

        /// <summary>
        /// 檢查任務是否已達到最大重試次數
        /// </summary>
        /// <param name="task">任務資訊</param>
        /// <returns>是否已達到最大重試次數</returns>
        bool HasExceededMaxRetries(TranscribeTask task);

        /// <summary>
        /// 取得重試策略的設定資訊
        /// </summary>
        /// <returns>策略設定物件</returns>
        RetryPolicyConfiguration GetConfiguration();
    }

    /// <summary>
    /// 重試策略設定
    /// </summary>
    public class RetryPolicyConfiguration
    {
        /// <summary>
        /// 策略名稱
        /// </summary>
        public string PolicyName { get; set; } = string.Empty;

        /// <summary>
        /// 預設最大重試次數
        /// </summary>
        public int DefaultMaxRetryCount { get; set; } = 3;

        /// <summary>
        /// 基礎重試延遲 (秒)
        /// </summary>
        public int BaseRetryDelaySeconds { get; set; } = 30;

        /// <summary>
        /// 最大重試延遲 (秒)
        /// </summary>
        public int MaxRetryDelaySeconds { get; set; } = 1800; // 30 分鐘

        /// <summary>
        /// 是否使用指數退避策略
        /// </summary>
        public bool UseExponentialBackoff { get; set; } = true;

        /// <summary>
        /// 指數退避乘數
        /// </summary>
        public double ExponentialBackoffMultiplier { get; set; } = 2.0;

        /// <summary>
        /// 是否加入隨機抖動
        /// </summary>
        public bool UseJitter { get; set; } = true;

        /// <summary>
        /// 抖動範圍百分比 (0.0 - 1.0)
        /// </summary>
        public double JitterRange { get; set; } = 0.1;

        /// <summary>
        /// 暫時性錯誤關鍵字列表
        /// </summary>
        public string[] TransientErrorKeywords { get; set; } = new[]
        {
            "timeout",
            "network",
            "connection",
            "unavailable",
            "rate limit",
            "server error",
            "503",
            "502",
            "504",
            "429"
        };

        /// <summary>
        /// 永久性錯誤關鍵字列表 (不重試)
        /// </summary>
        public string[] PermanentErrorKeywords { get; set; } = new[]
        {
            "invalid file",
            "unsupported format",
            "file not found",
            "authorization",
            "authentication",
            "401",
            "403",
            "400"
        };
    }

    /// <summary>
    /// 重試決策結果
    /// </summary>
    public class RetryDecision
    {
        /// <summary>
        /// 是否應該重試
        /// </summary>
        public bool ShouldRetry { get; set; }

        /// <summary>
        /// 延遲時間 (秒)
        /// </summary>
        public int DelaySeconds { get; set; }

        /// <summary>
        /// 決策原因
        /// </summary>
        public string Reason { get; set; } = string.Empty;

        /// <summary>
        /// 建立重試決策
        /// </summary>
        /// <param name="shouldRetry">是否重試</param>
        /// <param name="delaySeconds">延遲秒數</param>
        /// <param name="reason">決策原因</param>
        /// <returns>重試決策</returns>
        public static RetryDecision Create(bool shouldRetry, int delaySeconds, string reason)
        {
            return new RetryDecision
            {
                ShouldRetry = shouldRetry,
                DelaySeconds = delaySeconds,
                Reason = reason
            };
        }
    }
}
