using System;
using System.Threading;
using System.Threading.Tasks;
using MeetingAssistant.Models;

namespace MeetingUtility.Interfaces
{
    /// <summary>
    /// 轉錄任務處理器介面
    /// </summary>
    public interface ITranscribeTaskProcessor
    {
        /// <summary>
        /// 處理轉錄任務
        /// </summary>
        /// <param name="task">要處理的任務</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>任務處理結果</returns>
        Task<TranscribeTaskResult> ProcessTaskAsync(TranscribeTask task, CancellationToken cancellationToken = default);

        /// <summary>
        /// 檢查處理器是否可用
        /// </summary>
        /// <returns>處理器是否可用</returns>
        Task<bool> IsAvailableAsync();

        /// <summary>
        /// 檢查處理器是否支援指定的檔案格式
        /// </summary>
        /// <param name="fileExtension">檔案副檔名 (不含點)</param>
        /// <returns>是否支援</returns>
        bool SupportedFileFormat(string fileExtension);

        /// <summary>
        /// 檢查檔案大小是否在限制範圍內
        /// </summary>
        /// <param name="fileSizeBytes">檔案大小 (位元組)</param>
        /// <returns>是否在限制範圍內</returns>
        bool IsFileSizeValid(long fileSizeBytes);

        /// <summary>
        /// 取得處理器名稱
        /// </summary>
        string ProcessorName { get; }

        /// <summary>
        /// 取得處理器類型 (cloud 或 local)
        /// </summary>
        string ProcessorType { get; }

        /// <summary>
        /// 取得處理器設定
        /// </summary>
        object GetConfiguration();

        /// <summary>
        /// 估算任務處理時間 (分鐘)
        /// </summary>
        /// <param name="fileSizeBytes">檔案大小 (位元組)</param>
        /// <param name="durationSeconds">音檔長度 (秒)</param>
        /// <returns>估算處理時間 (分鐘)</returns>
        int EstimateProcessingTimeMinutes(long fileSizeBytes, int? durationSeconds = null);
    }

    /// <summary>
    /// 轉錄任務處理結果
    /// </summary>
    public class TranscribeTaskResult
    {
        /// <summary>
        /// 任務 ID
        /// </summary>
        public Guid TaskId { get; set; }

        /// <summary>
        /// 處理是否成功
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// 轉錄結果文字
        /// </summary>
        public string? TranscriptionText { get; set; }

        /// <summary>
        /// 處理開始時間
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 處理結束時間
        /// </summary>
        public DateTime EndTime { get; set; }

        /// <summary>
        /// 處理耗時 (毫秒)
        /// </summary>
        public long ProcessingDurationMs => (long)(EndTime - StartTime).TotalMilliseconds;

        /// <summary>
        /// 錯誤訊息 (處理失敗時)
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// 錯誤詳細資訊 (例外堆疊追蹤)
        /// </summary>
        public string? ErrorDetails { get; set; }

        /// <summary>
        /// 處理器名稱
        /// </summary>
        public string ProcessorName { get; set; } = string.Empty;

        /// <summary>
        /// 處理器類型
        /// </summary>
        public string ProcessorType { get; set; } = string.Empty;

        /// <summary>
        /// 是否需要重試
        /// </summary>
        public bool ShouldRetry { get; set; }

        /// <summary>
        /// 重試延遲時間 (秒)
        /// </summary>
        public int RetryDelaySeconds { get; set; }

        /// <summary>
        /// 額外結果資料 (JSON 格式)
        /// </summary>
        public string? AdditionalData { get; set; }

        /// <summary>
        /// 建立成功結果
        /// </summary>
        /// <param name="taskId">任務 ID</param>
        /// <param name="transcriptionText">轉錄文字</param>
        /// <param name="processorName">處理器名稱</param>
        /// <param name="processorType">處理器類型</param>
        /// <param name="additionalData">額外資料</param>
        /// <returns>成功結果</returns>
        public static TranscribeTaskResult Success(
            Guid taskId, 
            string transcriptionText, 
            string processorName, 
            string processorType,
            string? additionalData = null)
        {
            return new TranscribeTaskResult
            {
                TaskId = taskId,
                IsSuccess = true,
                TranscriptionText = transcriptionText,
                ProcessorName = processorName,
                ProcessorType = processorType,
                EndTime = DateTime.Now,
                AdditionalData = additionalData
            };
        }

        /// <summary>
        /// 建立失敗結果
        /// </summary>
        /// <param name="taskId">任務 ID</param>
        /// <param name="errorMessage">錯誤訊息</param>
        /// <param name="processorName">處理器名稱</param>
        /// <param name="processorType">處理器類型</param>
        /// <param name="shouldRetry">是否應該重試</param>
        /// <param name="retryDelaySeconds">重試延遲秒數</param>
        /// <param name="errorDetails">錯誤詳細資訊</param>
        /// <returns>失敗結果</returns>
        public static TranscribeTaskResult Failure(
            Guid taskId, 
            string errorMessage, 
            string processorName, 
            string processorType,
            bool shouldRetry = true,
            int retryDelaySeconds = 30,
            string? errorDetails = null)
        {
            return new TranscribeTaskResult
            {
                TaskId = taskId,
                IsSuccess = false,
                ErrorMessage = errorMessage,
                ErrorDetails = errorDetails,
                ProcessorName = processorName,
                ProcessorType = processorType,
                ShouldRetry = shouldRetry,
                RetryDelaySeconds = retryDelaySeconds,
                EndTime = DateTime.Now
            };
        }
    }
}
