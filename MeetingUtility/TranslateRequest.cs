namespace MeetingUtility
{
    /// <summary>
    /// 翻譯請求的資料模型
    /// </summary>
    public class TranslateRequest
    {
        /// <summary>
        /// 來源語言代碼
        /// </summary>
        public string From { get; set; } = string.Empty;

        /// <summary>
        /// 目標語言代碼
        /// </summary>
        public string To { get; set; } = string.Empty;

        /// <summary>
        /// 要翻譯的文本內容
        /// </summary>
        public string Content { get; set; } = string.Empty;

        /// <summary>
        /// 風格
        /// </summary>
        public string Style { get; set; } = "fair";

        /// <summary>
        /// 領域
        /// </summary>
        public string Domain { get; set; } = string.Empty;

        /// <summary>
        /// 上傳的檔案名稱
        /// </summary>
        public string? Filename { get; set; } = null;
    }
}
