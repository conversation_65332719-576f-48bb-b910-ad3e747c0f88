<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>/Users/<USER>/Projects/MeetingAssistant/Architecture-UML-Analysis</title>
    <style>
        /* 提供一些適合閱讀的基本樣式 */
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 20px auto;
            padding: 0 20px;
        }
        h1, h2, h3, h4, h5, h6 {
            color: #222;
        }
        code {
            background-color: #f4f4f4;
            padding: 2px 4px;
            border-radius: 4px;
            font-family: "Courier New", Courier, monospace;
        }
        pre:not(.mermaid) { /* 讓 mermaid 的 pre 背景透明 */
            background-color: #f4f4f4;
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        /* Mermaid 圖形預設置中 */
        .mermaid {
            text-align: center;
        }
    </style>
</head>
<body>
<h1>會議助手專案架構與UML分析</h1>
<h2>專案概述</h2>
<p>會議助手是一個企業內部的會議輔助系統，主要功能包括：</p>
<ul>
<li>音視頻檔案上傳與處理</li>
<li>語音轉文字（使用 Whisper API）</li>
<li>字幕生成（SRT、WebVTT格式）</li>
<li>會議摘要生成</li>
<li>文字翻譯</li>
<li>成本追蹤與使用記錄</li>
</ul>
<h2>系統架構圖</h2>
<pre class="mermaid">graph TB
    subgraph "前端層 (Vue 3 + TypeScript)"
        A[AssistantIndex.vue<br/>主要上傳介面]
        B[ResultView.vue<br/>結果展示]
        C[HistoryList.vue<br/>歷史記錄]
        D[Toast System<br/>訊息通知]
        E[API Client<br/>API呼叫層]
    end

    subgraph "後端API層 (.NET 8.0)"
        F[WhisperController<br/>主要控制器]
        G[OnPremisesWhisperController<br/>本地Whisper控制器]
    end

    subgraph "服務層 (Services)"
        H[TranscribeTaskQueue<br/>任務隊列]
        I[TranscribeBackgroundService<br/>背景服務]
        J[TranscribeTaskProcessor<br/>任務處理器]
        K[TaskRetryPolicy<br/>重試策略]
    end

    subgraph "資料存取層"
        L[FileLogDbContext<br/>資料庫上下文]
        M[FileLog Model<br/>檔案記錄]
        N[Transcribe Model<br/>轉錄結果]
        O[UsageLog Model<br/>使用記錄]
    end

    subgraph "外部服務"
        P[OpenAI Whisper API<br/>雲端語音轉文字]
        Q[本地Whisper服務<br/>地端語音轉文字]
        R[檔案儲存系統<br/>音視頻檔案存放]
    end

    subgraph "資料庫"
        S[(SQL Server<br/>主要資料庫)]
    end

    A --> E
    B --> E
    C --> E
    E --> F
    E --> G
    F --> H
    H --> I
    I --> J
    J --> K
    F --> L
    L --> M
    L --> N
    L --> O
    L --> S
    J --> P
    J --> Q
    F --> R

    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style H fill:#fff3e0
    style L fill:#e8f5e8
    style P fill:#ffebee</pre>
<h2>類別關係圖 (Class Diagram)</h2>
<pre class="mermaid">classDiagram
    class FileLog {
        +Guid Uid
        +int Id
        +string EmpNo
        +string Name
        +int DeptNo
        +string Department
        +string IP
        +string Hostname
        +DateTime UploadTime
        +string FileName
        +long FileSize
        +double Duration
        +double WhisperApiCost
        +bool IsCompleted
        +ProcessingStatus Status
        +TimeSpan ProcessedTime
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +int RetryCount
        +string ErrorMessage
        +DateTime LastProcessedAt
        +string Remark
    }

    class Transcribe {
        +string Transcript
        +string FileName
        +string Summary
        +string Srt
        +string Vtt
        +string Translation
        +Guid Guid
        +double Duration
        +double WhisperApiCost
        +FromFileLog(FileLog) Transcribe
    }

    class TranscribeTask {
        +Guid TaskId
        +string FileName
        +string FilePath
        +string EmpNo
        +string Name
        +int DeptNo
        +string Department
        +string IP
        +string Hostname
        +string Mode
        +int Priority
        +DateTime CreatedAt
        +DateTime StartedAt
        +ProcessingStatus Status
        +int RetryCount
        +string ErrorMessage
    }

    class WhisperController {
        -IConfiguration _configuration
        -FileLogDbContext _dbContext
        -ILogger _logger
        -ITranscribeTaskQueue _taskQueue
        +LoadTranscribe(Guid) IActionResult
        +LoadHistory() IActionResult
        +Download(string) IActionResult
        +Transcribe(List~IFormFile~, string) IActionResult
        +GetTaskStatus(Guid) IActionResult
        +CancelTask(Guid) IActionResult
        -GetDataDirectory(string, string) string
        -CreateZip(string, string[]) void
    }

    class TranscribeTaskQueue {
        -SortedDictionary _priorityQueues
        -ConcurrentDictionary _processingTasks
        -ConcurrentDictionary _completedTasks
        -ConcurrentDictionary _failedTasks
        +EnqueueTaskAsync(TranscribeTask) bool
        +DequeueTaskAsync() TranscribeTask
        +GetPendingTaskCount() int
        +GetProcessingTaskCount() int
        +GetStatistics() TaskQueueStatistics
        +TryCancelTaskAsync(Guid) bool
        +MarkTaskAsCompletedAsync(Guid) bool
    }

    class TranscribeBackgroundService {
        -ITranscribeTaskQueue _taskQueue
        -ITranscribeTaskProcessor _processor
        -ILogger _logger
        +ExecuteAsync(CancellationToken) Task
        -ProcessTasksAsync(CancellationToken) Task
    }

    class TranscribeTaskProcessor {
        -IWhisperProcessor _whisperProcessor
        -FileLogDbContext _dbContext
        -ILogger _logger
        +ProcessTaskAsync(TranscribeTask) Task
        -UpdateTaskStatus(Guid, ProcessingStatus) Task
        -SaveTranscribeResult(TranscribeTask, Transcribe) Task
    }

    class FileLogDbContext {
        +DbSet~FileLog~ FileLogs
        +DbSet~UsageLog~ UsageLogs
        #OnConfiguring(DbContextOptionsBuilder) void
    }

    class ProcessingStatus {
        <<enumeration>>
        Pending
        Processing
        Completed
        Failed
        Cancelled
    }

    WhisperController --> FileLogDbContext
    WhisperController --> TranscribeTaskQueue
    TranscribeBackgroundService --> TranscribeTaskQueue
    TranscribeBackgroundService --> TranscribeTaskProcessor
    TranscribeTaskProcessor --> FileLogDbContext
    TranscribeTaskQueue --> TranscribeTask
    FileLogDbContext --> FileLog
    FileLog --> ProcessingStatus
    TranscribeTask --> ProcessingStatus
    Transcribe --> FileLog : converts from</pre>
<h2>背景任務處理流程圖</h2>
<pre class="mermaid">sequenceDiagram
    participant Client as 前端客戶端
    participant Controller as WhisperController
    participant Queue as TranscribeTaskQueue
    participant Background as TranscribeBackgroundService
    participant Processor as TranscribeTaskProcessor
    participant DB as 資料庫
    participant API as Whisper API

    Client->>Controller: 上傳檔案 (POST /Transcribe)
    Controller->>DB: 建立 FileLog 記錄
    Controller->>Queue: 加入轉錄任務到隊列
    Controller-->>Client: 返回任務ID (立即回應)

    loop 背景處理循環
        Background->>Queue: 取出待處理任務
        Queue-->>Background: 返回任務 (如果有)
        Background->>Processor: 處理任務
        Processor->>DB: 更新任務狀態為 Processing
        Processor->>API: 呼叫 Whisper API
        API-->>Processor: 返回轉錄結果
        Processor->>DB: 儲存轉錄結果
        Processor->>DB: 更新任務狀態為 Completed
        Processor->>Queue: 標記任務完成
    end

    Client->>Controller: 查詢任務狀態 (GET /GetTaskStatus)
    Controller->>DB: 查詢任務狀態
    Controller-->>Client: 返回狀態資訊

    Client->>Controller: 載入轉錄結果 (GET /LoadTranscribe)
    Controller->>DB: 查詢轉錄結果
    Controller-->>Client: 返回完整結果</pre>
<h2>檔案處理流程圖</h2>
<pre class="mermaid">flowchart TD
    A[使用者上傳檔案] --> B{檔案格式檢查}
    B -->|支援格式| C[建立 FileLog 記錄]
    B -->|不支援格式| D[返回錯誤訊息]

    C --> E[建立 TranscribeTask]
    E --> F[加入任務隊列]
    F --> G[返回任務ID給前端]

    G --> H[背景服務取出任務]
    H --> I[更新狀態為 Processing]
    I --> J{選擇處理模式}

    J -->|雲端模式| K[呼叫 OpenAI Whisper API]
    J -->|地端模式| L[呼叫本地 Whisper 服務]

    K --> M[接收轉錄結果]
    L --> M

    M --> N[計算處理成本]
    N --> O[生成字幕檔案 SRT/WebVTT]
    O --> P[呼叫 GPT API 生成摘要]
    P --> Q[儲存所有結果到檔案系統]
    Q --> R[更新資料庫記錄]
    R --> S[標記任務完成]

    S --> T[前端輪詢狀態更新]
    T --> U[顯示處理結果]

    style A fill:#e1f5fe
    style G fill:#f3e5f5
    style M fill:#fff3e0
    style U fill:#e8f5e8</pre>
<h2>資料模型關係圖</h2>
<pre class="mermaid">erDiagram
    FileLog {
        Guid Uid PK
        int Id
        string EmpNo
        string Name
        int DeptNo
        string Department
        string IP
        string Hostname
        DateTime UploadTime
        string FileName
        long FileSize
        double Duration
        double WhisperApiCost
        bool IsCompleted
        ProcessingStatus Status
        TimeSpan ProcessedTime
        DateTime CreatedAt
        DateTime UpdatedAt
        int RetryCount
        string ErrorMessage
        DateTime LastProcessedAt
        string Remark
    }

    UsageLog {
        Guid Uid PK
        string EmpNo
        string Name
        int DeptNo
        string Department
        string IP
        string Hostname
        DateTime AccessTime
        string Action
        string FileGuid
        string Remark
    }

    TranscribeTask {
        Guid TaskId PK
        string FileName
        string FilePath
        string EmpNo
        string Name
        int DeptNo
        string Department
        string IP
        string Hostname
        string Mode
        int Priority
        DateTime CreatedAt
        DateTime StartedAt
        ProcessingStatus Status
        int RetryCount
        string ErrorMessage
    }

    FileLog ||--o{ UsageLog : "tracks usage"
    FileLog ||--|| TranscribeTask : "corresponds to"</pre>
<h2>系統部署架構圖</h2>
<pre class="mermaid">graph TB
    subgraph "用戶端"
        A[Web Browser<br/>Vue 3 SPA]
    end

    subgraph "Web Server (Kestrel)"
        B[.NET 8.0 WebAPI<br/>MeetingAssistant]
        C[靜態檔案服務<br/>Vue Build Output]
    end

    subgraph "背景服務"
        D[TranscribeBackgroundService<br/>任務處理服務]
        E[TaskQueue<br/>任務隊列管理]
    end

    subgraph "檔案儲存"
        F[本地檔案系統<br/>音視頻檔案]
        G[處理結果檔案<br/>逐字稿、字幕、摘要]
    end

    subgraph "資料庫"
        H[(SQL Server<br/>檔案記錄與使用統計)]
    end

    subgraph "外部API"
        I[OpenAI Whisper API<br/>語音轉文字]
        J[OpenAI GPT API<br/>摘要生成]
        K[本地Whisper服務<br/>地端處理選項]
    end

    A --> B
    A --> C
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H
    D --> I
    D --> J
    D --> K
    D --> F
    D --> G
    D --> H

    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style D fill:#fff3e0
    style H fill:#e8f5e8
    style I fill:#ffebee</pre>
<h2>技術棧總覽</h2>
<h3>前端技術</h3>
<ul>
<li><strong>框架</strong>: Vue 3 + TypeScript</li>
<li><strong>建置工具</strong>: Vite 6</li>
<li><strong>狀態管理</strong>: Composition API</li>
<li><strong>UI組件</strong>: 自定義組件 + Toast系統</li>
<li><strong>HTTP客戶端</strong>: Fetch API</li>
</ul>
<h3>後端技術</h3>
<ul>
<li><strong>框架</strong>: .NET 8.0 WebAPI</li>
<li><strong>ORM</strong>: Entity Framework Core</li>
<li><strong>資料庫</strong>: SQL Server</li>
<li><strong>背景服務</strong>: IHostedService</li>
<li><strong>日誌</strong>: ILogger</li>
<li><strong>設定管理</strong>: IConfiguration</li>
</ul>
<h3>外部整合</h3>
<ul>
<li><strong>語音轉文字</strong>: OpenAI Whisper API / 本地Whisper服務</li>
<li><strong>文字生成</strong>: OpenAI GPT-4 API</li>
<li><strong>檔案處理</strong>: 支援多種音視頻格式</li>
<li><strong>字幕格式</strong>: SRT、WebVTT</li>
</ul>
<h3>部署與運維</h3>
<ul>
<li><strong>Web伺服器</strong>: Kestrel</li>
<li><strong>檔案上傳</strong>: 支援最大4GB檔案</li>
<li><strong>成本追蹤</strong>: API使用成本計算與記錄</li>
<li><strong>錯誤處理</strong>: 統一錯誤處理與重試機制</li>
<li><strong>監控</strong>: 任務狀態追蹤與統計</li>
</ul>
<h2>主要設計模式</h2>
<ol>
<li><strong>Repository Pattern</strong>: 透過 DbContext 進行資料存取</li>
<li><strong>Queue Pattern</strong>: 使用優先級隊列管理背景任務</li>
<li><strong>Strategy Pattern</strong>: 支援多種語音處理模式（雲端/地端）</li>
<li><strong>Observer Pattern</strong>: 任務狀態變更通知</li>
<li><strong>Retry Pattern</strong>: 失敗任務自動重試機制</li>
<li><strong>Factory Pattern</strong>: 任務建立與處理器實例化</li>
</ol>

<script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default' // 可選 'default', 'neutral', 'dark', 'forest'
        });
    </script>
</body>
</html>