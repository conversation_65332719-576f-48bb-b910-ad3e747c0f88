# 變更記錄 (CHANGELOG)

- 此處為 1.x.x 版本變更記錄

## [2.x.x 版本變更記錄](ChangeLog.md)

## [1.8.2] [2025/06/27 13:06] [修復硬編碼 URL 問題]

### 🔧 修復

- **修復所有硬編碼 URL 問題**：將所有硬編碼的 API URL 改為從 `appsettings.json` 讀取
- **MeetingHelper 類別**：
  - 新增支援 `IConfiguration` 的建構函式
  - 將 `OpenAiApiUrl` 和 `OllamaApiUrl` 改為可設定的私有欄位
  - 提供多種建構函式選項以支援不同使用情境
- **WhisperApiProcessor 類別**：
  - 新增支援設定檔的建構函式
  - 將 `WhisperApiUrl` 改為從設定檔讀取
- **WhisperOnPremisesProcessor 類別**：
  - 新增支援設定檔的建構函式
  - 將 `OllamaApiUrl`、`OnPremisesApiUrl` 等改為從設定檔讀取
  - 支援 `OnPremisesTranslationModel` 和 `OnPremisesSummaryModel` 設定
- **OnPremisesWhisper 專案**：
  - 新增支援設定檔的建構函式
  - 將硬編碼的轉錄 API URL 改為可設定

### 📝 設定檔變更

- **appsettings.json 新增設定項目**：
  - `OpenAiApiUrl`: OpenAI Chat Completions API URL
  - `OllamaApiUrl`: 企業內部 Ollama API URL
- **現有設定項目**：
  - `WhisperApiUrl`: OpenAI Whisper API URL (已存在)
  - `OnPremisesApiUrl`: 地端轉錄 API URL (已存在)
  - `OnPremisesTranslationModel`: 地端翻譯模型 (已存在)
  - `OnPremisesSummaryModel`: 地端摘要模型 (已存在)

### 💡 影響

- **Production 環境**：現在可以透過修改 `appsettings.json` 正確變更所有 API URL
- **向後相容**：所有變更都提供預設值，確保現有程式碼正常運作
- **彈性配置**：支援多種建構函式，適應不同的使用情境

### ⚠️ 注意事項

- 現有的程式碼需要更新建構函式呼叫以使用新的設定檔支援
- 建議在 Production 環境中檢查所有 API URL 設定是否正確

## [1.8.1] [2025/06/24 11:06] [優化任務監控停止條件]

### 改進

- **優化任務監控停止邏輯**：
  - 修改 `historyStore.ts` 中 `checkTaskStatuses` 方法的停止監控條件
  - 改為直接檢查 `historyList.value` 中處理中任務的數量
  - 使用 `historyList.value.filter(item => item.status === 0 || item.status === 1).length === 0` 作為停止條件
  - 移除對 `monitoringTasks.value.size` 的依賴，避免集合狀態不一致的問題
  - 提供更準確和可靠的監控停止機制

### 技術細節

- 在 `checkTaskStatuses` 方法末尾重新檢查歷史記錄
- 統一檢查所有處理中任務，而非個別檢查監控集合
- 改善日誌顯示，顯示實際處理中任務數量
- 確保監控機制與實際資料狀態保持同步

---

## [1.8.0] [2025/06/24 10:35] [修復任務監控問題]

### 修復

- **修復任務監控遺漏問題**：
  - 修正 `AssistantIndex.vue` 中 `onMounted` 生命週期的邏輯
  - 新增檢查歷史記錄中處理中任務的功能
  - 自動將狀態為 `Pending` (0) 或 `Processing` (1) 的任務加入監控清單
  - 若發現處理中任務，自動啟動 `startTaskMonitoring()` 監控機制
  - 確保頁面重新載入後，處理中的任務仍能正常接收完成通知

### 技術細節

- 在 `onMounted` 中增加處理中任務檢查邏輯
- 使用 `item.status === 0 || item.status === 1` 篩選處理中任務
- 將任務 UID 加入 `monitoringTasks` 監控集合
- 自動啟動任務監控定時器，每30秒檢查一次任務狀態
- 增加除錯日誌，記錄發現的處理中任務數量

---

## [1.7.3] [2025/06/19] [新增 WhisperOnPremisesProcessor 單元測試]

### 測試功能增強

- 2025/06/19 15:29 新增 WhisperOnPremisesProcessor 類別的完整 xUnit 單元測試 (v1.7.3)
  - **Tests/WhisperOnPremisesProcessorTests.cs**：
    - **測試架構**：
      - 使用 xUnit 測試框架，符合專案測試標準
      - 使用 FakeItEasy 進行物件模擬，替代 Moq
      - 實作 IDisposable 接口，確保測試資源正確清理
      - 遵循 AAA (Arrange-Act-Assert) 測試模式
    - **建構函式測試**：
      - `Constructor_WithValidMeetingHelper_ShouldCreateInstance`：驗證正常建構函式初始化
      - `Constructor_WithNullMeetingHelper_ShouldNotThrow`：測試 null 參數處理
    - **檔案處理測試**：
      - `ProcessVideoFileAsync_WithNonExistentFile_ShouldReturnEmptyResult`：測試不存在檔案的處理
      - `ProcessVideoFileAsync_WithValidFile_ShouldNotThrowFileNotFoundException`：測試有效檔案處理
      - `ProcessVideoFileAsync_WithEmptyFile_ShouldPassFileExistenceCheck`：測試空檔案處理
      - `ProcessVideoFileAsync_WithOggFile_ShouldSkipConversion`：測試 OGG 檔案格式處理
    - **參數驗證測試**：
      - `ProcessVideoFileAsync_WithValidOutputDirectory_ShouldAcceptParameters`：測試輸出目錄參數
      - `ProcessVideoFileAsync_WithDifferentTaskIds_ShouldAcceptAllGuids`：測試不同 Guid 參數
    - **詳細繁體中文註解**：
      - 每個測試方法都有完整的 XML 文件註解
      - 詳細說明測試目的、測試重點、預期行為
      - 包含 `<remarks>` 區段說明測試案例和限制
      - 使用 `<list>` 標籤組織測試要點

### 測試涵蓋範圍

- **基本功能測試**：
  - 建構函式初始化和參數驗證
  - 檔案存在性檢查邏輯
  - 基本的方法呼叫流程
- **邊界條件測試**：
  - 不存在檔案的處理
  - 空檔案的處理
  - 不同檔案格式的處理 (OGG、MP4 等)
- **參數驗證測試**：
  - 各種 Guid 值的接受度
  - 輸出目錄參數的處理
  - 異常情況的處理機制
- **資源管理測試**：
  - 臨時檔案的建立和清理
  - 測試後的資源釋放
  - 異常情況下的資源清理

### 測試設計特色

- **模擬物件使用**：
  - 使用 FakeItEasy 模擬 MeetingHelper 依賴
  - 避免對外部服務的實際呼叫
  - 測試隔離，確保可重複執行
- **檔案系統測試**：
  - 使用臨時檔案進行測試
  - 自動清理測試檔案，避免污染環境
  - 支援各種檔案格式的測試案例
- **例外處理驗證**：
  - 測試預期的例外情況
  - 驗證非預期例外的處理
  - 確保錯誤訊息的正確性
- **實際限制考量**：
  - 認知無法測試完整 ffmpeg 流程的限制
  - 無法測試實際 Whisper API 呼叫的限制
  - 專注於可控制範圍內的邏輯測試

### 程式碼品質提升

- **測試命名規範**：
  - 使用 `MethodName_Condition_ExpectedResult` 命名模式
  - 清楚表達測試意圖和預期結果
  - 便於測試報告的閱讀和理解
- **繁體中文文件**：
  - 完整的 XML 文件註解
  - 詳細的測試說明和使用案例
  - 符合專案的中文化文件標準
- **測試結構優化**：
  - 清晰的 Arrange-Act-Assert 結構
  - 適當的測試資料準備和清理
  - 合理的斷言和驗證邏輯

### 影響範圍

- **測試專案**：Tests/WhisperOnPremisesProcessorTests.cs (新增檔案)
- **測試涵蓋率**：增加 WhisperOnPremisesProcessor 類別的測試覆蓋
- **程式碼品質**：提升整體專案的測試完整性
- **開發體驗**：提供測試範例和參考實作

## [1.7.2] [2025-06-18] [新增移除 \<think\>...\</think\> 標籤功能]

### 功能改進

- 2025/06/18 11:54 新增移除 AI 模型生成文字中的 \<think\>...\</think\> 標籤功能 (v1.7.2)
  - **MeetingUtility/MeetingHelper.cs**：
    - **OnPremisesTranslateAsync 方法**：
      - 在簡體中文轉繁體中文轉換前，使用正則表達式移除 `<think>...</think>` 標籤
      - 支援多行模式 (RegexOptions.Singleline)，確保跨行標籤也能正確移除
      - 移除後進行 Trim() 清理多餘空白字符
    - **OnPremisesSummarizeAsync 方法**：
      - 同樣在簡體中文轉繁體中文轉換前移除 `<think>...</think>` 標籤
      - 確保摘要結果不包含 AI 模型的思考過程標記
      - 保持文字內容的乾淨和專業性

### 技術實作詳情

- **正則表達式模式**：`@"<think>.*?</think>"`
  - 使用非貪婪匹配 (`.*?`) 確保正確處理多個標籤的情況
  - 支援標籤內容包含換行符的情況
- **處理流程**：
  1. 取得 AI 模型回應文字
  2. 進行基本的 Trim() 處理
  3. 使用正則表達式移除 `<think>...</think>` 標籤
  4. 再次進行 Trim() 清理
  5. 進行簡體中文轉繁體中文轉換

### 使用者體驗改進

- **更乾淨的輸出**：AI 模型生成的翻譯和摘要結果不再包含思考過程標記
- **專業性提升**：移除內部處理標記，提供更專業的最終結果
- **內容準確性**：確保輸出內容只包含實際的翻譯或摘要文字

### 影響範圍

- **後端服務**：MeetingHelper.cs (OnPremisesTranslateAsync 和 OnPremisesSummarizeAsync 方法)
- **文字處理**：所有使用本地翻譯和摘要服務的功能
- **使用者體驗**：提供更專業和乾淨的處理結果

## [1.7.1] [2025-06-11] [新增歷史記錄刪除功能]

### 功能新增

- 2025/06/11 22:51 新增歷史記錄刪除功能，使用者可以刪除不需要的歷史記錄 (v1.7.1)
  - **後端 API 新增**：
    - **WhisperController.cs**：
      - 新增 `RemoveHistory(Guid guid)` WebAPI 方法
      - 使用 HTTP DELETE 方法，支援刪除指定的歷史記錄
      - 安全性控制：只能刪除自己的記錄，確保資料安全性
      - 完整的檔案清理：刪除資料庫記錄的同時，遞迴刪除相關的檔案目錄
      - 使用記錄追蹤：記錄刪除操作到 UsageLog 表，用於稽核
      - 錯誤處理：妥善處理檔案刪除失敗的情況，不影響資料庫記錄刪除
      - 詳細的日誌記錄：記錄刪除操作的成功和失敗情況
  - **前端 API 整合**：
    - **api/index.ts**：
      - 新增 `removeHistory(guid: string)` API 呼叫函數
      - 使用 axios.delete 方法呼叫後端 RemoveHistory API
      - 統一的錯誤處理機制，提供友善的錯誤訊息
  - **前端 UI 功能**：
    - **HistoryList.vue**：
      - 在每個歷史記錄項目的長度顯示右方新增刪除按鈕
      - 使用紅色垃圾桶圖示，hover 時有視覺回饋效果
      - 點擊刪除按鈕時顯示確認對話框，防止誤刪
      - 確認對話框包含檔案名稱和警告訊息
      - 刪除成功後發送 `history-deleted` 事件給父組件
      - 使用 @click.stop 防止事件冒泡，避免觸發歷史記錄開啟
    - **AssistantIndex.vue**：
      - 新增 `handleHistoryDeleted(guid: string)` 事件處理函數
      - 刪除成功後自動刷新歷史記錄清單
      - 使用 Toast 系統顯示刪除成功訊息
      - 智能清理：如果當前顯示的結果是被刪除的記錄，自動清除結果顯示

### 使用者體驗改進

- **直觀的操作介面**：
  - 刪除按鈕位置合理，不會干擾正常的歷史記錄瀏覽
  - 紅色圖示清楚表達刪除功能，符合使用者直覺
  - Hover 效果提供即時視覺回饋
- **安全的刪除流程**：
  - 雙重確認機制：確認對話框 + 明確的警告訊息
  - 顯示檔案名稱，讓使用者確認要刪除的項目
  - 不可復原的警告，提醒使用者謹慎操作
- **即時的狀態更新**：
  - 刪除成功後立即更新歷史記錄清單
  - Toast 訊息提供操作結果回饋
  - 智能清理當前顯示內容，避免顯示已刪除的記錄

### 技術特色

- **資料安全性**：
  - 使用者只能刪除自己的記錄，通過 EmpNo 進行權限控制
  - 完整的使用記錄追蹤，所有刪除操作都有稽核軌跡
- **檔案系統清理**：
  - 遞迴刪除整個檔案目錄，包含逐字稿、字幕、摘要等所有相關檔案
  - 錯誤處理機制，檔案刪除失敗不影響資料庫記錄清理
- **前端架構**：
  - 事件驅動的組件通訊，保持組件間的低耦合
  - 統一的 API 錯誤處理，提供一致的使用者體驗
  - 響應式設計，適配各種螢幕尺寸

### 影響範圍

- **後端 API**：WhisperController.cs (新增 RemoveHistory 方法)
- **前端 API**：api/index.ts (新增 removeHistory 函數)
- **前端組件**：HistoryList.vue (新增刪除按鈕和功能)
- **前端組件**：AssistantIndex.vue (新增刪除事件處理)
- **資料庫**：FileLogs 表 (支援記錄刪除)
- **檔案系統**：支援相關檔案目錄的完整清理

## [1.7.0] [2025-06-10] [新增專案架構與UML分析文件]

### 文件系統完善

- 2025/06/10 20:33 新增完整的專案架構與UML分析文件 (v1.7.0)
  - **Architecture-UML-Analysis.md**：
    - **系統架構圖**：使用 Mermaid 圖表展示完整的系統分層架構
      - 前端層 (Vue 3 + TypeScript)：AssistantIndex.vue、ResultView.vue、HistoryList.vue、Toast System、API Client
      - 後端API層 (.NET 8.0)：WhisperController、OnPremisesWhisperController
      - 服務層：TranscribeTaskQueue、TranscribeBackgroundService、TranscribeTaskProcessor、TaskRetryPolicy
      - 資料存取層：FileLogDbContext、FileLog Model、Transcribe Model、UsageLog Model
      - 外部服務：OpenAI Whisper API、本地Whisper服務、檔案儲存系統
      - 資料庫：SQL Server
    - **類別關係圖 (Class Diagram)**：詳細展示主要類別的屬性、方法和關係
      - FileLog、Transcribe、TranscribeTask 等資料模型
      - WhisperController、TranscribeTaskQueue 等服務類別
      - ProcessingStatus 枚舉和類別間的依賴關係
    - **背景任務處理流程圖**：使用序列圖展示完整的非同步處理流程
      - 從前端上傳檔案到背景任務完成的完整生命週期
      - 包含任務隊列、背景服務、資料庫更新等各個環節
    - **檔案處理流程圖**：展示檔案從上傳到處理完成的詳細步驟
      - 檔案格式檢查、任務建立、背景處理、結果儲存等流程
      - 支援雲端和地端兩種處理模式的分支邏輯
    - **資料模型關係圖 (ER Diagram)**：展示資料庫表格間的關係
      - FileLog、UsageLog、TranscribeTask 之間的關聯
      - 主鍵、外鍵關係的清楚標示
    - **系統部署架構圖**：展示實際部署環境的組件配置
      - Web Server (Kestrel)、背景服務、檔案儲存、資料庫、外部API
      - 各組件間的網路連接和資料流向
  - **技術棧總覽**：
    - **前端技術**：Vue 3 + TypeScript、Vite 6、Composition API、自定義組件、Fetch API
    - **後端技術**：.NET 8.0 WebAPI、Entity Framework Core、SQL Server、IHostedService、ILogger
    - **外部整合**：OpenAI Whisper API、OpenAI GPT-4 API、本地Whisper服務、多種音視頻格式支援
    - **部署與運維**：Kestrel、4GB檔案上傳支援、API成本追蹤、統一錯誤處理、任務狀態監控
  - **主要設計模式**：
    - Repository Pattern、Queue Pattern、Strategy Pattern、Observer Pattern、Retry Pattern、Factory Pattern

### 文件品質特色

- **視覺化設計**：
  - 使用 Mermaid 圖表提供清晰的視覺化架構說明
  - 包含系統架構、類別關係、流程圖、資料模型等多種圖表類型
  - 色彩編碼區分不同層級和組件類型
- **完整性**：
  - 涵蓋前端、後端、資料庫、外部服務的完整架構
  - 包含技術棧、設計模式、部署架構等全方位說明
  - 從高層架構到詳細實作的多層次描述
- **實用性**：
  - 適合新進開發人員快速了解系統架構
  - 便於系統維護和擴展時的參考
  - 支援技術決策和架構評估

### 影響範圍

- **專案文件**：新增 Architecture-UML-Analysis.md 架構分析文件
- **開發體驗**：提供完整的系統架構參考資料
- **維護性**：增強系統可理解性和可維護性

## [1.6.9] [2025-06-10] [實作摘要 Markdown 轉 HTML 顯示功能]

### 前端功能增強

- 2025-06-10 21:30 在 ResultView.vue 中實作 Markdown 轉 HTML 功能，提升摘要顯示效果 (v1.6.9)
  - **ResultView.vue**：
    - 引入 `marked` 套件進行 Markdown 轉 HTML 處理
    - 新增 `summaryHtml` computed 屬性，自動將摘要內容從 Markdown 轉換為 HTML
    - 設定 marked 選項：支援換行 (breaks: true) 和 GitHub Flavored Markdown (gfm: true)
    - 加入錯誤處理機制，轉換失敗時回退到原始文字顯示
    - 更新摘要顯示區域使用 `markdown-content` CSS 類別
  - **CSS 樣式美化**：
    - 新增完整的 Markdown 內容樣式定義
    - 支援標題 (h1-h6)、段落、清單、引用區塊等元素
    - 程式碼區塊使用深色主題，提升可讀性
    - 表格樣式美化，包含邊框和背景色
    - 連結樣式和 hover 效果
    - 強調文字 (bold/italic) 樣式
    - 分隔線樣式
  - **使用者體驗改進**：
    - 摘要內容現在支援完整的 Markdown 語法顯示
    - 標題、清單、程式碼區塊等元素都有適當的視覺層次
    - 保持與現有 UI 設計的一致性
    - 響應式設計，適配各種螢幕尺寸

### 技術特色

- **Markdown 支援**：
  - 利用現有的 `marked` 套件 (v15.0.7) 進行轉換
  - 支援 GitHub Flavored Markdown 語法
  - 自動換行處理，提升中文內容顯示效果
- **樣式設計**：
  - 使用 Tailwind CSS 色彩系統保持一致性
  - 程式碼區塊使用等寬字體和深色主題
  - 適當的間距和層次設計
- **錯誤處理**：
  - 轉換失敗時優雅降級到純文字顯示
  - 控制台錯誤記錄便於除錯

### 影響範圍

- **前端組件**：ResultView.vue (摘要顯示功能)
- **使用者體驗**：摘要內容顯示效果大幅提升
- **相容性**：向下相容，不影響現有功能

## [1.6.8] [2025-06-09] [新增 OnPremisesSummarizeAsync 單元測試]

### 測試覆蓋率提升

- 2025/06/09 16:30 為 `OnPremisesSummarizeAsync(string meetingNotes)` 方法新增完整單元測試 (v1.6.8)
  - **Tests/MeetingHelperTests.cs**：
    - 新增 `OnPremisesSummarizeAsync_WithEmptyOrWhitespaceInput_ShouldReturnDefaultMessage` 測試：驗證空字串和空白字串處理
    - 新增 `OnPremisesSummarizeAsync_WithNullInput_ShouldReturnDefaultMessage` 測試：驗證 null 輸入處理
    - 新增 `OnPremisesSummarizeAsync_WithValidMeetingNotes_ShouldReturnSummary` 測試：驗證正常會議記錄摘要功能
    - 新增 `OnPremisesSummarizeAsync_WithShortText_ShouldHandleGracefully` 測試：驗證簡短文字處理
    - 新增 `OnPremisesSummarizeAsync_WithLongText_ShouldReturnAppropriteSummary` 測試：驗證長文本摘要功能
    - 新增 `OnPremisesSummarizeAsync_WithEnglishInput_ShouldReturnChineseSummary` 測試：驗證英文轉中文摘要功能
    - 採用 xUnit 測試框架和 Theory/InlineData 進行參數化測試
    - 包含異常處理測試，優雅處理 Ollama 服務不可用的情況

### 程式碼品質改進

- **測試方法設計**：涵蓋邊界條件、正常流程和異常情況
- **錯誤處理**：妥善處理 HttpRequestException 和 TaskCanceledException
- **測試隔離**：確保測試獨立性，避免外部依賴影響測試結果
- **程式碼註解**：詳細的中文註解說明每個測試的目的和預期行為

### 影響範圍

- **測試專案**：Tests/MeetingHelperTests.cs (新增 6 個單元測試方法)
- **測試覆蓋率**：提升 OnPremisesSummarizeAsync 方法的測試覆蓋率
- **程式碼品質**：增強程式碼可靠性和維護性

## [1.6.7] [2025-06-06] [新增 Transcribe 和 TaskStatus 類型詳細註解]

### 程式碼品質改進

- 2025/06/06 20:27 為 `types/index.ts` 中的 `Transcribe` 和 `TaskStatus` 類型加入詳細繁體中文註解 (v1.6.7)
  - **types/index.ts**：
    - 為 `Transcribe` 類型及其所有屬性加入詳細註解，說明逐字稿、字幕、摘要、翻譯等內容
    - 為 `TaskStatus` 類型及其所有屬性加入詳細註解，說明任務ID、狀態、進度等資訊
    - 提升程式碼可讀性、可維護性及開發體驗

### 影響範圍

- **前端類型定義**：types/index.ts (TypeScript 類型註解)
- **開發體驗**：提升程式碼可讀性和 IDE 智能提示品質

## [1.6.6] [2025-06-06] [新增 TypeScript 類型定義詳細註解]

### 程式碼品質改進

- 2025/06/06 20:21 為 TypeScript 類型定義加入詳細繁體中文註解 (v1.6.6)
  - **types/index.ts**：
    - 為 `ProcessingStatus` 枚舉加入詳細註解，說明每個狀態的具體含義
    - 為 `FileLog` 介面的所有屬性加入詳細註解，包含資料格式說明
    - 新增檔案頂部總體說明註解，描述檔案用途
    - 改善程式碼可讀性和維護性
    - 提供完整的 API 文件化支援
  - **註解內容包含**：
    - 處理狀態的詳細說明 (等待處理、處理中、已完成、處理失敗、已取消)
    - 檔案記錄各欄位的用途和資料格式 (如 ISO 8601 時間格式、UUID 格式等)
    - 成本追蹤相關欄位的單位說明 (美元、位元組、秒)
    - 可為空欄位的明確標示
    - 舊版相容性欄位的說明

### 影響範圍

- **前端類型定義**：types/index.ts (TypeScript 類型註解)
- **開發體驗**：提升程式碼可讀性和 IDE 智能提示品質

## [1.6.6] [2025-06-06] [更新 Structure.md 檔案]

### 文件更新

- 2025/06/06 15:45 更新 Structure.md 檔案，加入背景任務相關的 .cs 檔案路徑
  - 新增 `MeetingAssistant/Services/TranscribeTaskQueue.cs`
  - 新增 `MeetingAssistant/Services/TranscribeBackgroundService.cs`
  - 新增 `MeetingAssistant/Services/TranscribeTaskProcessor.cs`
  - 新增 `MeetingAssistant/Services/TaskRetryPolicy.cs`
  - 新增 `MeetingUtility/Models/TranscribeTask.cs`
  - 新增 `MeetingUtility/Models/BackgroundTaskConfig.cs`
  - 新增 `MeetingUtility/Interfaces/ITranscribeTaskQueue.cs`
  - 新增 `MeetingUtility/Interfaces/ITranscribeTaskProcessor.cs`
  - 新增 `MeetingUtility/Interfaces/ITaskRetryPolicy.cs`

## [1.6.5] [2025-06-06] [修復前後端物件轉換與 C# 靜態成員問題]

### Bug 修復

- 2025/06/06 15:42 修復因先前修改導致的前後端問題 (v1.6.5)
  - **AssistantIndex.vue**:
    - 修復從後端 API 接收的 C# 物件轉換為 TypeScript 物件時，因屬性大小寫不同導致無法正確存取的問題。
    - 例如，後端傳回的 `TaskId` 在前端需要以正確的大小寫 `taskId` 存取。
  - **WhisperController.cs**:
    - 修復 `GetTaskProgress` 和 `GetStatusDescription` 方法未宣告為 `static` 導致的執行時錯誤。
    - 將這兩個方法修正為 `private static` 以符合其使用情境和避免非靜態成員的存取問題。

### 影響範圍

- **前端組件**: AssistantIndex.vue (任務進度顯示)
- **後端控制器**: WhisperController.cs (任務狀態和描述獲取)

## [1.6.4] [2025-06-06] [前端背景任務模式完整實作]

### 前端系統升級

- 2025/06/06 11:25 完成前端背景任務模式適配 (v1.6.4)
  - **AssistantIndex.vue 重大更新**：
    - **背景任務模式支援**：完全重構 processFile 函數以配合後端背景任務 API
    - **智能 API 格式處理**：自動識別新舊 API 回應格式，向下相容現有功能
    - **任務監控系統**：新增每 30 秒的自動任務狀態檢查機制
    - **生命週期管理**：實作 onUnmounted 清理監控，避免記憶體洩漏
    - **本地儲存優化**：使用 localStorage 記錄已通知任務，避免重複提醒
    - **錯誤處理增強**：移除 alert() 彈窗，改用 Toast 訊息系統
  - **Toast 訊息系統實作**：
    - **多類型支援**：成功 (success)、錯誤 (error)、資訊 (info) 三種類型
    - **美觀動畫效果**：滑入/滑出轉場動畫，提升視覺體驗
    - **自動消失機制**：預設 5 秒自動隱藏，可自定義持續時間
    - **手動關閉功能**：點擊關閉按鈕立即隱藏 Toast
    - **響應式設計**：在桌面版 (md:w-96) 和行動版 (w-80) 都有適當寬度
    - **視覺層級設計**：使用 z-50 確保 Toast 始終在最上層顯示
  - **API 整合改進**：
    - **新增 API 函數**：getTaskStatus 和 cancelTask 函數封裝
    - **統一錯誤處理**：axios 錯誤的統一處理和訊息顯示
    - **類型安全增強**：新增 TaskStatus 類型定義確保資料結構一致性
  - **使用者體驗優化**：
    - **即時回饋**：檔案上傳後立即顯示成功訊息
    - **自動清理**：成功上傳後自動清除檔案選擇，可立即上傳新檔案
    - **狀態同步**：任務完成時自動刷新歷史記錄
    - **智能通知**：只在任務狀態變化時通知，避免干擾用戶
  - **前端架構改進**：
    - **模組化設計**：Toast 系統獨立實作，可重複使用
    - **狀態管理**：清晰的任務監控狀態管理
    - **記憶體管理**：適當的定時器清理和事件處理
    - **向下相容**：保持與歷史記錄和結果顯示功能的完整相容性

### 技術特色

- **背景任務整合**：
  - 完全解決前端等待後端同步處理的阻塞問題
  - 實作客戶端任務狀態即時監控機制
  - 支援多任務並行處理和狀態追蹤
- **用戶體驗設計**：
  - 替代傳統 alert() 彈窗，提供現代化互動體驗
  - 智能化的狀態提醒，不干擾用戶操作流程
  - 響應式設計確保各種裝置的一致體驗
- **系統穩定性**：
  - 實作完善的錯誤處理和復原機制
  - 避免重複通知和記憶體洩漏問題
  - 提供向下相容支援，確保系統穩定運行

### 影響範圍

- **前端組件**：AssistantIndex.vue (主要上傳組件)
- **API 模組**：api/index.ts (新增任務狀態 API)
- **類型定義**：新增 TaskStatus 介面
- **使用者體驗**：完全改變檔案處理流程，從同步改為非同步

## [1.6.3] [2025-06-05] [修復 TaskRetryPolicy 隨機抖動計算問題]

### Bug 修復

- 2025/06/05 18:30 修復 TaskRetryPolicy.CalculateRetryDelaySeconds 隨機抖動計算問題 (v1.6.3)
  - **問題描述**：在使用隨機抖動 (jitter) 時，計算結果可能小於基礎延遲時間 (baseDelaySeconds)，導致測試不穩定
  - **根本原因**：隨機抖動可能產生負值，使最終延遲時間小於基礎延遲時間
  - **解決方案**：
    - 確保加入隨機抖動後的延遲時間不小於 baseDelaySeconds
    - 確保加入隨機抖動後的延遲時間不超過 MaxRetryDelaySeconds (1800秒)
    - 使用 `Math.Max(baseDelaySeconds, Math.Min(MaxRetryDelaySeconds, (int)(delaySeconds + jitter)))` 進行範圍限制
  - **影響範圍**：TaskRetryPolicy 的重試延遲計算邏輯
  - **測試驗證**：所有 TaskRetryPolicyTests 測試案例現在穩定通過

## [1.6.2] [2025-06-05] [修復服務生命週期依賴問題]

### 修正和改進

- 2025/06/05 08:13 完全修復 TranscribeBackgroundService 服務生命週期依賴不匹配問題 (v1.6.2)
  - **TranscribeBackgroundService.cs**：
    - 修復 Singleton 服務依賴 Scoped 服務的生命週期問題
    - 移除直接依賴 ITranscribeTaskProcessor 和 ITaskRetryPolicy
    - 使用 IServiceScopeFactory 創建服務範圍以正確取得 Scoped 服務
    - 在 ExecuteTaskWithTimeout 方法中透過範圍工廠取得 ITranscribeTaskProcessor
    - 在 HandleFailedTask 方法中透過範圍工廠取得 ITaskRetryPolicy
    - 修改 ScheduleRetry 方法簽名以接受 ITaskRetryPolicy 參數
    - 確保每次任務處理都在獨立的服務範圍內執行
    - 避免了將 Scoped 服務提升為 Singleton 的問題
  - **TranscribeBackgroundServiceTests.cs**：
    - 更新單元測試以配合新的建構函數參數（移除 ITaskRetryPolicy 參數）
    - 使用 FakeItEasy 模擬 IServiceScopeFactory 和相關服務
    - 設定 ServiceProvider 模擬以提供 ITranscribeTaskProcessor 和 ITaskRetryPolicy
    - 修正所有建構函數驗證測試以測試正確的參數型別
    - 確保測試覆蓋率不受影響
  - **問題描述**：
    - 解決啟動時 "Cannot consume scoped service from singleton" 錯誤
    - 第一輪修復：ITranscribeTaskProcessor 的生命週期問題
    - 第二輪修復：ITaskRetryPolicy 的生命週期問題
    - 維持 ITranscribeTaskProcessor 和 DbContext 的 Scoped 生命週期
    - 確保資料庫上下文在每個請求範圍內正確管理
    - 應用程式現在可以正常啟動，無服務生命週期錯誤

## [1.6.1] [2025-06-04] [改進資料庫上下文測試環境檢測]

### 資料庫改進

- 2025/06/04 19:15 改進 FileLogDbContext 測試環境檢測 (v1.6.1)
  - **FileLogDbContext.cs**：
    - 新增 FakeItEasy 模擬物件檢測功能
    - 改進測試環境與生產環境的自動區分
    - 支援檢測 FakeItEasy 和 Castle.Proxies 型別
    - 測試環境跳過連線字串解密步驟
    - 改進錯誤訊息，包含環境資訊提示
    - 提升單元測試執行穩定性

## [1.6.0] [2025-06-04] [背景任務系統單元測試完成]

### 階段五：單元測試完成

- 2025/06/04 18:45 完成所有核心服務單元測試 (階段五 - 5.1)
  - **TaskRetryPolicyTests.cs**：
    - 實作重試策略完整測試覆蓋
    - 測試指數退避算法和重試決策邏輯
    - 驗證暫時性錯誤和永久性錯誤的區分
    - 測試最大重試次數和延遲時間計算
    - 涵蓋網路錯誤、API 限制和檔案錯誤等情境
    - 使用 Theory 測試多種重試次數組合
  - **TranscribeTaskProcessorTests.cs**：
    - 實作任務處理器核心功能測試
    - 測試檔案格式驗證和大小限制檢查
    - 驗證雲端 API 可用性檢測機制
    - 測試處理時間估算演算法
    - 模擬資料庫操作和狀態更新
    - 測試取消機制和錯誤處理流程
    - 使用記憶體資料庫確保測試隔離性
  - **TranscribeTaskQueueTests.cs**：
    - 驗證任務隊列的入隊和出隊操作
    - 測試優先級隊列排序機制
    - 驗證任務取消和完成標記功能
    - 測試重試任務的重新入隊邏輯
    - 檢查隊列統計資訊和容量限制
    - 測試並發安全性和線程同步
  - **測試技術架構**：
    - 使用 xUnit 測試框架
    - 採用 FakeItEasy 進行相依性模擬
    - 實作完整的 Arrange-Act-Assert 模式
    - 使用記憶體資料庫避免外部依賴
    - 實作 IDisposable 確保資源正確釋放
    - 涵蓋正常流程、邊界條件和異常情境

## [1.5.0] [2025-06-04] [背景任務系統 API 調整完成]

### 階段三：API 調整完成

- 2025/06/04 15:30 完成 WhisperController 重構 (階段三 - 3.1)
  - **Transcribe 方法背景化**：
    - 重構同步 Transcribe 方法為背景任務模式
    - 保留檔案上傳和驗證邏輯，確保安全性
    - 建立 TranscribeTask 並加入任務隊列
    - 立即回傳任務 ID，大幅改善用戶體驗
    - 新增錯誤處理和檔案清理機制
  - **依賴注入整合**：
    - 在建構函式中注入 ITranscribeTaskQueue 服務
    - 正確設定服務生命週期管理
    - 確保線程安全的任務操作

- 2025/06/04 15:45 完成任務狀態 API (階段三 - 3.2)
  - **GetTaskStatus API**：
    - 提供即時任務狀態查詢功能
    - 回傳詳細狀態資訊：處理狀態、進度百分比、錯誤訊息等
    - 實作權限控制，確保用戶只能查詢自己的任務
  - **CancelTask API**：
    - 支援用戶主動取消進行中的任務
    - 智能狀態檢查，只允許取消 Pending/Processing 狀態的任務
    - 自動更新任務狀態和時間戳記
  - **LoadHistory 增強**：
    - 新增狀態描述和進度百分比顯示
    - 優化回傳資料結構，支援前端狀態顯示
    - 保持向後相容性

### 階段四：系統整合完成

- 2025/06/04 16:00 完成依賴注入設定 (階段四 - 4.1)
  - **Program.cs 服務註冊**：
    - 註冊 ITranscribeTaskQueue 為 Singleton 服務
    - 註冊 ITranscribeTaskProcessor 和 ITaskRetryPolicy 為 Scoped 服務
    - 註冊 TranscribeBackgroundService 為 HostedService
    - 正確設定命名空間引用

- 2025/06/04 16:15 完成設定檔配置 (階段四 - 4.2)
  - **appsettings.json 更新**：
    - 新增 BackgroundTasks 設定區段
    - 設定最大並發任務數：2
    - 設定最大重試次數：3
    - 設定重試延遲時間：[1, 5, 15] 分鐘
    - 設定任務超時時間：30 分鐘
  - **appsettings.Development.json 優化**：
    - 開發環境降低並發數為 1
    - 縮短重試延遲：[1, 3] 分鐘
    - 縮短超時時間為 20 分鐘

### 技術改進

- **API 設計優化**：
  - 採用非同步背景處理架構，避免長時間請求阻塞
  - 實作即時狀態查詢機制，提升用戶體驗
  - 支援任務取消功能，增加系統靈活性
- **狀態管理增強**：
  - 新增 GetTaskProgress 和 GetStatusDescription 輔助方法
  - 統一狀態碼和描述訊息格式
  - 實作完整的任務生命週期管理
- **錯誤處理改進**：
  - 新增檔案清理機制，防止檔案系統污染
  - 完善異常處理和日誌記錄
  - 實作權限檢查和用戶隔離

### 建置驗證

- 2025/06/04 16:20 建置測試通過
  - .NET 8.0 編譯成功，無警告和錯誤
  - 所有依賴項目正確解析
  - 服務註冊配置驗證通過

## [1.4.7] [2025-06-04] [TaskRetryPolicy 重試策略實作完成]

### 重試策略系統實作

- 2025/06/04 14:38 完成 TaskRetryPolicy.cs 實作 (階段二 - 2.4)
  - **智能指數退避算法**：
    - 實作完整的指數退避策略：基礎延遲 × (2.0 ^ 重試次數)
    - 內建最大延遲時間保護機制，防止延遲時間過長
    - 支援線性增長備選方案，可根據業務需求靈活切換
    - 加入 10% 隨機抖動 (Jitter) 機制，有效避免雷群效應 (Thundering Herd)
    - 確保最小延遲時間為 1 秒，避免過度頻繁的重試
  - **精準的重試條件判斷**：
    - **暫時性錯誤檢測**：網路超時、連線中斷、API 限流、伺服器暫時不可用等
    - **永久性錯誤檢測**：檔案不存在、格式不支援、認證失敗、參數錯誤等
    - **智能 HTTP 狀態碼分析**：429(限流)、5xx(伺服器錯誤) 可重試，4xx(用戶端錯誤) 不重試
    - **OpenAI API 特化處理**：針對 OpenAI 服務的特定錯誤訊息進行精準分類
    - **地端 Whisper 支援**：針對本地 Whisper 服務的錯誤進行專門處理
  - **完善的策略配置系統**：
    - 支援動態配置重試參數：最大重試次數、基礎延遲、最大延遲等
    - 提供預設的企業級配置，適用於大多數生產環境場景
    - 支援關鍵字清單的動態更新，便於針對特定錯誤進行調整
    - 實作策略資訊查詢介面，便於監控和除錯
  - **優化的錯誤處理機制**：
    - 支援 Exception 物件的深度分析，提供更準確的重試決策
    - 特殊處理 TaskCanceledException 和 TimeoutException
    - 完整的日誌記錄，包含重試原因、延遲時間、錯誤分類等詳細資訊
    - 實作任務重試資訊自動更新機制，確保重試計數的準確性
  - **技術特色**：
    - **高效能設計**：使用 Random 實例避免重複建立，降低系統開銷
    - **線程安全**：採用無狀態設計，支援高並發環境下的安全使用
    - **可擴展性**：模組化的錯誤分類機制，便於新增自定義錯誤類型
    - **監控友好**：提供完整的策略狀態查詢和統計介面
  - **階段二核心服務完成**：
    - ✅ 2.1 任務隊列服務 (TranscribeTaskQueue)
    - ✅ 2.2 背景服務 (TranscribeBackgroundService)  
    - ✅ 2.3 任務處理器 (TranscribeTaskProcessor)
    - ✅ 2.4 重試策略 (TaskRetryPolicy)

## [1.4.6] [2025-06-04] [測試修正 - TranscribeBackgroundService 重試邏輯]

### 單元測試修正

- 2025/06/04 14:30 修正 TranscribeBackgroundServiceTests.cs 中的測試失敗
  - **修正 Service_WhenTaskFails_ShouldRetryTask 測試**：
    - 調整 CalculateRetryDelaySeconds 方法的期望參數
    - 從期望 retryCount: 1 修正為 retryCount: 0
    - 修正原因：ScheduleRetry 方法先調用 CalculateRetryDelaySeconds，然後才執行 task.RetryCount++
    - 確保測試期望值與實際實作邏輯一致
  - **測試覆蓋率**：維持完整的背景服務測試覆蓋率
  - **驗證結果**：所有單元測試通過，無其他測試受影響

## [1.4.5] [2025-06-04] [TranscribeTaskProcessor 任務處理器實作完成]

### 背景任務系統核心服務實作

- 2025/06/04 15:20 完成 TranscribeTaskProcessor.cs 實作 (階段二 - 2.3)
  - **完整移植現有 Transcribe 邏輯**：
    - 建立 `MeetingAssistant/Services/TranscribeTaskProcessor.cs` 實作 ITranscribeTaskProcessor 介面
    - 支援雲端 (OpenAI API) 和地端 Whisper API 雙處理模式，可根據任務設定動態切換
    - 完整移植檔案上傳、轉錄、摘要、翻譯處理流程，確保與原有功能完全相容
    - 自動產生 TXT (逐字稿)、SRT (字幕)、VTT (Web字幕)、摘要、翻譯等 5 種輸出檔案
    - 實作自動壓縮功能，將所有結果檔案打包為 archive.zip 便於下載管理
  - **完善的狀態更新機制**：
    - 實作即時 FileLog 狀態更新：Pending → Processing → Completed/Failed/Cancelled
    - 完整記錄處理時間、API 使用成本、重試次數等關鍵效能指標
    - 支援任務取消狀態管理，確保中斷任務的狀態正確記錄
    - 實作 LastProcessedAt 時間戳記錄，提供完整的處理軌跡追蹤
    - 自動清理和更新錯誤訊息，確保狀態資訊的準確性和即時性
  - **強化的錯誤處理機制**：
    - 實作智慧錯誤分類系統，區分可重試錯誤 (網路異常、API暫時性錯誤) 和不可重試錯誤 (檔案不存在、API Key無效)
    - 採用指數退避重試策略：第1次重試 1分鐘後、第2次 5分鐘後、第3次 15分鐘後、第4次 30分鐘後
    - 完整的例外處理覆蓋：HttpRequestException、TaskCanceledException、FileNotFoundException 等
    - 詳細錯誤記錄包含錯誤訊息、堆疊追蹤和重試建議，便於問題診斷和解決
    - 實作優雅的取消處理，確保被取消的任務不會丟失或造成資源洩漏
  - **檔案格式和大小驗證**：
    - 支援 8 種企業常用音檔格式：mp3, mp4, mpeg, mpga, m4a, ogg, wav, webm
    - 實作 4GB 最大檔案大小限制，符合企業大型會議檔案處理需求
    - 提供智慧處理時間估算：基於檔案大小 (每MB約0.5分鐘) 和音檔長度 (每分鐘音檔約0.2分鐘處理) 雙重評估
    - 實作檔案存在性檢查和目錄自動建立機制，確保處理流程的穩定性
    - 支援處理器可用性檢查，包含 API Key驗證、檔案目錄存取、資料庫連線等
  - **取消和超時控制機制**：
    - 實作完整的任務取消支援，支援 CancellationToken 在整個處理鏈中的傳遞
    - 可設定的處理超時時間控制，預設 20 分鐘超時避免資源長期佔用
    - 優雅的資源清理機制：確保檔案流、HTTP連線、記憶體等資源正確釋放
    - 實作超時處理邏輯，自動將超時任務標記為失敗並記錄詳細超時資訊
    - 支援服務停止時的任務狀態保存，確保系統重啟後可以恢復未完成任務
  - **技術實作優化**：
    - 使用 async/await 模式確保高效能非同步處理，避免執行緒阻塞
    - 實作完整的 IDisposable 模式和 using 語句確保資源管理
    - 採用結構化日誌記錄，提供詳細的處理軌跡和效能監控資料
    - 整合 Entity Framework 進行資料庫操作，確保資料一致性和交易安全
    - 實作設定注入模式，支援靈活的環境配置和部署管理

## [1.4.4] [2025-06-03] [TranscribeBackgroundService 程式碼註解完善]

### 程式碼文件化與可維護性提升

- 2025/06/03 17:50 完成 TranscribeBackgroundService.cs 詳細繁體中文註解新增
  - **類別與屬性註解**：
    - 為 TranscribeBackgroundService 類別新增詳細的功能說明
    - 為所有私有屬性新增詳細註解，說明其用途和職責
    - 包含 _taskQueue、_taskProcessor、_retryPolicy、_logger、_config 等核心元件
  - **建構函數註解**：
    - 詳細說明每個參數的用途和職責
    - 新增參數驗證邏輯的說明
    - 包含異常處理的文件說明
  - **方法邏輯區塊註解**：
    - ExecuteAsync 方法新增【並發控制區塊】、【主要處理迴圈】、【任務清理區塊】等邏輯分組註解
    - ProcessTaskWithRetryAsync 方法新增【超時控制區塊】、【任務狀態更新區塊】、【任務執行區塊】等詳細說明
    - HandleTaskResult、HandleFailedTask、ScheduleRetry 等方法的決策邏輯註解完善
  - **錯誤處理註解**：
    - 為各種異常處理區塊新增詳細說明
    - 包含【正常停止處理區塊】、【異常處理區塊】、【資源清理區塊】等分類
    - 說明各種 CancellationToken 的使用情境和目的
  - **資源管理註解**：
    - CleanupCompletedTasks 方法的記憶體管理邏輯說明
    - WaitForActiveTasks 方法的優雅停止機制說明
    - 信號量和超時控制的實作細節註解

## [1.4.3] [2025-06-03] [TranscribeBackgroundService 重構與效能優化]

### 背景服務穩定性提升

- 2025/06/03 17:20 完成 TranscribeBackgroundService.cs 全面重構
  - **記憶體管理優化**：
    - 實作定期清理機制避免已完成任務持續累積造成記憶體洩漏
    - 新增 `CleanupCompletedTasks` 方法自動移除已完成的任務參考
    - 改善任務列表管理，從簡單清理改為遞減迴圈避免索引錯位
    - 確保異常任務的 Exception 屬性被正確觀察以防止未處理異常
  - **資源管理強化**：
    - 在 `ProcessTaskWithRetryAsync` 中確保 CancellationTokenSource 正確釋放
    - 改善 Semaphore 釋放邏輯，使用 finally 區塊確保資源不會洩漏
    - 加入 ObjectDisposedException 處理避免已釋放物件的操作異常
    - 新增 `WaitForActiveTasks` 方法確保服務停止時優雅等待任務完成（最多5分鐘）
  - **錯誤處理架構重構**：
    - 將複雜的任務處理邏輯拆分為多個專職方法提高可讀性和維護性：
      - `ExecuteTaskWithTimeout` - 處理任務執行和超時控制，自動捕獲逾時異常並轉換為失敗結果
      - `HandleTaskResult` - 統一處理任務執行結果，根據成功或失敗狀態分流處理
      - `HandleFailedTask` - 專門處理失敗任務，判斷是否需要重試並執行相應邏輯
      - `ScheduleRetry` - 安排任務重試，計算延遲時間並更新重試計數
      - `DelayedRequeue` - 延遲重新排隊機制，實作指數退避策略避免系統過載
      - `HandleCancelledTask` - 處理因服務停止而取消的任務，確保任務不會丟失
      - `HandleUnexpectedError` - 處理未預期的系統異常，提供最後防線的錯誤恢復
    - 改善異常情況下的 Semaphore 釋放邏輯避免死鎖
    - 強化取消權杖處理，避免服務停止時的資源競爭
  - **並發控制優化**：
    - 改善信號量釋放邏輯，確保在異常情況下也能正確釋放避免資源洩漏
    - 優化錯誤恢復流程，確保服務在各種異常情況下都能持續運作
    - 加強背景任務的生命週期管理和資源清理機制
  - **技術特色強化**：
    - **智能清理機制**：定期清理已完成任務避免長時間運行造成的記憶體問題
    - **強韌錯誤恢復**：多層次異常處理確保服務在各種異常情況下都能持續運作
    - **優雅關閉流程**：服務停止時確保所有資源被正確清理，任務狀態完整保存
    - **詳細日誌記錄**：完整的操作軌跡記錄便於問題追蹤和效能監控分析

## [1.4.2] [2025-06-03] [MeetingHelper 類別註解完善化]

### 文件品質提升

- 2025/06/03 14:30 完成 MeetingHelper.cs 詳細繁體中文註解
  - **註解內容全面升級**：
    - 為所有 17 個方法添加詳細的功能說明和使用場景
    - 為 6 個常數和屬性提供完整的技術規格和安全提醒
    - 為建構函式添加最佳實踐建議和使用指引
    - 類別層級添加企業應用場景和技術優勢說明
  - **註解結構化改善**：
    - 使用 `<list>` 標籤提供結構化的功能列表
    - 添加 `<remarks>` 區段提供深度技術說明
    - 包含成本計算公式、錯誤處理機制、系統配置等詳細資訊
    - 提供適用場景、技術特色、品質控制等專業建議
  - **開發者體驗優化**：
    - 過時方法標記 `[Obsolete]` 並提供遷移建議
    - API 成本計算公式和定價資訊完整說明
    - 本地化 vs 雲端服務的選擇指導
    - 企業資料安全和隱私保護最佳實踐

## [1.4.1] [2025-05-31] [背景任務隊列服務實作完成]

### 核心服務實作

- 2025/05/31 23:48 完成任務隊列服務實作 (階段二 - 2.1)
  - **高效能任務隊列**：
    - 建立 `MeetingAssistant/Services/TranscribeTaskQueue.cs`
    - 使用優先級隊列實作任務排序，支援多優先級並發處理
    - 實作並發控制機制，支援最大並發任務數限制
    - 支援任務狀態追蹤：待處理、處理中、已完成、失敗
  - **功能特色**：
    - 優先級排序：數字越小優先級越高，支援動態優先級調整
    - 智能重試：失敗任務自動重新排隊，優先級自動降低
    - 統計監控：提供即時的隊列統計資訊和處理狀態
    - 資源管理：支援隊列容量限制和優雅關閉機制
    - 任務取消：支援取消待處理任務，正在處理的任務不可取消
  - **技術實作**：
    - 使用 ConcurrentDictionary 確保線程安全的任務追蹤
    - 使用 SortedDictionary 實作優先級隊列排序
    - 實作 IDisposable 模式確保資源正確釋放
    - 完整的日誌記錄和錯誤處理機制
  - **測試驗證**：
    - 建立 `Test/TranscribeTaskQueueTests.cs` 完整單元測試
    - 涵蓋所有核心功能：加入隊列、取出任務、優先級排序、取消任務等
    - 使用 FakeItEasy 模擬框架進行隔離測試
    - 所有 10 個測試案例全部通過，確保實作品質

## [1.4.0] [2025-05-31] [架構重構 - 背景任務系統基礎建設]

### 架構改進

- 2025/05/31 22:51 完成背景任務系統的基礎架構建設 (階段一)
  - **資料庫模型更新**：
    - 擴展 `ProcessingStatus.cs`，新增 `Failed = 3` 和 `Cancelled = 4` 狀態
    - 增強 `FileLog.cs`，新增重試相關欄位：`RetryCount`, `ErrorMessage`, `LastProcessedAt`
    - 建立完整的任務資料結構 `TranscribeTask.cs`，支援優先級、重試機制和多種處理模式
    - 建立系統設定模型 `BackgroundTaskConfig.cs`，包含隊列、重試、監控等完整配置選項
  - **服務介面定義**：
    - 建立 `ITranscribeTaskQueue.cs`，定義任務隊列管理、統計和監控介面
    - 建立 `ITranscribeTaskProcessor.cs`，定義任務處理器和處理結果標準化介面  
    - 建立 `ITaskRetryPolicy.cs`，定義智能重試策略和錯誤分類機制
  - **系統特色**：
    - 支援 Cloud 和 Local 雙模式處理器配置
    - 實作指數退避和抖動演算法的重試策略
    - 提供完整的任務生命週期管理和統計監控
    - 設計可擴展的優先級隊列和並發控制機制
  - 此變更為系統從同步轉為非同步背景處理奠定堅實基礎，大幅提升使用者體驗和系統可靠性

## [1.3.0] [2025-06-24] [實作 Pinia 狀態管理和自動更新功能]

### 新功能

- **實作 Pinia 狀態管理系統**：
  - 新增 `historyStore` 管理歷史記錄和任務監控狀態
  - 新增 `toastStore` 管理通知訊息系統
  - 集中化狀態管理，提供更好的資料一致性和可維護性

### 重大改進

- **解決歷史記錄自動更新問題**：
  - `HistoryList.vue` 現在直接使用 `historyStore`，實現即時自動更新
  - 移除對 props 的依賴，消除父子組件間的資料同步延遲
  - 任務狀態變更時，歷史記錄清單會自動反映最新狀態
  - 處理中任務完成後，狀態會立即更新顯示

### 任務監控系統優化

- **統一任務監控機制**：
  - 將任務監控邏輯移至 `historyStore`，實現全局管理
  - 使用自定義事件 (`taskCompleted`, `taskFailed`) 進行組件間通訊
  - 自動檢測並監控處理中的任務，無需手動啟動
  - 優化監控效率，避免重複檢查和資源浪費

### 程式碼重構

- **AssistantIndex.vue 重構**：
  - 移除本地任務監控邏輯，使用 `historyStore` 統一管理
  - 移除本地 Toast 系統，使用 `toastStore` 集中管理
  - 簡化組件邏輯，提高程式碼可讀性和維護性
  - 使用事件監聽器處理任務完成/失敗通知

- **HistoryList.vue 重構**：
  - 移除 `historyList` prop 依賴，直接使用 store 資料
  - 實現響應式資料更新，狀態變更時自動重新渲染
  - 保持原有的分頁和刪除功能，但資料來源改為 store

### 技術架構改進

- **狀態管理模式**：
  - 採用 Composition API 風格的 Pinia stores
  - 提供型別安全的狀態管理
  - 支援響應式資料更新和計算屬性
  - 實現狀態持久化和清理機制

- **事件驅動架構**：
  - 使用 `window.dispatchEvent` 和 `window.addEventListener` 實現跨組件通訊
  - 解耦組件間的直接依賴，提高系統靈活性
  - 支援多個組件同時監聽同一事件

### 使用者體驗提升

- **即時狀態更新**：
  - 歷史記錄頁面開啟時，處理中任務狀態會即時更新
  - 任務完成後，使用者無需手動刷新即可看到最新狀態
  - 處理中、已完成、失敗等狀態即時反映在 UI 上

- **統一通知系統**：
  - 所有 Toast 通知統一管理，避免重複或遺漏
  - 支援自動消失和手動關閉功能
  - 提供一致的視覺風格和互動體驗

### 技術細節

- **新增檔案**：
  - `src/stores/historyStore.ts` - 歷史記錄和任務監控狀態管理
  - `src/stores/toastStore.ts` - Toast 通知系統狀態管理

- **修改檔案**：
  - `src/main.ts` - 加入 Pinia 設定
  - `src/components/AssistantIndex.vue` - 重構使用 stores
  - `src/components/HistoryList.vue` - 移除 props 依賴，直接使用 store

- **套件依賴**：
  - 新增 `pinia` 套件依賴
  - 更新 `package.json` 和 `package-lock.json`

### 向下相容性

- 保持所有現有 API 和功能不變
- 使用者介面和操作流程完全一致
- 現有的檔案上傳、處理、查看功能正常運作

---

## [1.3.0] [2025-05-12] [功能增強]

### 新功能

- 2025/05/12 新增 WhisperOnPremisesProcessor 支援 Whisper.cpp Server Web API
  - 實現了新方法 `CallWhisperServerWebApiAsync` 用於呼叫 Whisper.cpp Server Web API
  - 使用 HTTP 客戶端透過 multipart/form-data 格式上傳音檔到 API 位於 `http://**********:22434/inference`
  - 設置必要的 API 參數：temperature、temperature_inc 和 response_format
  - 更新了 `WhisperTranscribeAsync` 方法使用新的 Web API 實現
  - 此變更改善了語音轉錄的效能與穩定性，無需本地執行 Whisper 可執行檔

- 2025/05/30 15:30 為 `OnPremisesWhisper/Program.cs` 添加詳細的繁體中文註解
  - 為 Program 類別添加 XML 文件註解，說明本地 Whisper 語音轉文字服務的主要功能
  - 為 Main 方法添加完整的參數說明和功能描述
  - 為 Kestrel 伺服器配置添加詳細說明，解釋 4GB 檔案大小限制的用途
  - 為 FormOptions 配置添加註解，說明多部分表單檔案上傳的限制設定
  - 為 Swagger 服務註冊添加註解，說明 API 文件產生功能
  - 為 HTTP 請求處理管道中的每個中介軟體添加詳細說明
  - 為路由配置和應用程式啟動過程添加解釋性註解
  - 此變更提高了代碼的可讀性和可維護性，便於團隊成員理解本地 Whisper 服務架構

---

## [1.2.0] [2025-04-25] [程式碼改進]

### 代碼優化

- 2025/04/25 為 `Program.cs` 添加詳細的繁體中文註解
  - 為檔案頂部添加全面的檔案說明，概述主要功能和責任
  - 為每個命名空間引用添加詳細解釋，說明其在專案中的用途
  - 為 WebApplication 建構和配置過程添加詳細註解
  - 為 Kestrel 伺服器的大型檔案上傳配置添加解釋性註解
  - 為資料庫連接、身份驗證和授權邏輯添加說明註解
  - 為 HTTP 請求管道的中間件配置添加詳細流程說明
  - 為 SPA 和 Vite 開發伺服器配置添加專業技術說明
  - 此變更提高了代碼的可讀性和可維護性，有助於新開發人員快速理解系統架構

## [未發布]

### 改進

- 2025/04/24 更新路由註冊方式，採用 .NET 8 推薦的頂層路由註冊語法
  - 移除了已經被註解掉的 `UseEndpoints(endpoints => endpoints.MapControllers())` 代碼
  - 確認使用了更簡潔的 `app.MapControllers()` 語法
  - 這項改進讓代碼更加簡潔且符合 .NET 8 的最佳實踐

### 其他變更

- 無

### 新增功能

- 2025/03/20 在 `WhisperProcessor` 中增加計算音檔總時間長度的功能
  - 在 `Transcribe` 模型中添加 `Duration` 屬性，用於存儲音檔總時間長度（秒）
  - 在 `ProcessVideoFileAsync` 方法中，使用 `GetAudioDuration` 方法計算音檔總時間長度，並將結果存儲在 `Transcribe` 物件的 `Duration` 屬性中
  - 此功能可讓使用者了解處理的音檔或影片的總時間長度，對會議摘要和逐字稿生成很有幫助

- 2025/03/21 添加 Whisper API 成本追蹤功能
  - 在 `FileLog` 和 `Transcribe` 模型中添加 `WhisperApiCost` 屬性，用於記錄 Whisper API 的使用成本（美元）
  - 確保在 `Transcribe.FromFileLog` 方法中同步轉換 `WhisperApiCost` 屬性
  - 在 `WhisperProcessor` 中實現成本計算邏輯：
    - 添加 `WHISPER_COST_PER_MINUTE` 常數，設定為 0.006 美元/分鐘
    - 修改 `CallWhisperApiAsync` 方法，計算每個音檔區段的處理成本
    - 在 `ProcessVideoFileAsync` 方法中累計總成本並存儲在 `Transcribe` 物件的 `WhisperApiCost` 屬性中
  - 此功能可讓使用者和管理員了解每次處理的 API 成本，有助於成本控制和預算管理

- 2025/03/21 添加翻譯和摘要 API 成本追蹤功能
  - 在 `MeetingHelper` 中添加翻譯和摘要 API 的計費常數：
    - `GPT4O_MINI_INPUT_COST_PER_1K`：每 1000 個輸入 token 成本 (0.15 美元)
    - `GPT4O_MINI_OUTPUT_COST_PER_1K`：每 1000 個輸出 token 成本 (0.60 美元)
  - 修改 `TranslateAsync` 和 `SummarizeAsync` 方法，計算每次 API 調用的處理成本
  - 在 `WhisperProcessor` 中將翻譯和摘要成本加入到 `WhisperApiCost` 中
  - 此功能可讓使用者和管理員了解每次處理的完整 API 成本，包括音訊轉錄、文本翻譯和摘要生成的成本

### 修復問題

- 無

## [2025-03-20]

- 初始版本

## 自動變更記錄管理

- **每次修改程式碼後，必須自動更新 CHANGELOG.md**
- 包含所有檔案修改、新增功能、Bug 修復等
- 在完成主要任務並確認沒有錯誤後，主動詢問是否需要更新變更記錄
- 變更記錄必須包含版本號遞增和詳細描述
