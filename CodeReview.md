
# MeetingAssistant 專案程式碼審查報告

## 1. 總體架構與設計 (Overall Architecture & Design)

專案採用了經典的 .NET + Vue.js 前後端分離架構，並透過背景服務處理耗時的轉錄任務，整體設計良好。

- **優點**:
  - **前後端分離**: 使得前端（Vue.js）和後端（ASP.NET Core）可以獨立開發、部署和擴展。
  - **背景任務處理**: 使用 `IHostedService` (`TranscribeBackgroundService`) 和任務隊列 (`ITranscribeTaskQueue`) 來處理耗時的音訊轉錄工作，避免了 HTTP 請求超時，提升了使用者體驗。這是一個非常好的設計模式。
  - **模組化**: 功能被劃分到不同的專案中 (`MeetingAssistant`, `MeetingUtility`, `OnPremisesWhisper`)，職責清晰。
  - **多模式支援**: `WhisperProcessor` 的抽象設計 (`WhisperProcessorBase`) 讓系統可以彈性地在雲端 (`WhisperApiProcessor`) 和地端 (`WhisperOnPremisesProcessor`) 處理器之間切換，增加了系統的靈活性和適用場景。

- **可改進之處**:
  - **設定管理**: 地端服務的 URL (例如 `OllamaApiUrl`) 和模型名稱 (`TranslateModel`, `SummaryModel`) 硬編碼在 `WhisperOnPremisesProcessor.cs` 中。建議將這些設定移至 `appsettings.json`，使其更易於配置。
  - **關注點分離**: `WhisperController` 處理了檔案儲存、業務邏輯、API 路由等多種職責，顯得有些臃腫。可以考慮引入一個服務層 (Service Layer) 來封裝核心業務邏輯，讓 Controller 更專注於處理 HTTP 請求和回應。

## 2. 後端程式碼 (C#)

### 安全性 (Security)

- **API 金鑰管理**: `WhisperApiProcessor` 和 `MeetingHelper` 透過建構函式接收 `apiKey`，這點很好。但在 `WhisperController` 中，`callOpenAI` 的設定是從 `IConfiguration` 讀取，但 API Key 的傳遞路徑沒有明確展示，需要確保金鑰沒有在程式碼中硬編碼。
- **檔案上傳**:
  - 您已經設定了請求大小限制 (`MaxRequestBodySize`)，這點很好。
  - **風險**: 目前程式碼直接使用使用者上傳的 `mediaFile.FileName` 來儲存檔案。這可能存在路徑遍歷 (Path Traversal) 的風險，攻擊者可能上傳如 `../../malicious.exe` 的檔名。
  - **建議**: 對檔名進行清理，只保留安全的字元，或者生成一個全新的、安全的檔名來儲存。

      ```csharp
      // 建議作法
      var safeFileName = Path.GetRandomFileName(); // 或其他安全命名方式
      var extension = Path.GetExtension(mediaFile.FileName);
      var finalFileName = $"{safeFileName}{extension}";
      string mediaFilePath = Path.Combine(dataDir, finalFileName);
      ```

- **使用者身份驗證**: `WhisperController` 中的 `[Authorize]` 屬性確保了 API 的存取需要授權，這點做得很好。

### 效能 (Performance)

- **同步檔案 I/O**: 在 `WhisperProcessorBase.cs` 的 `ConvertVideoToAudio` 和 `SplitAudioFile` 方法中，您使用了 `proc.WaitForExit()`，這會同步阻塞執行緒直到外部程序 (ffmpeg) 完成。對於 Web 應用程式，長時間的同步阻塞可能會耗盡執行緒池資源。
  - **建議**: 考慮使用 `Process` 的非同步版本 `WaitForExitAsync()` (在 .NET 5+ 中可用)，或者將整個 ffmpeg 的呼叫封裝在一個 `Task.Run` 中，以避免阻塞主執行緒。
- **資料庫查詢**: `LoadHistory` 方法中的查詢使用了 `.Take(200)`，這避免了一次載入過多資料，是很好的實踐。

### 錯誤處理 (Error Handling)

- **`WhisperController`**: `Transcribe` 方法中的 `try-catch` 區塊很完善，能夠在任務建立失敗時清理已上傳的檔案。
- **`WhisperOnPremisesProcessor`**: `CallWhisperExeAsync` 和 `CallWhisperServerWebApiAsync` 中的錯誤處理可以更具體。目前捕捉到 `Exception` 後只是寫入控制台並重新拋出。
  - **建議**: 可以定義一些自訂的例外型別 (e.g., `WhisperProcessingException`)，這樣上層呼叫者可以根據不同的錯誤類型做出更精細的處理。

### 可維護性與可讀性 (Maintainability & Readability)

- **註解**: 您的程式碼，特別是 `MeetingHelper.cs` 和 `WhisperController.cs`，擁有非常詳盡的中文註解，這對於理解程式碼非常有幫助。
- **硬編碼字串**:
  - `ffmpeg` 和 `ffprobe` 的路徑是直接寫死的。如果這些工具不在系統的 `PATH` 環境變數中，程式就會失敗。建議將其路徑也放入 `appsettings.json`。
  - `WhisperApiProcessor` 中的 `WHISPER_COST_PER_MINUTE` 是硬編碼的。如果 OpenAI 的價格變動，就需要修改程式碼。可以考慮將其移到設定檔中。

## 3. 前端程式碼 (Vue.js)

- **結構**: 元件劃分 (`AssistantIndex`, `HistoryList`, `ResultView`) 清晰，職責明確。
- **狀態管理**: 您使用了 Pinia (`historyStore`, `toastStore`) 來管理狀態，這是目前 Vue 生態系統中的最佳實踐，做得很好。
- **API 呼叫**: API 呼叫邏輯分散在元件中。
  - **建議**: 建立一個專門的 API 服務層 (例如 `src/api/whisper.ts`) 來封裝所有對後端 `/api/whisper` 的請求。這樣可以讓元件的邏輯更乾淨，並且 API 的管理也更集中。
- **使用者體驗**:
  - **檔案上傳**: 上傳檔案時，介面會顯示 "處理中..."，並在完成後自動刷新歷史記錄，體驗流暢。
  - **錯誤提示**: 使用了 `toastStore` 來顯示錯誤訊息，這點很好。可以考慮在 API 呼叫失敗時，提供更具體的錯誤訊息給使用者（例如，不僅僅是 "上傳失敗"，而是 "上傳失敗：檔案格式不支援"）。
- **安全性**:
  - **前端路由**: 專案是單頁應用，沒有複雜的前端路由，目前看起來沒有安全問題。後端的 `[Authorize]` 屬性是主要的保護屏障。

## 4. 總結與建議

您的專案是一個功能完整且設計精良的應用程式。主要的優點在於清晰的架構、對背景任務的妥善處理以及對多種處理模式的支援。

以下是按優先順序排列的改進建議：

1. **【高優先級 - 安全性】清理上傳的檔名**: 在 `WhisperController.cs` 的 `Transcribe` 方法中，對使用者上傳的檔名進行清理，以防止路徑遍歷攻擊。
2. **【中優先級 - 可維護性】將設定外部化**: 將硬編碼的設定（如 Ollama URL、模型名稱、ffmpeg 路徑、API 成本）移至 `appsettings.json`。
3. **【中優先級 - 架構】重構 `WhisperController`**: 引入一個服務層來處理核心業務邏輯，讓 Controller 更輕量。
4. **【低優先級 - 效能】非同步處理外部程序**: 將 `ffmpeg` 的呼叫改為非同步，以避免阻塞 Web 伺服器的執行緒。
5. **【低優先級 - 前端】建立 API 服務層**: 在 Vue 應用中，建立一個專門的 API 層來管理對後端的呼叫。

這份報告旨在提供建設性的回饋。您的專案已經非常出色，希望這些建議能幫助它變得更加健壯和易於維護。
