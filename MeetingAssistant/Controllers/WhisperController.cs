using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Security;
using System.Threading.Tasks;

using MeetingAssistant.Models;

using MeetingUtility.Interfaces;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace MeetingAssistant.Server.Controllers
{
    /// <summary>
    /// Whisper 語音轉文字控制器
    /// 提供語音轉文字、字幕生成、會議摘要等功能
    /// 支援雲端 OpenAI API 和地端 Whisper API 兩種處理模式
    /// </summary>
    [Authorize] // 需要授權才能存取
    [Route("api/[controller]/[action]")] // API 路由規則
    [ApiController] // 標記為 API 控制器
    public class WhisperController : ControllerBase
    {
        #region 私有變數宣告

        /// <summary>
        /// 系統設定檔案存取介面
        /// 用於讀取 appsettings.json 中的設定值
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// 是否呼叫 OpenAI API 的開關
        /// true: 使用雲端 OpenAI API
        /// false: 使用地端 Whisper API
        /// </summary>
        private bool callOpenAI = false;

        /// <summary>
        /// 資料庫上下文實例
        /// 用於存取檔案記錄、使用記錄等資料表
        /// </summary>
        private readonly FileLogDbContext _dbContext;

        /// <summary>
        /// 檔案儲存根目錄路徑
        /// 所有上傳的音視頻檔案和處理結果都會儲存在此目錄下
        /// </summary>
        private static string FileDir = "";

        /// <summary>
        /// 地端 Whisper API 的完整 URL 位址
        /// 當選擇地端處理模式時，會呼叫此 API 進行語音轉文字
        /// </summary>
        private readonly string OnPremisesApiUrl = "http://10.10.15.152:22434/transcribe";

        /// <summary>
        /// 日誌記錄器實例
        /// 用於記錄系統運行過程中的訊息、警告和錯誤
        /// </summary>
        private readonly ILogger<WhisperController> _logger;

        /// <summary>
        /// 轉錄任務隊列介面
        /// 負責管理背景處理的語音轉文字任務
        /// </summary>
        private readonly ITranscribeTaskQueue _taskQueue;



        #endregion

        #region 建構函式

        /// <summary>
        /// WhisperController 建構函式
        /// 初始化控制器所需的各項服務和設定
        /// </summary>
        /// <param name="configuration">系統設定檔案存取介面</param>
        /// <param name="dbContext">資料庫上下文，用於資料存取</param>
        /// <param name="logger">日誌記錄器，用於記錄系統運行訊息</param>
        /// <param name="taskQueue">轉錄任務隊列，用於背景任務管理</param>
        public WhisperController(IConfiguration configuration, FileLogDbContext dbContext, ILogger<WhisperController> logger, ITranscribeTaskQueue taskQueue)
        {
            // 注入依賴項目
            _configuration = configuration;
            _dbContext = dbContext;
            _logger = logger;
            _taskQueue = taskQueue;

            // 從設定檔讀取檔案儲存目錄，如果未設定則使用系統暫存目錄
            string? fileDir = _configuration["FileDir"];
            if (string.IsNullOrEmpty(fileDir))
            {
                FileDir = Path.GetTempPath(); // 使用系統預設暫存目錄
            }
            else
            {
                FileDir = fileDir; // 使用設定檔中指定的目錄
            }

            // 從設定檔讀取地端 API URL，如果有設定則覆蓋預設值
            string? onPremisesApiUrl = _configuration["OnPremisesApiUrl"];
            if (!string.IsNullOrEmpty(onPremisesApiUrl))
            {
                OnPremisesApiUrl = onPremisesApiUrl;
            }

            // 從設定檔讀取是否呼叫 OpenAI API 的開關
            string? callOpenAISetting = _configuration["CallOpenAI"];
            if (!string.IsNullOrEmpty(callOpenAISetting))
            {
                callOpenAI = bool.Parse(callOpenAISetting);
            }
        }
        #endregion

        /// <summary>
        /// 產生使用者專屬的暫存目錄
        /// 根據員工編號和唯一識別碼建立資料夾結構
        /// 目錄結構: FileDir/{empNo}/{guid}/
        /// </summary>
        /// <param name="empNo">員工編號，用於區分不同使用者</param>
        /// <param name="guid">唯一識別碼，用於區分不同的上傳任務</param>
        /// <returns>完整的目錄路徑，如果建立失敗則返回空字串</returns>
        private string GetDataDirectory(string empNo, string guid)
        {
            string result = "";
            try
            {
                // 組合完整的目錄路徑
                result = Path.Combine(FileDir, empNo, guid);

                // 如果目錄不存在則建立
                if (!Directory.Exists(result))
                {
                    Directory.CreateDirectory(result);
                }
            }
            catch (SecurityException ex)
            {
                // 如果因為安全性問題無法建立目錄，記錄錯誤
                Console.WriteLine(ex.StackTrace);
            }
            return result;
        }

        /// <summary>
        /// 建立包含多個檔案的 ZIP 壓縮檔
        /// 用於將轉錄結果（逐字稿、字幕、摘要等）打包供使用者下載
        /// </summary>
        /// <param name="zipPath">ZIP 檔案的完整儲存路徑</param>
        /// <param name="filesToZip">要壓縮的檔案路徑陣列</param>
        void CreateZip(string zipPath, string[] filesToZip)
        {
            // 建立 ZIP 檔案串流
            using (FileStream zipToCreate = new FileStream(zipPath, FileMode.Create))
            {
                // 建立 ZIP 壓縮檔案庫
                using (ZipArchive archive = new ZipArchive(zipToCreate, ZipArchiveMode.Create))
                {
                    // 逐一處理每個要壓縮的檔案
                    foreach (var filePath in filesToZip)
                    {
                        if (System.IO.File.Exists(filePath)) // 確保檔案存在才進行壓縮
                        {
                            string fileName = Path.GetFileName(filePath); // 取得檔案名稱（不含路徑）
                            // 在 ZIP 中建立新的檔案項目，使用最佳壓縮等級
                            ZipArchiveEntry entry = archive.CreateEntry(fileName, CompressionLevel.Optimal);

                            // 開啟來源檔案讀取串流
                            using (var fileStream = System.IO.File.OpenRead(filePath))
                            // 開啟 ZIP 項目寫入串流
                            using (var entryStream = entry.Open())
                            {
                                fileStream.CopyTo(entryStream); // 將檔案內容複製到 ZIP 中
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 載入指定任務的完整轉錄結果
        /// 包含逐字稿、SRT字幕、WebVTT字幕、摘要和翻譯內容
        /// </summary>
        /// <param name="guid">任務的唯一識別碼</param>
        /// <returns>包含完整轉錄資料的 Transcribe 物件</returns>        
        [Authorize] // 需要使用者授權
        [HttpGet] // HTTP GET 方法
        public async Task<IActionResult> LoadTranscribe(Guid guid)
        {
            // 驗證使用者身份，取得有效的員工編號
            string empNo = NetUtil.GetValidEmpNo(User?.Identity?.Name ?? string.Empty);
            if (empNo == null)
            {
                _logger.LogWarning("Invalid user");
                return BadRequest("Invalid user");
            }

            // 取得使用者的 IP 位址和主機名稱，用於記錄使用情況
            (IPAddress? ip, string? hostname) = NetUtil.GetIP_Hostname(HttpContext);
            if (ip == null)
            {
                _logger.LogWarning("Invalid IP");
                return BadRequest("Invalid IP");
            }

            // 取得使用者的詳細資訊（姓名、email、部門等）
            (string name, string email, int deptNo, string deptName) = NetUtil.GetDeptEtc(empNo, _configuration);

            // 將 LoadTranscribe 的使用記錄寫入資料庫，用於統計和稽核
            UsageLog usageLog = new UsageLog
            {
                EmpNo = empNo,
                Name = name,
                DeptNo = deptNo,
                Department = deptName,
                IP = ip.ToString(),
                Hostname = hostname,
                CallTime = DateTime.Now,
                APIName = "LoadTranscribe",
                Remark = guid.ToString()
            };
            _dbContext.Add(usageLog);
            _dbContext.SaveChanges();
            _logger.LogInformation("LoadTranscribe called by {EmpNo} ({Name})", empNo, name);

            try
            {
                // 從資料庫查詢指定 GUID 的檔案記錄，取最新的一筆
                FileLog? fileLog = await _dbContext.FileLogs.Where(f => f.Uid == guid).OrderByDescending(f => f.Id).FirstOrDefaultAsync();
                if (fileLog == null)
                {
                    // 如果找不到記錄，回傳空的 JSON 物件
                    return Ok("{}");
                }

                // 將 FileLog 轉換為 Transcribe 物件
                Transcribe transcribe = MeetingAssistant.Models.Transcribe.FromFileLog(fileLog);

                // 取得原始檔案名稱（不含副檔名）
                string mainFilename = Path.GetFileNameWithoutExtension(fileLog.FileName);

                // 取得該任務的資料目錄
                string dataDir = GetDataDirectory(empNo, guid.ToString());

                // 讀取逐字稿內容（純文字格式）
                string txtFilePath = Path.Combine(dataDir, $"{mainFilename}.txt");
                transcribe.Transcript = System.IO.File.ReadAllText(txtFilePath);

                // 讀取 SRT 字幕檔內容
                string srtFilePath = Path.Combine(dataDir, $"{mainFilename}.srt");
                transcribe.Srt = System.IO.File.ReadAllText(srtFilePath);

                // 讀取 WebVTT 字幕檔內容
                string vttFilePath = Path.Combine(dataDir, $"{mainFilename}.vtt");
                transcribe.Vtt = System.IO.File.ReadAllText(vttFilePath);

                // 讀取會議摘要內容
                string summaryFilePath = Path.Combine(dataDir, $"summary.txt");
                transcribe.Summary = System.IO.File.ReadAllText(summaryFilePath);

                // 讀取翻譯內容
                string translateFilePath = Path.Combine(dataDir, $"translation.txt");
                transcribe.Translation = System.IO.File.ReadAllText(translateFilePath);

                // 設定音檔長度和 API 成本資訊
                transcribe.Duration = fileLog.Duration;
                transcribe.WhisperApiCost = fileLog.WhisperApiCost;

                return Ok(transcribe);
            }
            catch (Exception ex)
            {
                // 記錄錯誤訊息到日誌
                _logger.LogError(ex, "Error when loading history for user {EmpNo} {StackTrace}", empNo, ex.StackTrace);
                return BadRequest("Error when loading history");
            }
        }

        /// <summary>
        /// 載入使用者的歷史記錄清單
        /// 顯示該使用者所有已上傳的檔案及其處理狀態
        /// 限制最多回傳 200 筆記錄
        /// </summary>
        /// <returns>包含歷史記錄清單的 JSON 物件</returns>        
        [Authorize] // 需要使用者授權
        [HttpGet] // HTTP GET 方法
        public async Task<IActionResult> LoadHistory()
        {
            // 驗證使用者身份
            string empNo = NetUtil.GetValidEmpNo(User?.Identity?.Name ?? string.Empty);
            if (empNo == null)
            {
                _logger.LogWarning("Invalid user");
                return BadRequest("Invalid user");
            }

            // 取得使用者 IP 資訊
            (IPAddress? ip, string? hostname) = NetUtil.GetIP_Hostname(HttpContext);
            if (ip == null)
            {
                _logger.LogWarning("Invalid IP");
                return BadRequest("Invalid IP");
            }

            // 取得使用者詳細資訊
            (string name, string email, int deptNo, string deptName) = NetUtil.GetDeptEtc(empNo, _configuration);

            // 註解：暫時不記錄 LoadHistory 的使用情況，避免產生過多記錄
            // 因為歷史記錄查詢頻率較高，記錄會造成資料庫負擔
            //UsageLog usageLog = new UsageLog
            //{
            //    EmpNo = empNo,
            //    Name = name,
            //    DeptNo = deptNo,
            //    Department = deptName,
            //    IP = ip.ToString(),
            //    Hostname = hostname,
            //    CallTime = DateTime.Now,
            //    APIName = "LoadHistory"
            //};
            //_dbContext.Add(usageLog);
            //_dbContext.SaveChanges();
            //_logger.LogInformation("LoadHistory called by {EmpNo} ({Name})", empNo, name);
            // 除錯資訊：檢查資料庫中的記錄總數
            //Console.WriteLine("_dbContext.FileLogs.Count() = " + _dbContext.FileLogs.Count());
            //var his = _dbContext.FileLogs
            //        .Where(f => f.EmpNo == empNo).OrderByDescending(f => f.Id);
            //Console.WriteLine("his.Count() = " + his.Count());
            //var errorHere = his.ToList();

            try
            {
                // 查詢該使用者的檔案記錄，按 ID 降序排列，限制最多 200 筆
                var fileLogs = await _dbContext.FileLogs
                    .Where(f => f.EmpNo == empNo) // 篩選該使用者的記錄
                    .OrderByDescending(f => f.Id) // 按 ID 降序排列（最新的在前）
                    .Take(200) // 限制最多 200 筆記錄，避免回傳過多資料
                    .Select(f => new
                    {
                        // 基本識別資訊
                        f.Uid,                    // 唯一識別碼
                        f.Id,                     // 資料庫主鍵
                        f.EmpNo,                  // 員工編號
                        f.Name,                   // 員工姓名
                        f.DeptNo,                 // 部門編號
                        f.Department,             // 部門名稱
                        f.IP,                     // 使用者 IP
                        f.Hostname,               // 主機名稱

                        // 檔案相關資訊
                        f.UploadTime,             // 上傳時間
                        f.FileName,               // 檔案名稱
                        f.FileSize,               // 檔案大小（位元組）
                        f.Duration,               // 音檔長度（秒）
                        f.WhisperApiCost,         // Whisper API 成本（美元）

                        // 處理狀態資訊
                        f.IsCompleted,            // 是否處理完成
                        //Status = f.Status.ToString(),           // 狀態名稱（字串）
                        //StatusCode = (int)f.Status,             // 狀態代碼（數字）
                        f.Status,                 // 狀態代碼（數字）
                        f.ProcessedTime,          // 處理完成時間
                        f.CreatedAt,              // 建立時間
                        f.UpdatedAt,              // 更新時間
                        f.RetryCount,             // 重試次數
                        f.ErrorMessage,           // 錯誤訊息
                        f.LastProcessedAt,        // 最後處理時間
                        f.Remark,                 // 備註
                                                  // 計算欄位
                        Progress = GetTaskProgress(f.Status),                // 處理進度百分比
                        StatusDescription = GetStatusDescription(f.Status)   // 狀態中文描述
                    })
                    .ToListAsync(); // 非同步執行查詢

                return Ok(fileLogs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error when loading history for user {EmpNo} {StackTrace}", empNo, ex.StackTrace);
                return BadRequest("Error when loading history");
            }
        }

        /// <summary>
        /// 下載包含所有轉錄結果的壓縮檔案
        /// 包括逐字稿、字幕檔、摘要等所有相關檔案
        /// </summary>
        /// <param name="guid">任務的唯一識別碼</param>
        /// <returns>ZIP 壓縮檔案下載</returns>        
        [Authorize] // 需要使用者授權
        [HttpGet] // HTTP GET 方法
        public IActionResult Download(string guid)
        {
            // 驗證使用者身份
            string empNo = NetUtil.GetValidEmpNo(User?.Identity?.Name ?? string.Empty);
            if (empNo == null)
            {
                _logger.LogWarning("Invalid user");
                return BadRequest("Invalid user");
            }

            // 取得使用者 IP 資訊
            (IPAddress? ip, string? hostname) = NetUtil.GetIP_Hostname(HttpContext);
            if (ip == null)
            {
                _logger.LogWarning("Invalid IP");
                return BadRequest("Invalid IP");
            }

            // 記錄下載使用情況
            LogDownloadUsage(guid, empNo, ip, hostname);

            try
            {
                // 儲存使用記錄到資料庫
                _dbContext.SaveChanges();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Save usage log failed");
                return BadRequest("Save usage log failed");
            }

            // 取得該任務的資料目錄和 ZIP 檔案路徑
            string dataDir = GetDataDirectory(empNo, guid);
            string zipFilePath = Path.Combine(dataDir, "archive.zip");

            // 檢查 ZIP 檔案是否存在
            if (System.IO.File.Exists(zipFilePath))
            {
                // 回傳檔案下載
                return PhysicalFile(zipFilePath, "application/zip", "archive.zip");
            }

            // 檔案不存在時回傳 404
            return NotFound("沒有檔案");
        }

        /// <summary>
        /// 記錄使用者下載檔案的使用情況
        /// 用於統計和稽核目的，追蹤哪些使用者下載了哪些檔案
        /// </summary>
        /// <param name="guid">下載檔案的任務識別碼</param>
        /// <param name="empNo">員工編號</param>
        /// <param name="ip">使用者 IP 位址</param>
        /// <param name="hostname">使用者主機名稱</param>
        private void LogDownloadUsage(string guid, string empNo, IPAddress ip, string? hostname)
        {
            // 取得使用者詳細資訊
            (string name, string email, int deptNo, string deptName) = NetUtil.GetDeptEtc(empNo, _configuration);

            // 建立使用記錄物件
            UsageLog usageLog = new UsageLog
            {
                EmpNo = empNo,                    // 員工編號
                Name = name,                      // 員工姓名
                DeptNo = deptNo,                  // 部門編號
                Department = deptName,            // 部門名稱
                IP = ip.ToString(),               // IP 位址
                Hostname = hostname,              // 主機名稱
                CallTime = DateTime.Now,          // 呼叫時間
                APIName = "Download",             // API 名稱
                Remark = guid                     // 備註（存放下載的檔案 GUID）
            };

            // 將使用記錄加入資料庫
            _dbContext.Add(usageLog);
            _logger.LogInformation("Download {guid} called by {EmpNo} ({Name})", guid, empNo, name);
        }

        /// <summary>
        /// 進行語音轉文字處理（背景任務模式）
        /// 支援上傳音視頻檔案，並選擇雲端或地端處理模式
        /// 採用非同步背景處理，避免長時間阻塞使用者介面
        /// </summary>
        /// <param name="file">上傳的音視頻檔案清單</param>
        /// <param name="mode">處理模式：'cloud' 使用雲端 OpenAI API，'local' 使用地端 Whisper API</param>
        /// <returns>任務 ID 和處理狀態，供後續查詢使用</returns>        
        [Authorize] // 需要使用者授權
        [HttpPost] // HTTP POST 方法
        public async Task<IActionResult> Transcribe([FromForm] List<IFormFile> file, [FromForm] string mode)
        {
            // 基本檔案驗證
            if (file == null || file.Count == 0)
            {
                return BadRequest("請提供有效的影像/聲音檔案。");
            }

            // 限制一次只能上傳一個檔案
            if (file.Count > 1)
            {
                return BadRequest("一次僅能上傳一個檔案");
            }

            // 尋找有效的檔案（大小大於 0）
            IFormFile? mediaFile = null;
            foreach (IFormFile formFile in file)
            {
                if (formFile.Length > 0)
                {
                    mediaFile = formFile;
                    break;
                }
            }

            // 確保找到有效檔案
            if (mediaFile == null)
            {
                return BadRequest("請提供有效的影像/聲音檔案。");
            }

            // 使用者身份驗證
            string empNo = NetUtil.GetValidEmpNo(User?.Identity?.Name ?? string.Empty);
            if (empNo == null)
            {
                _logger.LogWarning("Invalid user");
                return BadRequest("Invalid user");
            }

            // IP 位址驗證
            (IPAddress? ip, string? hostname) = NetUtil.GetIP_Hostname(HttpContext);
            if (ip == null)
            {
                _logger.LogWarning("Invalid IP");
                return BadRequest("Invalid IP");
            }

            // 生成唯一的任務 ID 和建立儲存目錄
            Guid taskId = Guid.NewGuid();
            string dataDir = GetDataDirectory(empNo, taskId.ToString());
            string mediaFilePath = Path.Combine(dataDir, mediaFile.FileName); try
            {
                // 將上傳的檔案儲存到伺服器磁碟
                using (var stream = new FileStream(mediaFilePath, FileMode.Create))
                {
                    await mediaFile.CopyToAsync(stream);
                }

                // 取得使用者的詳細資訊（姓名、部門等）
                (string name, string email, int deptNo, string deptName) = NetUtil.GetDeptEtc(empNo, _configuration);

                // 記錄 API 使用情況，用於統計和稽核
                UsageLog usageLog = new UsageLog
                {
                    EmpNo = empNo,
                    Name = name,
                    DeptNo = deptNo,
                    Department = deptName,
                    IP = ip.ToString(),
                    Hostname = hostname,
                    CallTime = DateTime.Now,
                    APIName = "Transcribe",
                    Remark = taskId.ToString()
                };
                _dbContext.Add(usageLog);

                // 建立檔案處理記錄，初始狀態為 Pending（等待處理）
                FileLog fileLog = new FileLog
                {
                    Uid = taskId,                           // 任務唯一識別碼
                    EmpNo = empNo,                          // 員工編號
                    Name = name,                            // 員工姓名
                    DeptNo = deptNo,                        // 部門編號
                    Department = deptName,                  // 部門名稱
                    IP = ip.ToString(),                     // 使用者 IP
                    Hostname = hostname,                    // 主機名稱
                    UploadTime = DateTime.Now,              // 上傳時間
                    FileName = mediaFile.FileName,          // 原始檔案名稱
                    FileSize = mediaFile.Length,            // 檔案大小（位元組）
                    Status = ProcessingStatus.Pending,      // 處理狀態：等待處理
                    IsCompleted = false                     // 尚未完成
                };
                _dbContext.Add(fileLog);

                // 將使用記錄和檔案記錄儲存到資料庫
                await _dbContext.SaveChangesAsync();                // 建立背景處理任務物件
                TranscribeTask task = new TranscribeTask
                {
                    TaskId = taskId,                                    // 任務 ID
                    EmpNo = empNo,                                     // 員工編號
                    MediaFilePath = mediaFilePath,                     // 音視頻檔案完整路徑
                    DataDirectory = dataDir,                           // 資料儲存目錄
                    FileName = mediaFile.FileName,                     // 檔案名稱
                    ProcessingMode = mode == "cloud" ? "cloud" : "local", // 處理模式（雲端或地端）
                    CreatedAt = DateTime.Now,                          // 任務建立時間
                    Priority = 1                                       // 任務優先級（預設為 1）
                };

                // 將任務加入背景處理隊列
                bool enqueued = await _taskQueue.EnqueueTaskAsync(task);
                if (!enqueued)
                {
                    // 如果無法加入隊列，記錄錯誤並回傳伺服器錯誤
                    _logger.LogError("Failed to enqueue transcribe task {TaskId}", taskId);
                    return StatusCode(500, "無法將任務加入處理隊列，請稍後再試。");
                }

                // 記錄任務成功加入隊列的訊息
                _logger.LogInformation("Transcribe task {TaskId} enqueued successfully for user {EmpNo} ({Name})",
                    taskId, empNo, name);

                // 立即回傳任務 ID 和狀態，不等待處理完成
                return Ok(new
                {
                    TaskId = taskId,
                    Message = "檔案上傳成功，正在背景處理中，請稍後查看歷史記錄。",
                    Status = "Processing"
                });
            }
            catch (Exception ex)
            {
                // 記錄錯誤訊息
                _logger.LogError(ex, "Failed to create transcribe task: {Message}", ex.Message);

                // 清理已建立的檔案，避免留下無用的檔案
                try
                {
                    // 刪除已上傳的檔案
                    if (System.IO.File.Exists(mediaFilePath))
                    {
                        System.IO.File.Delete(mediaFilePath);
                    }

                    // 如果目錄為空，則刪除目錄
                    if (Directory.Exists(dataDir) && !Directory.EnumerateFileSystemEntries(dataDir).Any())
                    {
                        Directory.Delete(dataDir);
                    }
                }
                catch
                {
                    // 忽略清理過程中的錯誤，避免影響主要錯誤處理流程
                }

                // 回傳錯誤訊息給使用者
                return StatusCode(500, $"建立轉錄任務時發生錯誤：{ex.Message}");
            }
        }

        /// <summary>
        /// 查詢指定任務的處理狀態
        /// 使用者可透過此 API 查詢上傳檔案的處理進度和結果
        /// </summary>
        /// <param name="taskId">任務的唯一識別碼</param>
        /// <returns>包含任務狀態、進度、錯誤訊息等資訊的 JSON 物件</returns>
        [Authorize] // 需要使用者授權
        [HttpGet] // HTTP GET 方法
        public async Task<IActionResult> GetTaskStatus(Guid taskId)
        {
            // 驗證使用者身份
            string empNo = NetUtil.GetValidEmpNo(User?.Identity?.Name ?? string.Empty);
            if (empNo == null)
            {
                _logger.LogWarning("Invalid user");
                return BadRequest("Invalid user");
            }

            try
            {
                // 從資料庫查詢該使用者的指定任務記錄
                // 限制只能查詢自己的任務，確保資料安全性
                FileLog? fileLog = await _dbContext.FileLogs
                    .Where(f => f.Uid == taskId && f.EmpNo == empNo)
                    .FirstOrDefaultAsync();

                if (fileLog == null)
                {
                    return NotFound("找不到指定的任務");
                }

                // 組織回傳的任務狀態資訊
                var response = new
                {
                    TaskId = taskId,                                    // 任務 ID
                    Status = fileLog.Status.ToString(),                // 處理狀態（字串）
                    FileName = fileLog.FileName,                       // 檔案名稱
                    UploadTime = fileLog.UploadTime,                   // 上傳時間
                    LastProcessedAt = fileLog.LastProcessedAt,         // 最後處理時間
                    IsCompleted = fileLog.IsCompleted,                 // 是否處理完成
                    ErrorMessage = fileLog.ErrorMessage,               // 錯誤訊息（如有）
                    RetryCount = fileLog.RetryCount,                   // 重試次數
                    Progress = GetTaskProgress(fileLog.Status)         // 處理進度百分比
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get task status for {TaskId}: {Message}", taskId, ex.Message);
                return StatusCode(500, "查詢任務狀態時發生錯誤");
            }
        }

        /// <summary>
        /// 查詢指定任務的詳細處理進度
        /// 提供更細緻的進度資訊，包含處理階段、分段進度、預估時間等
        /// </summary>
        /// <param name="taskId">任務的唯一識別碼</param>
        /// <returns>包含詳細進度資訊的 JSON 物件</returns>
        [Authorize] // 需要使用者授權
        [HttpGet] // HTTP GET 方法
        public async Task<IActionResult> GetTaskProgress(Guid taskId)
        {
            // 驗證使用者身份
            string empNo = NetUtil.GetValidEmpNo(User?.Identity?.Name ?? string.Empty);
            if (empNo == null)
            {
                _logger.LogWarning("Invalid user");
                return BadRequest("Invalid user");
            }

            try
            {
                // 檢查任務是否屬於該使用者
                FileLog? fileLog = await _dbContext.FileLogs
                    .Where(f => f.Uid == taskId && f.EmpNo == empNo)
                    .FirstOrDefaultAsync();

                if (fileLog == null)
                {
                    return NotFound("找不到指定的任務");
                }
                // 從_taskQueue 取得指定任務記錄
                TranscribeTask? task = _taskQueue.GetTask(taskId);
                if (task != null)
                {
                    return Ok(task.ProcessPercentage);
                }

                int progressPercentage = GetTaskProgress(fileLog.Status);

                return Ok(progressPercentage);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to get detailed task progress for {TaskId}: {Message}", taskId, ex.Message);
                return StatusCode(500, "查詢任務詳細進度時發生錯誤");
            }
        }

        /// <summary>
        /// 取消指定的處理任務
        /// 只有處於等待中或處理中狀態的任務可以被取消
        /// </summary>
        /// <param name="taskId">要取消的任務 ID</param>
        /// <returns>取消操作的結果訊息</returns>
        [Authorize] // 需要使用者授權
        [HttpPost] // HTTP POST 方法
        public async Task<IActionResult> CancelTask(Guid taskId)
        {
            // 驗證使用者身份
            string empNo = NetUtil.GetValidEmpNo(User?.Identity?.Name ?? string.Empty);
            if (empNo == null)
            {
                _logger.LogWarning("Invalid user");
                return BadRequest("Invalid user");
            }

            try
            {
                // 查詢該使用者的指定任務記錄
                FileLog? fileLog = await _dbContext.FileLogs
                    .Where(f => f.Uid == taskId && f.EmpNo == empNo)
                    .FirstOrDefaultAsync();

                if (fileLog == null)
                {
                    return NotFound("找不到指定的任務");
                }

                // 檢查任務狀態是否允許取消
                // 只有 Pending（等待中）或 Processing（處理中）的任務可以取消
                if (fileLog.Status != ProcessingStatus.Pending && fileLog.Status != ProcessingStatus.Processing)
                {
                    return BadRequest($"任務狀態為 {fileLog.Status}，無法取消");
                }

                // 更新任務狀態為已取消
                fileLog.Status = ProcessingStatus.Cancelled;
                fileLog.LastProcessedAt = DateTime.Now;
                fileLog.ErrorMessage = "用戶取消任務";

                // 儲存變更到資料庫
                await _dbContext.SaveChangesAsync();

                // 記錄取消任務的操作
                _logger.LogInformation("Task {TaskId} cancelled by user {EmpNo}", taskId, empNo);

                return Ok(new { Message = "任務已成功取消", TaskId = taskId });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to cancel task {TaskId}: {Message}", taskId, ex.Message);
                return StatusCode(500, "取消任務時發生錯誤");
            }
        }

        /// <summary>
        /// 根據處理狀態計算任務進度百分比
        /// 用於前端顯示進度條或進度指示器
        /// </summary>
        /// <param name="status">任務的處理狀態</param>
        /// <returns>進度百分比（0-100）</returns>
        private static int GetTaskProgress(ProcessingStatus status)
        {
            return status switch
            {
                ProcessingStatus.Pending => 0,      // 等待處理：0%
                ProcessingStatus.Processing => 50,   // 處理中：50%
                ProcessingStatus.Completed => 100,   // 處理完成：100%
                ProcessingStatus.Failed => 0,        // 處理失敗：0%
                ProcessingStatus.Cancelled => 0,     // 已取消：0%
                _ => 0                               // 未知狀態：0%
            };
        }

        /// <summary>
        /// 取得處理狀態的繁體中文描述
        /// 將英文狀態代碼轉換為使用者友善的中文說明
        /// </summary>
        /// <param name="status">處理狀態枚舉值</param>
        /// <returns>狀態的中文描述字串</returns>
        private static string GetStatusDescription(ProcessingStatus status)
        {
            return status switch
            {
                ProcessingStatus.Pending => "等待處理",      // 任務已建立，等待背景服務處理
                ProcessingStatus.Processing => "處理中",     // 正在進行語音轉文字處理
                ProcessingStatus.Completed => "處理完成",    // 所有處理步驟已完成
                ProcessingStatus.Failed => "處理失敗",       // 處理過程中發生錯誤
                ProcessingStatus.Cancelled => "已取消",      // 使用者主動取消任務
                _ => "未知狀態"                             // 無法識別的狀態
            };
        }



        /// <summary>
        /// 刪除指定的歷史記錄
        /// 刪除資料庫記錄和相關的檔案資料
        /// </summary>
        /// <param name="guid">要刪除的任務唯一識別碼</param>
        /// <returns>刪除操作的結果訊息</returns>
        [Authorize] // 需要使用者授權
        [HttpDelete] // HTTP DELETE 方法
        public async Task<IActionResult> RemoveHistory(Guid guid)
        {
            // 驗證使用者身份
            string empNo = NetUtil.GetValidEmpNo(User?.Identity?.Name ?? string.Empty);
            if (empNo == null)
            {
                _logger.LogWarning("Invalid user");
                return BadRequest("Invalid user");
            }

            // 取得使用者 IP 資訊
            (IPAddress? ip, string? hostname) = NetUtil.GetIP_Hostname(HttpContext);
            if (ip == null)
            {
                _logger.LogWarning("Invalid IP");
                return BadRequest("Invalid IP");
            }

            try
            {
                // 查詢該使用者的指定記錄
                // 限制只能刪除自己的記錄，確保資料安全性
                FileLog? fileLog = await _dbContext.FileLogs
                    .Where(f => f.Uid == guid && f.EmpNo == empNo)
                    .FirstOrDefaultAsync();

                if (fileLog == null)
                {
                    return NotFound("找不到指定的歷史記錄");
                }

                // 取得使用者詳細資訊
                (string name, string email, int deptNo, string deptName) = NetUtil.GetDeptEtc(empNo, _configuration);

                // 記錄刪除操作的使用情況
                UsageLog usageLog = new UsageLog
                {
                    EmpNo = empNo,
                    Name = name,
                    DeptNo = deptNo,
                    Department = deptName,
                    IP = ip.ToString(),
                    Hostname = hostname,
                    CallTime = DateTime.Now,
                    APIName = "RemoveHistory",
                    Remark = $"刪除記錄: {fileLog.FileName} (GUID: {guid})"
                };
                _dbContext.Add(usageLog);

                // 刪除相關檔案
                string dataDir = GetDataDirectory(empNo, guid.ToString());
                if (Directory.Exists(dataDir))
                {
                    try
                    {
                        // 遞迴刪除整個目錄及其內容
                        Directory.Delete(dataDir, true);
                        _logger.LogInformation("Deleted directory: {DataDir}", dataDir);
                    }
                    catch (Exception ex)
                    {
                        // 記錄檔案刪除錯誤，但不影響資料庫記錄的刪除
                        _logger.LogWarning(ex, "Failed to delete directory {DataDir}: {Message}", dataDir, ex.Message);
                    }
                }

                // 從資料庫刪除記錄
                _dbContext.FileLogs.Remove(fileLog);
                await _dbContext.SaveChangesAsync();

                _logger.LogInformation("History record {Guid} removed by user {EmpNo} ({Name})", guid, empNo, name);

                return Ok(new
                {
                    Message = "歷史記錄已成功刪除",
                    Guid = guid,
                    FileName = fileLog.FileName
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to remove history record {Guid}: {Message}", guid, ex.Message);
                return StatusCode(500, "刪除歷史記錄時發生錯誤");
            }
        }
    }
}
