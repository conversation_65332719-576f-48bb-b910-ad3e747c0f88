using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.DependencyInjection;
using MeetingAssistant.Models;
using MeetingUtility.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace MeetingAssistant.Server.Services
{
    /// <summary>
    /// 轉錄任務背景服務
    /// 負責從任務隊列中取得任務並進行處理
    /// 此服務繼承自 BackgroundService，提供長時間運行的背景處理能力
    /// 支援並發處理、任務重試、錯誤處理等功能
    /// </summary>
    public class TranscribeBackgroundService : BackgroundService
    {
        /// <summary>
        /// 轉錄任務隊列介面
        /// 用於從隊列中取得待處理的轉錄任務
        /// </summary>
        private readonly ITranscribeTaskQueue _taskQueue;
        
        /// <summary>
        /// 服務範圍工廠
        /// 用於在背景服務中創建服務範圍以取得 Scoped 服務
        /// </summary>
        private readonly IServiceScopeFactory _serviceScopeFactory;
        
        /// <summary>
        /// 日誌記錄器
        /// 用於記錄服務運行過程中的各種資訊、警告和錯誤
        /// </summary>
        private readonly ILogger<TranscribeBackgroundService> _logger;
        
        /// <summary>
        /// 背景任務配置設定
        /// 包含最大並發數、輪詢間隔、任務超時時間等設定
        /// </summary>
        private readonly BackgroundTaskConfig _config;

        /// <summary>
        /// 建構函數 - 初始化轉錄背景服務的所有依賴項目
        /// </summary>
        /// <param name="taskQueue">任務隊列服務，用於管理待處理的轉錄任務</param>
        /// <param name="serviceScopeFactory">服務範圍工廠，用於創建服務範圍以取得 Scoped 服務</param>
        /// <param name="logger">日誌記錄器，用於記錄服務執行過程</param>
        /// <param name="config">背景任務配置，包含服務運行的各項參數設定</param>
        /// <exception cref="ArgumentNullException">當任何必要的依賴項目為 null 時拋出</exception>
        public TranscribeBackgroundService(
            ITranscribeTaskQueue taskQueue,
            IServiceScopeFactory serviceScopeFactory,
            ILogger<TranscribeBackgroundService> logger,
            IOptions<BackgroundTaskConfig> config)
        {
            // 驗證所有依賴項目都不為 null，確保服務能正常運作
            _taskQueue = taskQueue ?? throw new ArgumentNullException(nameof(taskQueue));
            _serviceScopeFactory = serviceScopeFactory ?? throw new ArgumentNullException(nameof(serviceScopeFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));

            // 記錄服務初始化完成的相關配置資訊
            _logger.LogInformation("TranscribeBackgroundService 已初始化，並發處理數：{MaxConcurrentTasks}, 輪詢間隔：{PollingIntervalMs}ms", 
                _config.MaxConcurrentTasks, _config.PollingIntervalMs);
        }

        /// <summary>
        /// 背景服務主要執行邏輯
        /// 此方法會持續運行直到服務被停止，負責：
        /// 1. 管理並發任務處理的信號量控制
        /// 2. 從任務隊列中持續取得新任務
        /// 3. 啟動任務處理並管理活動任務列表
        /// 4. 處理各種異常情況並確保服務穩定運行
        /// </summary>
        /// <param name="stoppingToken">停止權杖，當服務需要停止時會被觸發</param>
        /// <returns>非同步任務，代表背景服務的執行生命週期</returns>
        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("TranscribeBackgroundService 開始執行");

            try
            {
                // 【並發控制區塊】
                // 建立信號量來限制同時處理的任務數量，避免系統資源過載
                using var semaphore = new SemaphoreSlim(_config.MaxConcurrentTasks, _config.MaxConcurrentTasks);
                
                // 維護所有正在進行中任務的列表，用於追蹤和清理
                var activeTasks = new List<Task>();

                // 【主要處理迴圈】
                // 持續運行直到收到停止信號
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        // 【任務清理區塊】
                        // 定期清理已完成的任務以避免記憶體洩漏
                        CleanupCompletedTasks(activeTasks);

                        // 【並發控制區塊】
                        // 等待可用的處理槽位，如果已達到最大並發數則會阻塞
                        await semaphore.WaitAsync(stoppingToken);

                        try
                        {
                            // 【任務取得區塊】
                            // 嘗試從隊列中取得下一個待處理的任務
                            var task = await _taskQueue.DequeueTaskAsync(stoppingToken);
                            
                            if (task != null)
                            {
                                // 【任務處理啟動區塊】
                                // 記錄取得的任務資訊
                                _logger.LogDebug("取得任務：{TaskId}，優先級：{Priority}", task.TaskId, task.Priority);

                                // 啟動任務處理（非同步，不等待完成）
                                // 這樣可以讓主迴圈繼續處理其他任務
                                var processingTask = ProcessTaskWithRetryAsync(task, semaphore, stoppingToken);
                                activeTasks.Add(processingTask);
                            }
                            else
                            {
                                // 【空任務處理區塊】
                                // 沒有任務時釋放信號量並等待
                                semaphore.Release();
                                
                                // 等待指定的輪詢間隔時間後再次檢查隊列
                                await Task.Delay(_config.PollingIntervalMs, stoppingToken);
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "處理任務時發生錯誤，將釋放信號量");
                            semaphore.Release(); // 在這裡釋放，因為 WaitAsync 已成功
                            await Task.Delay(5000, stoppingToken); // 錯誤後延遲
                        }
                    }
                    catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                    {
                        // 【正常停止處理區塊】
                        _logger.LogInformation("背景服務接收到取消請求");
                        break;
                    }
                    catch (Exception ex)
                    {
                        // 【異常處理區塊】
                        // 這個 catch 現在只處理 WaitAsync 之前或 WaitAsync 本身的錯誤
                        _logger.LogError(ex, "背景服務在等待任務時發生嚴重錯誤");
                        
                        // 發生錯誤時等待一段時間再繼續，避免連續錯誤造成資源浪費
                        try
                        {
                            await Task.Delay(5000, stoppingToken);
                        }
                        catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
                        {
                            break;
                        }
                    }
                }

                // 【服務停止清理區塊】
                // 等待所有進行中的任務完成，確保優雅關閉
                await WaitForActiveTasks(activeTasks, stoppingToken);
            }
            catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
            {
                _logger.LogInformation("TranscribeBackgroundService 已停止");
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "TranscribeBackgroundService 發生嚴重錯誤");
                throw;
            }
        }

        /// <summary>
        /// 處理任務並實施重試策略
        /// 此方法負責執行單一轉錄任務的完整生命週期，包括：
        /// 1. 任務超時控制
        /// 2. 任務狀態更新
        /// 3. 執行實際處理邏輯
        /// 4. 處理各種異常情況
        /// 5. 確保資源正確釋放
        /// </summary>
        /// <param name="task">要處理的轉錄任務物件</param>
        /// <param name="semaphore">並發控制信號量，處理完成後需要釋放</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        /// <returns>非同步任務，代表任務處理的完整過程</returns>
        private async Task ProcessTaskWithRetryAsync(TranscribeTask task, SemaphoreSlim semaphore, CancellationToken stoppingToken)
        {
            // 超時控制權杖來源，用於管理任務執行時間限制
            CancellationTokenSource? timeoutCts = null;

            try
            {
                // 【超時控制區塊】
                // 建立連結的取消權杖，結合服務停止和任務超時控制
                timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken);
                timeoutCts.CancelAfter(TimeSpan.FromMinutes(_config.TaskTimeoutMinutes));

                _logger.LogInformation("開始處理任務：{TaskId}，檔案：{FileName}", task.TaskId, task.FileName);

                // 【任務狀態更新區塊】
                // 將任務標記為處理中狀態，避免被其他處理器重複處理
                await _taskQueue.MarkTaskAsProcessingAsync(task.TaskId);

                // 【任務執行區塊】
                // 執行實際的轉錄處理邏輯，包含超時控制
                var result = await ExecuteTaskWithTimeout(task, timeoutCts.Token, stoppingToken);
                
                // 【結果處理區塊】
                // 根據處理結果決定後續動作（完成或重試）
                await HandleTaskResult(task, result, stoppingToken);
            }
            catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
            {
                // 【服務停止處理區塊】
                _logger.LogInformation("任務 {TaskId} 因服務停止而取消", task.TaskId);
                await HandleCancelledTask(task, stoppingToken);
            }
            catch (Exception ex)
            {
                // 【未預期錯誤處理區塊】
                _logger.LogError(ex, "處理任務 {TaskId} 時發生未預期的錯誤", task.TaskId);
                await HandleUnexpectedError(task, ex, stoppingToken);
            }
            finally
            {
                // 【資源清理區塊】
                // 確保超時控制權杖被正確釋放
                timeoutCts?.Dispose();
                
                // 確保信號量被正確釋放，避免死鎖
                try
                {
                    semaphore.Release();
                }
                catch (ObjectDisposedException)
                {
                    // 信號量已被釋放，忽略此異常
                }
            }
        }

        /// <summary>
        /// 執行任務並處理超時情況
        /// 此方法包裝實際的任務處理邏輯，提供超時控制機制
        /// 當任務執行時間超過設定限制時，會自動取消並回傳失敗結果
        /// </summary>
        /// <param name="task">要執行的轉錄任務</param>
        /// <param name="timeoutToken">超時控制權杖</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        /// <returns>任務處理結果，包含成功/失敗狀態和相關資訊</returns>
        private async Task<TranscribeTaskResult> ExecuteTaskWithTimeout(TranscribeTask task, CancellationToken timeoutToken, CancellationToken stoppingToken)
        {
            try
            {
                // 【實際處理區塊】
                // 使用服務範圍工廠創建範圍以取得 Scoped 服務
                using var scope = _serviceScopeFactory.CreateScope();
                var taskProcessor = scope.ServiceProvider.GetRequiredService<ITranscribeTaskProcessor>();
                
                // 委託給任務處理器執行實際的轉錄邏輯
                return await taskProcessor.ProcessTaskAsync(task, timeoutToken);
            }
            catch (OperationCanceledException) when (timeoutToken.IsCancellationRequested && !stoppingToken.IsCancellationRequested)
            {
                // 【超時處理區塊】
                // 任務因超時而被取消，記錄警告並建立失敗結果
                _logger.LogWarning("任務 {TaskId} 處理逾時，已取消", task.TaskId);
                
                return TranscribeTaskResult.Failure(
                    task.TaskId,
                    $"任務處理逾時（超過 {_config.TaskTimeoutMinutes} 分鐘）",
                    "TranscribeBackgroundService",
                    "BackgroundService",
                    false,
                    0);
            }
        }

        /// <summary>
        /// 處理任務執行結果
        /// 根據任務處理的成功或失敗狀態，決定後續的處理流程
        /// 成功時標記任務為完成，失敗時則進入重試處理邏輯
        /// </summary>
        /// <param name="task">已處理的轉錄任務</param>
        /// <param name="result">任務處理結果，包含成功/失敗狀態和詳細資訊</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        /// <returns>非同步任務，代表結果處理的完成</returns>
        private async Task HandleTaskResult(TranscribeTask task, TranscribeTaskResult result, CancellationToken stoppingToken)
        {
            if (result.IsSuccess)
            {
                // 【成功處理區塊】
                // 記錄任務成功完成的資訊，包含處理時間
                _logger.LogInformation("任務 {TaskId} 處理完成，耗時：{Duration}ms", 
                    task.TaskId, result.ProcessingDurationMs);
                
                // 將任務標記為已完成狀態
                await _taskQueue.MarkTaskAsCompletedAsync(task.TaskId);
            }
            else
            {
                // 【失敗處理區塊】
                // 進入失敗任務的處理流程，包含重試邏輯
                HandleFailedTask(task, result, stoppingToken);
            }
        }

        /// <summary>
        /// 處理失敗的任務
        /// 根據重試策略決定任務是否需要重試或標記為最終失敗
        /// 會檢查重試次數限制和重試策略的建議
        /// </summary>
        /// <param name="task">失敗的轉錄任務</param>
        /// <param name="result">失敗的任務處理結果</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        private void HandleFailedTask(TranscribeTask task, TranscribeTaskResult result, CancellationToken stoppingToken)
        {
            // 【重試決策區塊】
            // 使用服務範圍工廠取得重試策略服務
            using var scope = _serviceScopeFactory.CreateScope();
            var retryPolicy = scope.ServiceProvider.GetRequiredService<ITaskRetryPolicy>();
            
            // 根據重試策略和錯誤訊息決定是否應該重試
            var shouldRetry = retryPolicy.ShouldRetry(task, result.ErrorMessage ?? "Unknown error");
            
            // 檢查是否應該重試且尚未超過最大重試次數
            if (shouldRetry && task.RetryCount < task.MaxRetryCount)
            {
                // 【安排重試區塊】
                // 安排任務重試，包含延遲重新排隊
                ScheduleRetry(task, retryPolicy, stoppingToken);
            }
            else
            {
                // 【最終失敗處理區塊】
                // 任務已達到最大重試次數或不適合重試，記錄最終失敗
                _logger.LogError("任務 {TaskId} 最終失敗，不再重試。錯誤：{Error}", 
                    task.TaskId, result.ErrorMessage);
            }
        }

        /// <summary>
        /// 安排任務重試
        /// 計算重試延遲時間並更新任務的重試計數
        /// 使用非同步方式安排延遲重新排隊，避免阻塞主處理流程
        /// </summary>
        /// <param name="task">需要重試的轉錄任務</param>
        /// <param name="retryPolicy">重試策略實例</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        private void ScheduleRetry(TranscribeTask task, ITaskRetryPolicy retryPolicy, CancellationToken stoppingToken)
        {
            // 【重試延遲計算區塊】
            // 根據當前重試次數計算延遲時間（通常會遞增）
            var retryDelay = retryPolicy.CalculateRetryDelaySeconds(task.RetryCount);
            
            // 記錄重試安排的相關資訊
            _logger.LogWarning("任務 {TaskId} 處理失敗，將在 {RetryDelay} 秒後重試。重試次數：{RetryCount}", 
                task.TaskId, retryDelay, task.RetryCount + 1);
            
            // 【重試計數更新區塊】
            // 增加任務的重試計數
            task.RetryCount++;
            
            // 【延遲重新排隊區塊】
            // 使用 Fire-and-Forget 模式啟動延遲重新排隊
            // 這樣不會阻塞當前的處理流程
            _ = DelayedRequeue(task, retryDelay, stoppingToken);
        }

        /// <summary>
        /// 延遲重新排隊任務
        /// 實現任務重試的延遲機制，等待指定時間後將任務重新加入處理隊列
        /// 此方法使用非同步方式執行，不會阻塞主要處理流程
        /// </summary>
        /// <param name="task">需要重新排隊的轉錄任務</param>
        /// <param name="delaySeconds">延遲時間（秒）</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        /// <returns>非同步任務，代表延遲重新排隊的完成</returns>
        private async Task DelayedRequeue(TranscribeTask task, int delaySeconds, CancellationToken stoppingToken)
        {
            try
            {
                // 【延遲等待區塊】
                // 等待指定的延遲時間，實現退避重試機制
                await Task.Delay(TimeSpan.FromSeconds(delaySeconds), stoppingToken);
                
                // 【重新排隊區塊】
                // 確認服務尚未停止才執行重新排隊
                if (!stoppingToken.IsCancellationRequested)
                {
                    await _taskQueue.RequeueFailedTaskAsync(task, stoppingToken);
                }
            }
            catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
            {
                // 【取消處理區塊】
                // 服務停止時的正常取消，記錄除錯資訊
                _logger.LogDebug("重試任務 {TaskId} 被取消", task.TaskId);
            }
            catch (Exception ex)
            {
                // 【重新排隊失敗處理區塊】
                // 重新排隊過程中發生錯誤，記錄錯誤資訊
                _logger.LogError(ex, "重新排隊任務 {TaskId} 時發生錯誤", task.TaskId);
            }
        }

        /// <summary>
        /// 處理被取消的任務
        /// 當任務因為服務停止而被取消時，嘗試將任務重新排隊以便後續處理
        /// 這確保了任務不會因為服務重啟而遺失
        /// </summary>
        /// <param name="task">被取消的轉錄任務</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        /// <returns>非同步任務，代表取消處理的完成</returns>
        private async Task HandleCancelledTask(TranscribeTask task, CancellationToken stoppingToken)
        {
            try
            {
                // 【重新排隊區塊】
                // 只有在服務尚未完全停止時才嘗試重新排隊
                if (!stoppingToken.IsCancellationRequested)
                {
                    await _taskQueue.RequeueFailedTaskAsync(task, stoppingToken);
                }
            }
            catch (Exception ex)
            {
                // 【重新排隊失敗處理區塊】
                // 無法重新排隊已取消的任務，記錄警告但不影響服務運行
                _logger.LogWarning(ex, "無法重新排隊已取消的任務 {TaskId}", task.TaskId);
            }
        }

        /// <summary>
        /// 處理未預期的錯誤
        /// 當任務處理過程中發生預期外的異常時，記錄錯誤並嘗試重新排隊任務
        /// 但會限制重試次數以避免無限循環重試
        /// </summary>
        /// <param name="task">發生錯誤的轉錄任務</param>
        /// <param name="ex">發生的異常物件</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        /// <returns>非同步任務，代表錯誤處理的完成</returns>
        private async Task HandleUnexpectedError(TranscribeTask task, Exception ex, CancellationToken stoppingToken)
        {
            try
            {
                // 【錯誤記錄區塊】
                // 記錄未預期錯誤的詳細資訊
                _logger.LogError("任務 {TaskId} 處理失敗，錯誤：{Error}", task.TaskId, ex.Message);
                
                // 【有限重試區塊】
                // 對於未預期錯誤，仍然給予重試機會，但要檢查重試次數限制
                if (task.RetryCount < task.MaxRetryCount)
                {
                    // 增加重試計數並重新排隊
                    task.RetryCount++;
                    await _taskQueue.RequeueFailedTaskAsync(task, stoppingToken);
                }
            }
            catch (Exception retryEx)
            {
                // 【重試失敗處理區塊】
                // 連重新排隊都失敗，記錄嚴重錯誤但不影響服務運行
                _logger.LogError(retryEx, "處理任務 {TaskId} 未預期錯誤後的重試失敗", task.TaskId);
            }
        }

        /// <summary>
        /// 清理已完成的任務以防止記憶體洩漏
        /// 定期掃描活動任務列表，移除已完成的任務以釋放記憶體
        /// 同時確保已完成任務的異常狀態被正確觀察和處理
        /// </summary>
        /// <param name="activeTasks">目前所有活動任務的列表</param>
        private static void CleanupCompletedTasks(List<Task> activeTasks)
        {
            // 【反向遍歷區塊】
            // 從列表末尾開始遍歷，避免移除元素時索引錯亂
            for (int i = activeTasks.Count - 1; i >= 0; i--)
            {
                if (activeTasks[i].IsCompleted)
                {
                    // 【任務移除區塊】
                    // 取得已完成的任務並從列表中移除
                    var task = activeTasks[i];
                    activeTasks.RemoveAt(i);

                    // 【異常觀察區塊】
                    // 確保失敗任務的異常被觀察到，避免未觀察異常警告
                    // 實際的異常處理已經在 ProcessTaskWithRetryAsync 中完成
                    if (task.IsFaulted && task.Exception != null)
                    {
                        // 透過存取 Exception 屬性來標記異常已被觀察
                        _ = task.Exception;
                    }
                }
            }
        }

        /// <summary>
        /// 等待所有活動任務完成
        /// 在服務停止時確保所有進行中的任務都能優雅地完成
        /// 提供超時機制避免無限期等待，確保服務能在合理時間內停止
        /// </summary>
        /// <param name="activeTasks">目前所有活動任務的列表</param>
        /// <param name="stoppingToken">服務停止權杖</param>
        /// <returns>非同步任務，代表等待所有任務完成的過程</returns>
        private async Task WaitForActiveTasks(List<Task> activeTasks, CancellationToken stoppingToken)
        {
            // 【空任務檢查區塊】
            // 如果沒有活動任務，直接返回
            if (activeTasks.Count == 0) return;

            _logger.LogInformation("等待 {TaskCount} 個進行中的任務完成", activeTasks.Count);

            try
            {
                // 【超時控制區塊】
                // 建立一個有時間限制的取消權杖，避免無限期等待
                using var timeoutCts = CancellationTokenSource.CreateLinkedTokenSource(stoppingToken);
                timeoutCts.CancelAfter(TimeSpan.FromSeconds(_config.GracefulShutdownTimeoutSeconds)); // 使用設定值

                // 【任務等待區塊】
                // 等待所有任務完成，但受到超時限制
                await Task.WhenAll(activeTasks).WaitAsync(timeoutCts.Token);
                _logger.LogInformation("所有任務已完成");
            }
            catch (OperationCanceledException) when (stoppingToken.IsCancellationRequested)
            {
                // 【強制停止處理區塊】
                // 服務被要求立即停止，不再等待任務完成
                _logger.LogWarning("服務停止，強制結束等待任務完成");
            }
            catch (Exception ex)
            {
                // 【等待失敗處理區塊】
                // 等待過程中發生其他錯誤
                _logger.LogError(ex, "等待任務完成時發生錯誤");
            }
        }

        /// <summary>
        /// 服務停止時的清理工作
        /// 覆寫基類的停止方法，提供自訂的停止邏輯和日誌記錄
        /// 確保服務能夠優雅地停止並記錄相關資訊
        /// </summary>
        /// <param name="cancellationToken">取消權杖，用於控制停止操作的超時</param>
        /// <returns>非同步任務，代表服務停止過程的完成</returns>
        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            // 【停止開始記錄區塊】
            _logger.LogInformation("TranscribeBackgroundService 正在停止...");
            
            // 【基類停止呼叫區塊】
            // 呼叫基類的停止方法，執行標準的背景服務停止流程
            await base.StopAsync(cancellationToken);
            
            // 【停止完成記錄區塊】
            _logger.LogInformation("TranscribeBackgroundService 已停止");
        }
    }
}
