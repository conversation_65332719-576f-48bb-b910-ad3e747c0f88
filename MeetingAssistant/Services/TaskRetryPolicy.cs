using System;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MeetingAssistant.Models;
using MeetingUtility.Interfaces;

namespace MeetingAssistant.Services
{
    /// <summary>
    /// 任務重試策略實作 - 使用指數退避算法
    /// </summary>
    public class TaskRetryPolicy : ITaskRetryPolicy
    {
        private readonly ILogger<TaskRetryPolicy> _logger;
        private readonly BackgroundTaskConfig _config;
        private readonly Random _random;
        private readonly RetryPolicyConfiguration _policyConfig;

        public TaskRetryPolicy(ILogger<TaskRetryPolicy> logger, IOptions<BackgroundTaskConfig> config)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));
            _random = new Random();
            _policyConfig = CreateDefaultConfiguration();
        }

        /// <summary>
        /// 策略名稱
        /// </summary>
        public string PolicyName => _policyConfig.PolicyName;

        /// <summary>
        /// 預設最大重試次數
        /// </summary>
        public int DefaultMaxRetryCount => _config.DefaultMaxRetryCount;

        /// <summary>
        /// 最大重試延遲時間 (秒)
        /// </summary>
        public int MaxRetryDelaySeconds => _config.MaxRetryIntervalMinutes * 60;

        /// <summary>
        /// 判斷任務是否應該重試
        /// </summary>
        public bool ShouldRetry(TranscribeTask task, string errorMessage, Exception? exception = null)
        {
            if (task == null)
            {
                _logger.LogWarning("任務為 null，無法判斷重試條件");
                return false;
            }

            // 檢查是否已達到最大重試次數
            if (HasExceededMaxRetries(task))
            {
                _logger.LogInformation("任務 {TaskId} 已達到最大重試次數 {MaxRetryCount}，不再重試", task.TaskId, task.MaxRetryCount);
                return false;
            }

            // 檢查是否為暫時性錯誤
            bool isTransient = IsTransientError(errorMessage, exception);
            
            _logger.LogInformation("任務 {TaskId} 重試判斷: 當前重試次數={RetryCount}, 是否暫時性錯誤={IsTransient}, 錯誤訊息={ErrorMessage}", 
                task.TaskId, task.RetryCount, isTransient, errorMessage);

            return isTransient;
        }

        /// <summary>
        /// 計算下次重試的延遲時間 (使用指數退避 + 隨機抖動)
        /// </summary>
        public int CalculateRetryDelaySeconds(int retryCount, int baseDelaySeconds = 30)
        {
            if (retryCount < 0) retryCount = 0;
            if (baseDelaySeconds <= 0) baseDelaySeconds = _config.RetryIntervalBaseSeconds;

            int delaySeconds;

            if (_policyConfig.UseExponentialBackoff)
            {
                // 指數退避: baseDelay * (multiplier ^ retryCount)
                double exponentialDelay = baseDelaySeconds * Math.Pow(_policyConfig.ExponentialBackoffMultiplier, retryCount);
                delaySeconds = (int)Math.Min(exponentialDelay, MaxRetryDelaySeconds);
            }
            else
            {
                // 線性增長
                delaySeconds = Math.Min(baseDelaySeconds * (retryCount + 1), MaxRetryDelaySeconds);
            }            // 加入隨機抖動以避免雷群效應
            if (_policyConfig.UseJitter)
            {
                double jitterRange = delaySeconds * _policyConfig.JitterRange;
                double jitter = (_random.NextDouble() - 0.5) * 2 * jitterRange; // -jitterRange 到 +jitterRange
                delaySeconds = Math.Max(baseDelaySeconds, Math.Min(MaxRetryDelaySeconds, (int)(delaySeconds + jitter)));
            }

            _logger.LogDebug("計算重試延遲: 重試次數={RetryCount}, 基礎延遲={BaseDelay}秒, 計算結果={DelaySeconds}秒", 
                retryCount, baseDelaySeconds, delaySeconds);

            return delaySeconds;
        }

        /// <summary>
        /// 判斷錯誤是否為暫時性錯誤 (可重試)
        /// </summary>
        public bool IsTransientError(string errorMessage, Exception? exception = null)
        {
            if (string.IsNullOrWhiteSpace(errorMessage))
            {
                return false;
            }

            var lowerErrorMessage = errorMessage.ToLowerInvariant();

            // 檢查永久性錯誤關鍵字 (不應重試的錯誤)
            if (_policyConfig.PermanentErrorKeywords.Any(keyword => lowerErrorMessage.Contains(keyword.ToLowerInvariant())))
            {
                _logger.LogDebug("檢測到永久性錯誤，不應重試: {ErrorMessage}", errorMessage);
                return false;
            }

            // 檢查暫時性錯誤關鍵字 (可重試的錯誤)
            bool isTransient = _policyConfig.TransientErrorKeywords.Any(keyword => lowerErrorMessage.Contains(keyword.ToLowerInvariant()));

            // 特殊處理 HTTP 狀態碼
            if (exception is HttpRequestException httpEx)
            {
                isTransient = IsTransientHttpError(httpEx);
            }            // 特殊處理網路相關例外
            if (exception is TaskCanceledException or TimeoutException)
            {
                isTransient = true;
            }

            _logger.LogDebug("暫時性錯誤判斷結果: {IsTransient}, 錯誤訊息: {ErrorMessage}", isTransient, errorMessage);
            
            return isTransient;
        }

        /// <summary>
        /// 更新任務的重試資訊
        /// </summary>
        public TranscribeTask UpdateTaskForRetry(TranscribeTask task, string errorMessage, Exception? exception = null)
        {
            if (task == null)
            {
                throw new ArgumentNullException(nameof(task));
            }

            task.RetryCount++;
            
            _logger.LogInformation("更新任務 {TaskId} 重試資訊: 重試次數={RetryCount}, 錯誤={ErrorMessage}", 
                task.TaskId, task.RetryCount, errorMessage);

            return task;
        }

        /// <summary>
        /// 檢查任務是否已達到最大重試次數
        /// </summary>
        public bool HasExceededMaxRetries(TranscribeTask task)
        {
            if (task == null) return true;
            
            return task.RetryCount >= task.MaxRetryCount;
        }

        /// <summary>
        /// 取得重試策略的設定資訊
        /// </summary>
        public RetryPolicyConfiguration GetConfiguration()
        {
            return _policyConfig;
        }

        /// <summary>
        /// 建立預設重試策略設定
        /// </summary>
        private RetryPolicyConfiguration CreateDefaultConfiguration()
        {
            return new RetryPolicyConfiguration
            {
                PolicyName = "ExponentialBackoffWithJitter",
                DefaultMaxRetryCount = _config.DefaultMaxRetryCount,
                BaseRetryDelaySeconds = _config.RetryIntervalBaseSeconds,
                MaxRetryDelaySeconds = _config.MaxRetryIntervalMinutes * 60,
                UseExponentialBackoff = true,
                ExponentialBackoffMultiplier = 2.0,
                UseJitter = true,
                JitterRange = 0.1,
                TransientErrorKeywords = new[]
                {
                    // 網路相關錯誤
                    "timeout", "network", "connection", "socket", "dns",
                    // API 相關錯誤
                    "rate limit", "throttle", "quota", "unavailable", "busy",
                    // HTTP 狀態碼
                    "429", "502", "503", "504", "500",
                    // OpenAI API 特定錯誤
                    "server error", "internal error", "temporary",
                    // 地端 Whisper 相關錯誤
                    "whisper api", "local whisper", "model loading"
                },
                PermanentErrorKeywords = new[]
                {
                    // 檔案相關錯誤
                    "invalid file", "unsupported format", "file not found", "corrupted",
                    "file too large", "empty file", "invalid audio",
                    // 認證授權錯誤
                    "unauthorized", "forbidden", "invalid api key", "authentication",
                    "401", "403", "400",
                    // 業務邏輯錯誤
                    "invalid parameter", "bad request", "validation error",
                    "insufficient balance", "quota exceeded permanently"
                }
            };
        }        /// <summary>
        /// 判斷 HTTP 錯誤是否為暫時性錯誤
        /// </summary>
        private bool IsTransientHttpError(HttpRequestException httpEx)
        {
            var message = httpEx.Message?.ToLowerInvariant() ?? string.Empty;
            
            // 檢查 HTTP 狀態碼相關錯誤訊息
            if (message.Contains("429") || // Too Many Requests
                message.Contains("502") || // Bad Gateway
                message.Contains("503") || // Service Unavailable
                message.Contains("504") || // Gateway Timeout
                message.Contains("500"))   // Internal Server Error
            {
                return true;
            }

            // 檢查連線相關錯誤
            if (message.Contains("timeout") ||
                message.Contains("connection") ||
                message.Contains("network"))
            {
                return true;
            }

            return false;
        }
    }
}
