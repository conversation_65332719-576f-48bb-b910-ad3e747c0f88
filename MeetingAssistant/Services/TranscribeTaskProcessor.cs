using System;
using System.IO;
using System.IO.Compression;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading;
using System.Threading.Tasks;

using MeetingAssistant.Models;

using MeetingUtility;
using MeetingUtility.Interfaces;
using MeetingUtility.Processors;

using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

using Newtonsoft.Json;

namespace MeetingAssistant.Server.Services
{
    /// <summary>
    /// 轉錄任務處理器實作
    /// 負責執行實際的轉錄處理邏輯，包含：
    /// 1. 移植現有 Transcribe 邏輯
    /// 2. 狀態更新機制  
    /// 3. 錯誤處理
    /// 4. 支援雲端和地端處理模式
    /// 5. 檔案格式驗證和大小限制
    /// </summary>
    public class TranscribeTaskProcessor : ITranscribeTaskProcessor
    {
        /// <summary>
        /// 設定檔服務
        /// </summary>
        private readonly IConfiguration _configuration;

        /// <summary>
        /// 資料庫上下文
        /// </summary>
        private readonly FileLogDbContext _dbContext;

        /// <summary>
        /// 日誌記錄器
        /// </summary>
        private readonly ILogger<TranscribeTaskProcessor> _logger;



        /// <summary>
        /// OpenAI API 金鑰
        /// </summary>
        private readonly string _openAiApiKey;

        /// <summary>
        /// 地端 Whisper API URL
        /// </summary>
        private readonly string _onPremisesApiUrl;

        /// <summary>
        /// 檔案儲存目錄
        /// </summary>
        private readonly string _fileDir;

        /// <summary>
        /// 支援的音檔格式
        /// </summary>
        private static readonly string[] SupportedFormats = 
        {
            "mp3", "mp4", "mpeg", "mpga", "m4a", "ogg", "wav", "webm"
        };

        /// <summary>
        /// 最大檔案大小 (4GB)
        /// </summary>
        private const long MaxFileSizeBytes = 4L * 1024 * 1024 * 1024;

        /// <summary>
        /// 處理器名稱
        /// </summary>
        public string ProcessorName => "TranscribeTaskProcessor";

        /// <summary>
        /// 處理器類型 (根據設定決定) 預設 local
        /// </summary>
        public string ProcessorType { get; private set; } = "local";

        /// <summary>
        /// 建構函數
        /// </summary>
        /// <param name="configuration">設定檔服務</param>
        /// <param name="dbContext">資料庫上下文</param>
        /// <param name="logger">日誌記錄器</param>
        /// <exception cref="ArgumentNullException">當必要的依賴項目為 null 時拋出</exception>
        /// <exception cref="InvalidOperationException">當必要的設定值缺失時拋出</exception>
        public TranscribeTaskProcessor(
            IConfiguration configuration,
            FileLogDbContext dbContext,
            ILogger<TranscribeTaskProcessor> logger)
        {
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // 初始化設定值
            _openAiApiKey = _configuration["APIKey:OPENAI_API_KEY"] 
                ?? throw new InvalidOperationException("OpenAI API Key 未設定");

            _onPremisesApiUrl = _configuration["OnPremisesApiUrl"] 
                ?? "http://10.10.15.152:22434/transcribe_full";

            _fileDir = _configuration["FileDir"] ?? Path.GetTempPath();

            _logger.LogInformation("TranscribeTaskProcessor 已初始化，檔案目錄：{FileDir}，地端API：{OnPremisesUrl}", 
                _fileDir, _onPremisesApiUrl);
        }

        /// <summary>
        /// 處理轉錄任務
        /// 這是核心處理方法，執行完整的轉錄流程
        /// </summary>
        /// <param name="task">要處理的任務</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>任務處理結果</returns>
        public async Task<TranscribeTaskResult> ProcessTaskAsync(TranscribeTask task, CancellationToken cancellationToken = default)
        {
            if(task == null)
            {
                //throw new ArgumentNullException(nameof(task), "轉錄任務不能為 null");

                return TranscribeTaskResult.Failure(
                        Guid.Empty,
                        "轉錄任務為 null",
                        ProcessorName,
                        ProcessorType,
                        false);
            }
            var startTime = DateTime.Now;
            _logger.LogInformation("開始處理轉錄任務：{TaskId}，檔案：{FileName}，模式：{Mode}", 
                task.TaskId, task.FileName, task.ProcessingMode);

            try
            {
                // 【前置檢查區塊】
                // 檢查檔案是否存在
                if (!File.Exists(task.MediaFilePath))
                {
                    return TranscribeTaskResult.Failure(
                        task.TaskId,
                        $"媒體檔案不存在：{task.MediaFilePath}",
                        ProcessorName,
                        ProcessorType,
                        false); // 檔案不存在不需要重試
                }

                // 檢查資料目錄是否存在，不存在則建立
                if (!Directory.Exists(task.DataDirectory))
                {
                    Directory.CreateDirectory(task.DataDirectory);
                }

                // 【狀態更新區塊】
                // 更新 FileLog 狀態為處理中
                await UpdateFileLogStatusAsync(task.TaskId, ProcessingStatus.Processing, cancellationToken);

                // 【轉錄處理區塊】
                Transcribe? transcribe = null;
                bool isCloudMode = task.ProcessingMode?.ToLower() == "cloud";
                

                
                if (isCloudMode)
                {
                    _logger.LogDebug("使用雲端 OpenAI Whisper API 處理任務：{TaskId}", task.TaskId);
                    transcribe = await ProcessWithCloudApiAsync(task, task.MediaFilePath, task.DataDirectory, cancellationToken);
                    ProcessorType = "cloud";
                }
                else
                {
                    _logger.LogDebug("使用地端 Whisper API 處理任務：{TaskId}", task.TaskId);
                    transcribe = await ProcessWithOnPremisesApiWithProgressAsync(task, task.MediaFilePath, task.DataDirectory, cancellationToken);
                    ProcessorType = "local";
                }

                if (transcribe == null)
                {
                    return TranscribeTaskResult.Failure(
                        task.TaskId,
                        "轉錄處理回傳空結果",
                        ProcessorName,
                        ProcessorType,
                        true); // 可能是暫時性錯誤，允許重試
                }

                // 【檔案處理區塊】
                // 設定轉錄結果的基本資訊
                transcribe.Guid = task.TaskId;
                transcribe.FileName = task.FileName;

                // 儲存轉錄結果到檔案
                await SaveTranscribeResultsAsync(task, transcribe, cancellationToken);

                // 【狀態更新區塊】
                // 更新 FileLog 狀態為完成並儲存相關資訊
                await UpdateFileLogWithResultsAsync(task.TaskId, transcribe, startTime, cancellationToken);

                var endTime = DateTime.Now;
                _logger.LogInformation("轉錄任務 {TaskId} 處理完成，耗時：{Duration}ms", 
                    task.TaskId, (endTime - startTime).TotalMilliseconds);

                return TranscribeTaskResult.Success(
                    task.TaskId,
                    transcribe.Transcript ?? string.Empty,
                    ProcessorName,
                    ProcessorType,
                    JsonConvert.SerializeObject(new { 
                        Duration = transcribe.Duration,
                        WhisperApiCost = transcribe.WhisperApiCost,
                        ProcessingMode = task.ProcessingMode 
                    }));
            }
            catch (OperationCanceledException) when (cancellationToken.IsCancellationRequested)
            {
                _logger.LogWarning("轉錄任務 {TaskId} 被取消", task.TaskId);
                
                // 更新狀態為取消
                await UpdateFileLogStatusAsync(task.TaskId, ProcessingStatus.Cancelled, CancellationToken.None);
                
                return TranscribeTaskResult.Failure(
                    task.TaskId,
                    "任務被取消",
                    ProcessorName,
                    ProcessorType,
                    false); // 取消的任務不需要重試
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "處理轉錄任務 {TaskId} 時發生錯誤", task.TaskId);

                // 更新狀態為失敗並記錄錯誤訊息
                await UpdateFileLogWithErrorAsync(task.TaskId, ex.Message, cancellationToken);

                // 判斷是否應該重試
                bool shouldRetry = ShouldRetryError(ex);
                
                return TranscribeTaskResult.Failure(
                    task.TaskId,
                    ex.Message,
                    ProcessorName,
                    ProcessorType,
                    shouldRetry,
                    CalculateRetryDelay(task.RetryCount),
                    ex.StackTrace);
            }
        }

        /// <summary>
        /// 使用雲端 OpenAI Whisper API 進行處理
        /// </summary>
        /// <param name="task">TranscribeTask</param>
        /// <param name="mediaFilePath">媒體檔案路徑</param>
        /// <param name="dataDirectory">資料目錄</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>轉錄結果</returns>
        private async Task<Transcribe> ProcessWithCloudApiAsync(TranscribeTask task, string mediaFilePath, string dataDirectory, CancellationToken cancellationToken)
        {
            var meetingHelper = new MeetingHelper(_openAiApiKey, _configuration);
            var processor = new WhisperApiProcessor(_openAiApiKey, meetingHelper, _configuration);
            
            return await processor.ProcessVideoFileAsync(task, mediaFilePath, dataDirectory);
        }

        /// <summary>
        /// 使用地端 Whisper API 進行處理
        /// </summary>
        /// <param name="task">TranscribeTask</param>
        /// <param name="mediaFilePath">媒體檔案路徑</param>
        /// <param name="dataDirectory">資料目錄</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>轉錄結果</returns>
        private async Task<Transcribe?> ProcessWithOnPremisesApiAsync(TranscribeTask task, string mediaFilePath, string dataDirectory, CancellationToken cancellationToken)
        {
            MeetingHelper meetingHelper = new MeetingHelper(_configuration);
            WhisperOnPremisesProcessor processor = new WhisperOnPremisesProcessor(meetingHelper, _configuration);
            Transcribe transcribe = await processor.ProcessVideoFileAsync(task, mediaFilePath, dataDirectory);
            transcribe.Guid = task.TaskId;
            return transcribe;
        }

        /// <summary>
        /// 使用地端 Whisper API 進行處理
        /// </summary>
        /// <param name="task">TranscribeTask</param>
        /// <param name="mediaFilePath">媒體檔案路徑</param>
        /// <param name="dataDirectory">資料目錄</param>
        /// <param name="cancellationToken">取消權杖</param>
        /// <returns>轉錄結果</returns>
        private async Task<Transcribe?> ProcessWithOnPremisesApiWithProgressAsync(TranscribeTask task, string mediaFilePath, string dataDirectory, CancellationToken cancellationToken)
        {
            // 使用地端處理器進行處理
            MeetingHelper meetingHelper = new MeetingHelper(_configuration);
            WhisperOnPremisesProcessor processor = new WhisperOnPremisesProcessor(meetingHelper, _configuration);
            
            // 執行實際處理
            Transcribe transcribe = await processor.ProcessVideoFileAsync(task, mediaFilePath, dataDirectory);
            transcribe.Guid = task.TaskId;
            
            return transcribe;
        }

        /// <summary>
        /// 儲存轉錄結果到檔案系統
        /// </summary>
        /// <param name="task">任務資訊</param>
        /// <param name="transcribe">轉錄結果</param>
        /// <param name="cancellationToken">取消權杖</param>
        private async Task SaveTranscribeResultsAsync(TranscribeTask task, Transcribe transcribe, CancellationToken cancellationToken)
        {
            string mainFilename = Path.GetFileNameWithoutExtension(task.MediaFilePath);
            
            // 寫入逐字稿
            string txtFilePath = Path.Combine(task.DataDirectory, $"{mainFilename}.txt");
            await File.WriteAllTextAsync(txtFilePath, transcribe.Transcript ?? string.Empty, cancellationToken);

            // 寫入會議摘要
            string summaryFilePath = Path.Combine(task.DataDirectory, "summary.txt");
            await File.WriteAllTextAsync(summaryFilePath, transcribe.Summary ?? string.Empty, cancellationToken);

            // 寫入譯文
            string translateFilePath = Path.Combine(task.DataDirectory, "translation.txt");
            await File.WriteAllTextAsync(translateFilePath, transcribe.Translation ?? string.Empty, cancellationToken);

            // 寫入 SRT 字幕
            string srtFilePath = Path.Combine(task.DataDirectory, $"{mainFilename}.srt");
            await File.WriteAllTextAsync(srtFilePath, transcribe.Srt ?? string.Empty, cancellationToken);

            // 寫入 VTT 字幕
            string vttFilePath = Path.Combine(task.DataDirectory, $"{mainFilename}.vtt");
            await File.WriteAllTextAsync(vttFilePath, transcribe.Vtt ?? string.Empty, cancellationToken);

            // 建立壓縮檔案
            await CreateZipFileAsync(task.DataDirectory, 
                new[] { txtFilePath, srtFilePath, vttFilePath, summaryFilePath, translateFilePath },
                cancellationToken);

            _logger.LogDebug("轉錄結果檔案已儲存到：{DataDirectory}", task.DataDirectory);
        }

        /// <summary>
        /// 建立壓縮檔案
        /// </summary>
        /// <param name="dataDirectory">資料目錄</param>
        /// <param name="filePaths">要壓縮的檔案路徑陣列</param>
        /// <param name="cancellationToken">取消權杖</param>
        private async Task CreateZipFileAsync(string dataDirectory, string[] filePaths, CancellationToken cancellationToken)
        {
            await Task.Run(() =>
            {
                string zipFilePath = Path.Combine(dataDirectory, "archive.zip");
                using var archive = System.IO.Compression.ZipFile.Open(zipFilePath, System.IO.Compression.ZipArchiveMode.Create);
                
                foreach (string filePath in filePaths)
                {
                    if (File.Exists(filePath))
                    {
                        archive.CreateEntryFromFile(filePath, Path.GetFileName(filePath));
                    }
                }
            }, cancellationToken);
        }

        /// <summary>
        /// 更新 FileLog 狀態
        /// </summary>
        /// <param name="uid">FileLog UID</param>
        /// <param name="status">新狀態</param>
        /// <param name="cancellationToken">取消權杖</param>
        private async Task UpdateFileLogStatusAsync(Guid uid, ProcessingStatus status, CancellationToken cancellationToken)
        {
            try
            {
                var fileLog = await _dbContext.FileLogs.FirstOrDefaultAsync(f => f.Uid == uid, cancellationToken);
                if (fileLog != null)
                {
                    fileLog.Status = status;
                    fileLog.LastProcessedAt = DateTime.Now;
                    await _dbContext.SaveChangesAsync(cancellationToken);
                    
                    _logger.LogDebug("FileLog {Uid} 狀態已更新為：{Status}", uid, status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新 FileLog {Uid} 狀態時發生錯誤", uid);
            }
        }

        /// <summary>
        /// 更新 FileLog 與處理結果
        /// </summary>
        /// <param name="uid">FileLog UID</param>
        /// <param name="transcribe">轉錄結果</param>
        /// <param name="startTime">處理開始時間</param>
        /// <param name="cancellationToken">取消權杖</param>
        private async Task UpdateFileLogWithResultsAsync(Guid uid, Transcribe transcribe, DateTime startTime, CancellationToken cancellationToken)
        {
            try
            {
                var fileLog = await _dbContext.FileLogs.FirstOrDefaultAsync(f => f.Uid == uid, cancellationToken);
                if (fileLog != null)
                {
                    var endTime = DateTime.Now;
                    fileLog.Status = ProcessingStatus.Completed;
                    fileLog.IsCompleted = true;
                    fileLog.Duration = transcribe.Duration;
                    fileLog.WhisperApiCost = transcribe.WhisperApiCost;
                    fileLog.ProcessedTime = endTime - startTime;
                    fileLog.LastProcessedAt = endTime;
                    fileLog.ErrorMessage = null; // 清除錯誤訊息
                    
                    await _dbContext.SaveChangesAsync(cancellationToken);
                    
                    _logger.LogDebug("FileLog {Uid} 已更新完成狀態，耗時：{Duration}，成本：{Cost}", 
                        uid, fileLog.ProcessedTime, fileLog.WhisperApiCost);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新 FileLog {Uid} 完成狀態時發生錯誤", uid);
            }
        }

        /// <summary>
        /// 更新 FileLog 與錯誤資訊
        /// </summary>
        /// <param name="uid">FileLog UID</param>
        /// <param name="errorMessage">錯誤訊息</param>
        /// <param name="cancellationToken">取消權杖</param>
        private async Task UpdateFileLogWithErrorAsync(Guid uid, string errorMessage, CancellationToken cancellationToken)
        {
            try
            {
                var fileLog = await _dbContext.FileLogs.FirstOrDefaultAsync(f => f.Uid == uid, cancellationToken);
                if (fileLog != null)
                {
                    fileLog.Status = ProcessingStatus.Failed;
                    fileLog.IsCompleted = false;
                    fileLog.ErrorMessage = errorMessage;
                    fileLog.LastProcessedAt = DateTime.Now;
                    fileLog.RetryCount++;
                    
                    await _dbContext.SaveChangesAsync(cancellationToken);
                    
                    _logger.LogDebug("FileLog {Uid} 已更新失敗狀態，重試次數：{RetryCount}", uid, fileLog.RetryCount);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "更新 FileLog {Uid} 失敗狀態時發生錯誤", uid);
            }
        }

        /// <summary>
        /// 判斷錯誤是否應該重試
        /// </summary>
        /// <param name="exception">發生的例外</param>
        /// <returns>是否應該重試</returns>
        private bool ShouldRetryError(Exception exception)
        {
            return exception switch
            {
                // 網路相關錯誤通常可以重試
                HttpRequestException _ => true,
                TaskCanceledException _ => true,
                
                // 檔案不存在或權限問題通常不需要重試
                FileNotFoundException _ => false,
                UnauthorizedAccessException _ => false,
                DirectoryNotFoundException _ => false,
                
                // API 相關錯誤需要檢查具體訊息
                InvalidOperationException ex when ex.Message.Contains("API Key") => false,
                InvalidOperationException ex when ex.Message.Contains("檔案格式") => false,
                
                // 其他錯誤預設允許重試
                _ => true
            };
        }

        /// <summary>
        /// 計算重試延遲時間（指數退避）
        /// </summary>
        /// <param name="retryCount">目前重試次數</param>
        /// <returns>延遲秒數</returns>
        private int CalculateRetryDelay(int retryCount)
        {
            // 指數退避：1分鐘、5分鐘、15分鐘
            return retryCount switch
            {
                0 => 60,    // 1分鐘
                1 => 300,   // 5分鐘
                2 => 900,   // 15分鐘
                _ => 1800   // 30分鐘
            };
        }

        /// <summary>
        /// 檢查處理器是否可用
        /// </summary>
        /// <returns>處理器是否可用</returns>
        public async Task<bool> IsAvailableAsync()
        {
            try
            {
                // 檢查 OpenAI API Key 是否設定
                if (string.IsNullOrEmpty(_openAiApiKey))
                {
                    _logger.LogWarning("OpenAI API Key 未設定");
                    return false;
                }

                // 檢查檔案目錄是否可存取
                if (!Directory.Exists(_fileDir))
                {
                    try
                    {
                        Directory.CreateDirectory(_fileDir);
                    }
                    catch
                    {
                        _logger.LogWarning("無法建立檔案目錄：{FileDir}", _fileDir);
                        return false;
                    }
                }

                // 檢查資料庫連線是否正常
                await _dbContext.Database.CanConnectAsync();

                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "檢查處理器可用性時發生錯誤");
                return false;
            }
        }

        /// <summary>
        /// 檢查處理器是否支援指定的檔案格式
        /// </summary>
        /// <param name="fileExtension">檔案副檔名 (不含點)</param>
        /// <returns>是否支援</returns>
        public bool SupportedFileFormat(string fileExtension)
        {
            if (string.IsNullOrEmpty(fileExtension))
                return false;

            string normalizedExtension = fileExtension.ToLowerInvariant().TrimStart('.');
            return Array.Exists(SupportedFormats, format => format == normalizedExtension);
        }

        /// <summary>
        /// 檢查檔案大小是否在限制範圍內
        /// </summary>
        /// <param name="fileSizeBytes">檔案大小 (位元組)</param>
        /// <returns>是否在限制範圍內</returns>
        public bool IsFileSizeValid(long fileSizeBytes)
        {
            return fileSizeBytes > 0 && fileSizeBytes <= MaxFileSizeBytes;
        }

        /// <summary>
        /// 估算處理時間
        /// </summary>
        /// <param name="fileSizeBytes">檔案大小 (位元組)</param>
        /// <param name="durationSeconds">音檔長度 (秒)</param>
        /// <returns>估算處理時間 (分鐘)</returns>
        public int EstimateProcessingTimeMinutes(long fileSizeBytes, int? durationSeconds = null)
        {
            // 基於檔案大小的估算（每 MB 約需 0.5 分鐘）
            int estimateBySize = (int)Math.Ceiling(fileSizeBytes / 1024.0 / 1024.0 * 0.5);

            // 基於音檔長度的估算（每分鐘音檔約需 0.2 分鐘處理）
            int estimateByDuration = durationSeconds.HasValue 
                ? (int)Math.Ceiling(durationSeconds.Value / 60.0 * 0.2)
                : estimateBySize;

            // 取較大值作為估算結果，最少 1 分鐘
            return Math.Max(1, Math.Max(estimateBySize, estimateByDuration));
        }

        /// <summary>
        /// 取得處理器設定
        /// </summary>
        /// <returns>處理器設定物件</returns>
        public object GetConfiguration()
        {
            return new
            {
                ProcessorName = ProcessorName,
                ProcessorType = ProcessorType,
                SupportedFormats = SupportedFormats,
                MaxFileSizeBytes = MaxFileSizeBytes,
                FileDir = _fileDir,
                OnPremisesApiUrl = _onPremisesApiUrl,
                HasOpenAiApiKey = !string.IsNullOrEmpty(_openAiApiKey)
            };
        }
    }
}
