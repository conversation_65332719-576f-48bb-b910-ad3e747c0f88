using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

using MeetingAssistant.Models;

using MeetingUtility.Interfaces;

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace MeetingAssistant.Services
{
    /// <summary>
    /// 轉錄任務隊列服務實作
    /// 使用優先級隊列來管理任務，支援並發控制和智能重試
    /// </summary>
    public class TranscribeTaskQueue : ITranscribeTaskQueue, IDisposable
    {
        private readonly ILogger<TranscribeTaskQueue> _logger;
        private readonly BackgroundTaskConfig _config;
        
        // 用於存儲待處理任務的優先級隊列
        private readonly SortedDictionary<int, Queue<TranscribeTask>> _priorityQueues;
        private readonly object _queueLock = new object();
        
        // 用於追蹤正在處理的任務
        private readonly ConcurrentDictionary<Guid, TranscribeTask> _processingTasks;
        
        // 用於追蹤任務統計
        private readonly ConcurrentDictionary<Guid, DateTime> _completedTasks;
        private readonly ConcurrentDictionary<Guid, DateTime> _failedTasks;
        
        private volatile bool _disposed = false;
        private int _totalEnqueuedTasks = 0;

        public TranscribeTaskQueue(
            ILogger<TranscribeTaskQueue> logger,
            IOptions<BackgroundTaskConfig> config)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _config = config?.Value ?? throw new ArgumentNullException(nameof(config));

            _priorityQueues = new SortedDictionary<int, Queue<TranscribeTask>>();
            _processingTasks = new ConcurrentDictionary<Guid, TranscribeTask>();
            _completedTasks = new ConcurrentDictionary<Guid, DateTime>();
            _failedTasks = new ConcurrentDictionary<Guid, DateTime>();

            _logger.LogInformation("轉錄任務隊列已初始化，最大容量: {MaxCapacity}, 並發任務數: {MaxConcurrent}",
                _config.MaxQueueCapacity, _config.MaxConcurrentTasks);
        }

        /// <summary>
        /// 將任務加入隊列
        /// </summary>
        public Task<bool> EnqueueTaskAsync(TranscribeTask task, CancellationToken cancellationToken = default)
        {
            if (_disposed)
            {
                _logger.LogWarning("嘗試向已釋放的隊列加入任務: {TaskId}", task.TaskId);
                return Task.FromResult(false);
            }

            if (task == null)
            {
                _logger.LogWarning("嘗試加入空任務到隊列");
                return Task.FromResult(false);
            }

            try
            {
                // 檢查隊列是否已滿
                if (IsQueueFull())
                {
                    _logger.LogWarning("任務隊列已滿，無法加入新任務: {TaskId}", task.TaskId);
                    return Task.FromResult(false);
                }

                lock (_queueLock)
                {
                    // 建立或取得對應優先級的隊列
                    if (!_priorityQueues.ContainsKey(task.Priority))
                    {
                        _priorityQueues[task.Priority] = new Queue<TranscribeTask>();
                    }
                    
                    _priorityQueues[task.Priority].Enqueue(task);
                }

                Interlocked.Increment(ref _totalEnqueuedTasks);

                _logger.LogInformation("任務已加入隊列: {TaskId}, 優先級: {Priority}, 檔案: {FileName}",
                    task.TaskId, task.Priority, task.FileName);

                return Task.FromResult(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "加入任務到隊列時發生錯誤: {TaskId}", task.TaskId);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// 獲取 TranscribeTask 的詳細資訊
        /// </summary>
        /// <param name="taskId">TranscribeTask 的TaskId</param>
        /// <returns>TranscribeTask or null</returns>
        public TranscribeTask? GetTask(Guid taskId)
        {
            return _processingTasks.Values.FirstOrDefault(task => task.TaskId == taskId);
        }

        /// <summary>
        /// 從隊列取出任務進行處理
        /// </summary>
        public Task<TranscribeTask?> DequeueTaskAsync(CancellationToken cancellationToken = default)
        {
            if (_disposed)
            {
                return Task.FromResult<TranscribeTask?>(null);
            }

            try
            {
                // 檢查是否已達到最大並發處理數
                if (_processingTasks.Count >= _config.MaxConcurrentTasks)
                {
                    _logger.LogDebug("已達到最大並發處理數 ({MaxConcurrent})，等待任務完成",
                        _config.MaxConcurrentTasks);
                    return Task.FromResult<TranscribeTask?>(null);
                }

                TranscribeTask? taskToProcess = null;

                lock (_queueLock)
                {
                    // 按優先級順序處理 (數字越小優先級越高)
                    foreach (var priority in _priorityQueues.Keys.ToList())
                    {
                        var queue = _priorityQueues[priority];
                        if (queue.Count > 0)
                        {
                            taskToProcess = queue.Dequeue();
                            
                            // 如果這個優先級的隊列空了，移除它
                            if (queue.Count == 0)
                            {
                                _priorityQueues.Remove(priority);
                            }
                            
                            break;
                        }
                    }
                }

                if (taskToProcess != null)
                {
                    // 標記任務為處理中
                    taskToProcess.StartedAt = DateTime.Now;
                    _processingTasks.TryAdd(taskToProcess.TaskId, taskToProcess);

                    _logger.LogInformation("從隊列取出任務: {TaskId}, 檔案: {FileName}",
                        taskToProcess.TaskId, taskToProcess.FileName);
                }

                return Task.FromResult(taskToProcess);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "從隊列取出任務時發生錯誤");
                return Task.FromResult<TranscribeTask?>(null);
            }
        }

        /// <summary>
        /// 獲取隊列中待處理任務數量
        /// </summary>
        public int GetPendingTaskCount()
        {
            lock (_queueLock)
            {
                return _priorityQueues.Values.Sum(q => q.Count);
            }
        }

        /// <summary>
        /// 獲取正在處理的任務數量
        /// </summary>
        public int GetProcessingTaskCount()
        {
            return _processingTasks.Count;
        }

        /// <summary>
        /// 獲取隊列統計資訊
        /// </summary>
        public TaskQueueStatistics GetStatistics()
        {
            lock (_queueLock)
            {
                var highestPriority = _priorityQueues.Keys.FirstOrDefault();
                
                return new TaskQueueStatistics
                {
                    PendingTasks = GetPendingTaskCount(),
                    ProcessingTasks = GetProcessingTaskCount(),
                    CompletedTasks = _completedTasks.Count,
                    FailedTasks = _failedTasks.Count,
                    MaxCapacity = _config.MaxQueueCapacity,
                    HighestPriority = _priorityQueues.Any() ? highestPriority : null,
                    LastUpdated = DateTime.Now
                };
            }
        }

        /// <summary>
        /// 依優先級獲取隊列中的任務列表 (不移除任務)
        /// </summary>
        public Task<IList<TranscribeTask>> PeekTasksAsync(int maxCount = 10)
        {
            var tasks = new List<TranscribeTask>();
            var count = 0;

            lock (_queueLock)
            {
                foreach (var priority in _priorityQueues.Keys)
                {
                    if (count >= maxCount) break;

                    var queue = _priorityQueues[priority];
                    foreach (var task in queue)
                    {
                        if (count >= maxCount) break;
                        tasks.Add(task);
                        count++;
                    }
                }
            }

            return Task.FromResult<IList<TranscribeTask>>(tasks);
        }

        /// <summary>
        /// 嘗試取消指定任務
        /// </summary>
        public Task<bool> TryCancelTaskAsync(Guid taskId)
        {
            // 檢查是否為正在處理的任務
            if (_processingTasks.ContainsKey(taskId))
            {
                _logger.LogWarning("無法取消正在處理的任務: {TaskId}", taskId);
                return Task.FromResult(false);
            }

            // 從優先級隊列中移除任務
            lock (_queueLock)
            {
                foreach (var priority in _priorityQueues.Keys.ToList())
                {
                    var queue = _priorityQueues[priority];
                    var tempList = new List<TranscribeTask>();
                    var found = false;

                    while (queue.Count > 0)
                    {
                        var task = queue.Dequeue();
                        if (task.TaskId == taskId)
                        {
                            found = true;
                            _logger.LogInformation("任務已從隊列中取消: {TaskId}", taskId);
                        }
                        else
                        {
                            tempList.Add(task);
                        }
                    }

                    // 重新加入剩餘任務
                    foreach (var task in tempList)
                    {
                        queue.Enqueue(task);
                    }

                    if (found) return Task.FromResult(true);

                    // 清理空的優先級隊列
                    if (queue.Count == 0)
                    {
                        _priorityQueues.Remove(priority);
                    }
                }
            }

            _logger.LogWarning("找不到要取消的任務: {TaskId}", taskId);
            return Task.FromResult(false);
        }

        /// <summary>
        /// 清除所有待處理任務
        /// </summary>
        public Task<int> ClearPendingTasksAsync()
        {
            var clearedCount = 0;

            lock (_queueLock)
            {
                foreach (var queue in _priorityQueues.Values)
                {
                    clearedCount += queue.Count;
                    queue.Clear();
                }
                _priorityQueues.Clear();
            }

            _logger.LogInformation("已清除 {Count} 個待處理任務", clearedCount);
            return Task.FromResult(clearedCount);
        }

        /// <summary>
        /// 檢查隊列是否已滿
        /// </summary>
        public bool IsQueueFull()
        {
            if (_config.MaxQueueCapacity <= 0) return false; // 無限制

            var pendingCount = GetPendingTaskCount();
            return pendingCount >= _config.MaxQueueCapacity;
        }

        /// <summary>
        /// 標記任務開始處理
        /// </summary>
        public Task<bool> MarkTaskAsProcessingAsync(Guid taskId)
        {
            if (_processingTasks.ContainsKey(taskId))
            {
                var task = _processingTasks[taskId];
                task.StartedAt = DateTime.Now;
                _logger.LogDebug("任務已標記為處理中: {TaskId}", taskId);
                return Task.FromResult(true);
            }

            _logger.LogWarning("找不到要標記為處理中的任務: {TaskId}", taskId);
            return Task.FromResult(false);
        }

        /// <summary>
        /// 標記任務處理完成
        /// </summary>
        public Task<bool> MarkTaskAsCompletedAsync(Guid taskId)
        {
            if (_processingTasks.TryRemove(taskId, out var task))
            {
                _completedTasks.TryAdd(taskId, DateTime.Now);
                _logger.LogInformation("任務已標記為完成: {TaskId}, 檔案: {FileName}",
                    taskId, task.FileName);
                return Task.FromResult(true);
            }

            _logger.LogWarning("找不到要標記為完成的處理中任務: {TaskId}", taskId);
            return Task.FromResult(false);
        }

        /// <summary>
        /// 重新將失敗任務加入隊列 (用於重試)
        /// </summary>
        public async Task<bool> RequeueFailedTaskAsync(TranscribeTask task, CancellationToken cancellationToken = default)
        {
            if (task == null) return false;

            // 從處理清單中移除
            _processingTasks.TryRemove(task.TaskId, out _);
            
            // 記錄失敗
            _failedTasks.TryAdd(task.TaskId, DateTime.Now);

            // 增加重試次數
            task.RetryCount++;

            // 檢查是否超過最大重試次數
            if (task.RetryCount >= task.MaxRetryCount)
            {
                _logger.LogWarning("任務已達到最大重試次數，不再重試: {TaskId}, 重試次數: {RetryCount}",
                    task.TaskId, task.RetryCount);
                return false;
            }

            // 調整優先級 (失敗任務優先級降低)
            task.Priority += 10;

            _logger.LogInformation("失敗任務重新加入隊列: {TaskId}, 重試次數: {RetryCount}",
                task.TaskId, task.RetryCount);

            return await EnqueueTaskAsync(task, cancellationToken);
        }

        /// <summary>
        /// 釋放資源
        /// </summary>
        public void Dispose()
        {
            if (_disposed) return;

            _disposed = true;

            _logger.LogInformation("轉錄任務隊列已釋放，處理了 {TotalTasks} 個任務",
                _totalEnqueuedTasks);
        }
    }
}
