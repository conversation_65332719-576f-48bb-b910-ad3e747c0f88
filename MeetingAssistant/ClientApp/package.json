{"name": "meeting-assistant", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix"}, "dependencies": {"@tailwindcss/vite": "4.1.10", "pinia": "^3.0.3", "tailwindcss": "4.1.10", "vue": "3.5.16"}, "devDependencies": {"@stagewise/toolbar": "0.4.8", "@stagewise/toolbar-vue": "0.4.8", "@tsconfig/node22": "22.0.2", "@types/node": "24.0.3", "@vitejs/plugin-vue": "5.2.4", "@vue/eslint-config-typescript": "14.5.1", "@vue/tsconfig": "^0.7.0", "axios": "1.10.0", "eslint": "^9.29.0", "eslint-plugin-vue": "^9.32.0", "jiti": "^2.4.2", "marked": "15.0.12", "npm-run-all2": "8.0.4", "sass-embedded": "1.89.2", "typescript": "5.8.3", "vite": "6.3.5", "vite-plugin-vue-devtools": "7.7.7", "vue-tsc": "^2.2.10"}}