/**
 * 會議助手系統的 TypeScript 類型定義
 * 包含處理狀態枚舉和檔案記錄介面定義
 */

/**
 * 逐字稿資料型別
 * 定義語音轉文字處理後產生的相關資料結構，包含逐字稿內容、字幕、摘要等
 */
export type Transcribe = {
    /** 逐字稿內容 */
    transcript: string;
    /** 原始檔案名稱 */
    fileName: string;
    /** 會議摘要內容 */
    summary: string;
    /** SRT 字幕內容 */
    srt: string;
    /** WEBVTT 字幕內容 */
    vtt: string;
    /** 翻譯內容 (如果進行了翻譯) */
    translation: string;
    /** 檔案識別碼 (UUID 格式) */
    guid: string;
    /** 音檔總時間長度 (秒) */
    duration: number;
    /** Whisper API 處理成本 (美元) */
    whisperApiCost: number;
}

/**
 * 任務狀態資料型別
 * 用於追蹤背景任務的執行狀態和進度
 */
export type TaskStatus = {
    /** 任務唯一識別碼 */
    taskId: string;
    /** 任務當前處理狀態 (例如 Pending, Processing, Completed 等) */
    status: string;
    /** 相關檔案名稱 */
    fileName: string;
    /** 檔案上傳時間 (ISO 8601 格式) */
    uploadTime: string;
    /** 最後一次處理時間 (ISO 8601 格式，可選) */
    lastProcessedAt?: string;
    /** 任務是否已完成 */
    isCompleted: boolean;
    /** 錯誤訊息 (如果任務失敗，可選) */
    errorMessage?: string;
    /** 任務重試次數 */
    retryCount: number;
    /** 任務處理進度百分比 (0-100) */
    progress: number;
}

/**
 * 處理狀態枚舉
 * 用於追蹤檔案處理的各個階段狀態
 */
export enum ProcessingStatus {
    /** 等待處理 - 檔案已上傳但尚未開始處理 */
    Pending = 0,
    /** 處理中 - 檔案正在進行語音轉文字處理 */
    Processing = 1,
    /** 已完成 - 檔案處理成功完成 */
    Completed = 2,
    /** 處理失敗 - 檔案處理過程中發生錯誤 */
    Failed = 3,
    /** 已取消 - 使用者主動取消處理任務 */
    Cancelled = 4
}

/**
 * 檔案記錄介面
 * 定義上傳檔案的完整資訊結構，包含處理狀態、成本追蹤等資訊
 */
export type FileLog = {
    /** 唯一識別碼 (UUID 格式) */
    uid: string;
    /** 資料庫主鍵 ID */
    id: number;
    /** 員工編號 */
    empNo: string;
    /** 員工姓名 */
    name: string;
    /** 部門編號 */
    deptNo: number;
    /** 部門名稱 */
    department: string;
    /** 上傳者的 IP 位址 */
    iP: string;
    /** 上傳者的主機名稱 (可為空) */
    hostname: string | null;
    /** 檔案上傳時間 (ISO 8601 格式) */
    uploadTime: string;
    /** 原始檔案名稱 */
    fileName: string;
    /** 檔案大小 (位元組) */
    fileSize: number;
    /** 音檔總長度 (秒) */
    duration: number;
    /** Whisper API 處理成本 (美元) */
    whisperApiCost: number;
    /** 是否已完成處理 (舊版相容性欄位) */
    isCompleted: boolean;
    /** 目前處理狀態 */
    status: ProcessingStatus;
    /** 處理完成時間 (ISO 8601 格式，可為空) */
    processedTime: string | null;
    /** 記錄建立時間 (ISO 8601 格式) */
    createdAt: string;
    /** 記錄最後更新時間 (ISO 8601 格式，可為空) */
    updatedAt: string | null;
    /** 重試次數 */
    retryCount: number;
    /** 錯誤訊息 (處理失敗時的詳細錯誤資訊，可為空) */
    errorMessage: string | null;
    /** 最後處理時間 (ISO 8601 格式，可為空) */
    lastProcessedAt: string | null;
    /** 備註資訊 (可為空) */
    remark: string | null;
}
