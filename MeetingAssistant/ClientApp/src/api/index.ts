import axios from 'axios';
const mode = import.meta.env.MODE;
const API_BASE_URL = (mode === 'development') ? 'https://localhost:7156/' : `${import.meta.env.BASE_URL}`;
//const API_BASE_URL = '/MeetingAssistant/';
// 轉錄產生逐字稿 API
export const whisperAPIurl = `${API_BASE_URL}api/Whisper/Transcribe`;

// 下載結果檔 API
export const downloadAPIurl = `${API_BASE_URL}api/Whisper/Download`;

// 載入歷史記錄 API
const loadHistoryUrl = `${API_BASE_URL}api/Whisper/LoadHistory`;

// 載入逐字稿 API
const loadTranscribeUrl = `${API_BASE_URL}api/Whisper/LoadTranscribe`;

// 任務狀態查詢 API
const getTaskStatusUrl = `${API_BASE_URL}api/Whisper/GetTaskStatus`;

// 取消任務 API
const cancelTaskUrl = `${API_BASE_URL}api/Whisper/CancelTask`;

// 刪除歷史記錄 API
const removeHistoryUrl = `${API_BASE_URL}api/Whisper/RemoveHistory`;

// 任務詳細進度查詢 API
const getTaskProgressUrl = `${API_BASE_URL}api/Whisper/GetTaskProgress`;

// API回傳的資料結構如下
// {
//   transcript: '逐字稿內容',
//   fileName: '檔案名稱',
//   summary: '會議摘要',
//   srt: 'SRT字幕內容',
//   vtt: 'VTT字幕內容',
//   translation: '翻譯內容',
//   guid: '檔案識別碼'
//   duration: '音檔總時間長度（秒）'
// }

// 載入歷史記錄
export const loadHistory = async () => {
    //const response = await fetch(loadHistoryUrl);
    try {
        const response = await axios.get(loadHistoryUrl);
        const data = await response.data;
        return data;
    } catch (error) {
        console.error('載入歷史記錄時發生錯誤：', error);
        return [];
    }
}

// 載入逐字稿
export const loadTranscribe = async (guid: string) => {
    try {
        const response = await axios.get(loadTranscribeUrl, {
            params: {
                guid: guid
            }
        });
        const data = await response.data;
        return data;
    } catch (error) {
        console.error('載入逐字稿時發生錯誤：', error);
        return [];
    }
}

// 查詢任務狀態
export const getTaskStatus = async (taskId: string) => {
    try {
        const response = await axios.get(getTaskStatusUrl, {
            params: {
                taskId: taskId
            }
        });
        return response.data;
    } catch (error) {
        console.error('查詢任務狀態時發生錯誤：', error);
        throw error;
    }
}

// 取消任務
export const cancelTask = async (taskId: string) => {
    try {
        const response = await axios.post(cancelTaskUrl, {
            taskId: taskId
        });
        return response.data;
    } catch (error) {
        console.error('取消任務時發生錯誤：', error);
        throw error;
    }
}

// 刪除歷史記錄
export const removeHistory = async (guid: string) => {
    try {
        const response = await axios.delete(removeHistoryUrl, {
            params: {
                guid: guid
            }
        });
        return response.data;
    } catch (error) {
        console.error('刪除歷史記錄時發生錯誤：', error);
        throw error;
    }
}

// 查詢任務詳細進度
export const getTaskProgress = async (taskId: string): Promise<number|string> => {
    try {
        const response = await axios.get(getTaskProgressUrl, {
            params: {
                taskId: taskId
            }
        });
        console.log('查詢任務詳細進度：', response.data);
        return response.data;
    } catch (error) {
        console.error('查詢任務詳細進度時發生錯誤：', error);
        throw error;
    }
}

