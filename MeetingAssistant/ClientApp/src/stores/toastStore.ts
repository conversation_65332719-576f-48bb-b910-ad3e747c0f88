import { defineStore } from 'pinia'
import { ref } from 'vue'

export interface Toast {
  id: number
  type: 'success' | 'error' | 'info'
  title: string
  message: string
  show: boolean
}

export const useToastStore = defineStore('toast', () => {
  const toasts = ref<Toast[]>([])
  let toastIdCounter = 0

  const showToast = (type: 'success' | 'error' | 'info', title: string, message: string, duration = 5000) => {
    const id = ++toastIdCounter
    const toast: Toast = {
      id,
      type,
      title,
      message,
      show: true
    }

    toasts.value.push(toast)

    // 自動隱藏 Toast
    setTimeout(() => {
      hideToast(id)
    }, duration)

    return id
  }

  const hideToast = (id: number) => {
    const index = toasts.value.findIndex(t => t.id === id)
    if (index !== -1) {
      toasts.value[index].show = false
      // 延遲移除以配合動畫
      setTimeout(() => {
        toasts.value.splice(index, 1)
      }, 300)
    }
  }

  const clearAllToasts = () => {
    toasts.value = []
  }

  return {
    toasts,
    showToast,
    hideToast,
    clearAllToasts
  }
}) 