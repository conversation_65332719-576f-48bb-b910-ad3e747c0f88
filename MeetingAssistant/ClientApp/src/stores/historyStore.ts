import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { loadHistory, getTaskStatus, getTaskProgress } from '@/api';
import type { FileLog } from '@/types';

export const useHistoryStore = defineStore('history', () => {
  // 狀態
  const historyList = ref<FileLog[]>([]);
  const isLoading = ref(false);
  const monitoringTasks = ref<Set<string>>(new Set());
  const taskMonitorInterval = ref<number | null>(null);

  // 計算屬性
  const processingTasks = computed(() =>
    historyList.value.filter(item =>
      item.status === 0 || item.status === 1 // Pending or Processing
    )
  );

  const completedTasks = computed(() =>
    historyList.value.filter(item => item.status === 2) // Completed
  );

  // 行動
  const loadHistoryList = async () => {
    isLoading.value = true;
    try {
      console.log('🔄 開始載入歷史記錄...');
      const data = await loadHistory();
      historyList.value = data || [];

      console.log(`📋 載入了 ${historyList.value.length} 筆歷史記錄`);

      // 等待 Vue 的響應式更新完成，然後檢查處理中的任務
      await new Promise(resolve => setTimeout(resolve, 0));

      // 檢查是否有處理中的任務需要監控
      const currentProcessingTasks = historyList.value.filter(item =>
        item.status === 0 || item.status === 1 // Pending or Processing
      );

      console.log(`⏳ 發現 ${currentProcessingTasks.length} 筆處理中的任務:`,
        currentProcessingTasks.map(t => ({ uid: t.uid, fileName: t.fileName, status: t.status })));

      // 更新監控任務集合 - 只添加新的處理中任務，不清除現有監控
      currentProcessingTasks.forEach(task => {
        if (!monitoringTasks.value.has(task.uid)) {
          monitoringTasks.value.add(task.uid);
          console.log(`➕ 新增監控任務: ${task.fileName} (${task.uid})`);
        }
      });

      // 移除已經不在處理中的任務監控
      const currentProcessingTaskIds = currentProcessingTasks.map(t => t.uid);
      for (const taskId of monitoringTasks.value) {
        if (!currentProcessingTaskIds.includes(taskId)) {
          // 檢查這個任務是否已經完成（不在歷史記錄中或狀態已改變）
          const taskInHistory = historyList.value.find(t => t.uid === taskId);
          if (!taskInHistory || (taskInHistory.status !== 0 && taskInHistory.status !== 1)) {
            monitoringTasks.value.delete(taskId);
            console.log(`➖ 移除監控任務: ${taskId}`);
          }
        }
      }

      console.log(`👀 目前監控中的任務數量: ${monitoringTasks.value.size}`);

      // 如果有處理中的任務且監控未啟動，則啟動監控
      if (monitoringTasks.value.size > 0 && !taskMonitorInterval.value) {
        startTaskMonitoring();
      } else if (monitoringTasks.value.size === 0 && taskMonitorInterval.value) {
        stopTaskMonitoring();
      }

      return historyList.value;
    } catch (error) {
      console.error('❌ 載入歷史記錄失敗:', error);
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  const addMonitoringTask = (taskId: string) => {
    if (!monitoringTasks.value.has(taskId)) {
      monitoringTasks.value.add(taskId);
      console.log(`➕ 手動新增監控任務: ${taskId}`);

      if (!taskMonitorInterval.value) {
        startTaskMonitoring();
      }
    }
  };

  const removeMonitoringTask = (taskId: string) => {
    if (monitoringTasks.value.has(taskId)) {
      monitoringTasks.value.delete(taskId);
      console.log(`➖ 手動移除監控任務: ${taskId}`);

      if (monitoringTasks.value.size === 0) {
        stopTaskMonitoring();
      }
    }
  };

  const startTaskMonitoring = () => {
    if (taskMonitorInterval.value) return;

    // 縮短監控間隔為 10 秒，便於測試和即時更新
    taskMonitorInterval.value = setInterval(async () => {
      // 重新載入歷史記錄
      historyList.value = await loadHistory();
      await checkTaskStatuses();
    }, 10000); // 每10秒檢查一次

    console.log('🚀 任務監控已啟動 (每10秒檢查一次)');
  };

  const stopTaskMonitoring = () => {
    if (taskMonitorInterval.value) {
      clearInterval(taskMonitorInterval.value);
      taskMonitorInterval.value = null;
      console.log('⏹️ 任務監控已停止');
    }
  };

  const checkTaskStatuses = async () => {
    if (monitoringTasks.value.size === 0) {
      console.log('⏭️ 沒有需要監控的任務，跳過檢查');
      return;
    }

    console.log(`🔍 開始檢查 ${monitoringTasks.value.size} 個任務的狀態...`);

    const completedTaskIds: string[] = [];
    let hasUpdates = false;

    for (const taskId of monitoringTasks.value) {
      try {
        // 檢查 localStorage 中是否已經通知過
        const notifiedTasks = JSON.parse(localStorage.getItem('notifiedTasks') || '[]');
        if (notifiedTasks.includes(taskId)) {
          console.log(`⏭️ 任務 ${taskId} 已經通知過，移除監控`);
          completedTaskIds.push(taskId);
          continue;
        }

        console.log(`🔍 檢查任務狀態: ${taskId}`);
        const taskStatus = await getTaskStatus(taskId);
        console.log(`📊 任務 ${taskId} 狀態: ${taskStatus.Status}`);

        if (taskStatus.Status === 'Completed') {
          console.log(`✅ 任務完成: ${taskStatus.FileName}`);

          // 記錄已通知的任務
          notifiedTasks.push(taskId);
          localStorage.setItem('notifiedTasks', JSON.stringify(notifiedTasks));

          completedTaskIds.push(taskId);
          hasUpdates = true;

          // 發送自定義事件通知任務完成
          window.dispatchEvent(new CustomEvent('taskCompleted', {
            detail: { taskId, fileName: taskStatus.FileName }
          }));

        } else if (taskStatus.Status === 'Failed') {
          console.log(`❌ 任務失敗: ${taskStatus.FileName}`);
          completedTaskIds.push(taskId);
          hasUpdates = true;

          // 發送自定義事件通知任務失敗
          window.dispatchEvent(new CustomEvent('taskFailed', {
            detail: { taskId, fileName: taskStatus.FileName, error: taskStatus.ErrorMessage }
          }));

        } else if (taskStatus.Status === 'Cancelled') {
          console.log(`🚫 任務已取消: ${taskId}`);
          completedTaskIds.push(taskId);
          hasUpdates = true;
        } else {
          console.log(`⏳ 任務 ${taskId} 仍在處理中 (${taskStatus.Status})`);
        }
      } catch (error) {
        console.error(`❌ 檢查任務狀態失敗 ${taskId}:`, error);
      }
    }

    // 移除已完成的任務監控
    completedTaskIds.forEach(taskId => {
      monitoringTasks.value.delete(taskId);
    });

    if (completedTaskIds.length > 0) {
      console.log(`🧹 移除了 ${completedTaskIds.length} 個已完成的任務監控`);
    }

    // 如果有狀態更新，重新載入歷史記錄
    if (hasUpdates) {
      console.log('🔄 檢測到狀態更新，重新載入歷史記錄...');
      await loadHistoryList();
    }

         // 重新檢查歷史記錄中是否還有處理中的任務
     const currentProcessingTasks = historyList.value.filter(item =>
       item.status === 0 || item.status === 1 // Pending or Processing
     );
     
     // 如果沒有處理中的任務，停止監控
     if (currentProcessingTasks.length === 0) {
       console.log('✨ 所有任務都已完成，停止監控');
       stopTaskMonitoring();
     } else {
       console.log(`👀 繼續監控 ${currentProcessingTasks.length} 個處理中任務`);
     }
  };

  const removeHistoryItem = (guid: string) => {
    const index = historyList.value.findIndex(item => item.uid === guid);
    if (index !== -1) {
      const removedItem = historyList.value[index];
      historyList.value.splice(index, 1);
      console.log(`🗑️ 從歷史記錄中移除: ${removedItem.fileName}`);
    }

    // 同時移除監控
    removeMonitoringTask(guid);
  };

  const updateHistoryItem = (updatedItem: FileLog) => {
    const index = historyList.value.findIndex(item => item.uid === updatedItem.uid);
    if (index !== -1) {
      historyList.value[index] = updatedItem;
      console.log(`📝 更新歷史記錄項目: ${updatedItem.fileName}`);
    }
  };

  // 獲取任務詳細進度
  // const getDetailedProgress = async (taskId: string) => {
  //   try {
  //     console.log(`🔍 獲取任務詳細進度: ${taskId}`);
  //     const progress = await getTaskProgress(taskId);
  //     console.log(`📊 任務 ${taskId} 詳細進度:`, progress);
  //     return progress;
  //   } catch (error) {
  //     console.error(`❌ 獲取任務詳細進度失敗 ${taskId}:`, error);
  //     return null;
  //   }
  // };

  // 清理函數
  const cleanup = () => {
    console.log('🧹 清理 historyStore...');
    stopTaskMonitoring();
    monitoringTasks.value.clear();
  };

  return {
    // 狀態
    historyList,
    isLoading,
    monitoringTasks,

    // 計算屬性
    processingTasks,
    completedTasks,

    // 行動
    loadHistoryList,
    addMonitoringTask,
    removeMonitoringTask,
    startTaskMonitoring,
    stopTaskMonitoring,
    checkTaskStatuses,
    removeHistoryItem,
    updateHistoryItem,
    getTaskProgress,
    cleanup
  };
}); 