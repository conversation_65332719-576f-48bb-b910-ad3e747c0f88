<template>
    <div class="mb-8 bg-white rounded-lg shadow-md overflow-hidden">
        <div class="p-6">
            <h2 class="text-xl font-semibold mb-4">歷史記錄</h2>
            <div v-if="historyStore.historyList.length === 0" class="text-gray-500 text-center py-4">
                暫無歷史紀錄
            </div>
            <div v-else>
                <div class="space-y-4">
                    <div v-for="(item, index) in paginatedHistoryList" :key="item.id"
                        class="border rounded-lg transition-colors overflow-hidden"
                        :class="{ 'bg-blue-50': index % 2 === 0, 'bg-gray-50': index % 2 === 1 }">

                        <!-- 處理中的任務顯示詳細進度 -->
                        <div v-if="isProcessing(item)" class="p-0">
                            <EnhancedProgressView :task-id="item.uid" :file-name="item.fileName" :status="item.status"
                                :is-completed="item.isCompleted" :error-message="item.errorMessage"
                                @progress-click="handleHistoryItemClick(item.uid)" />
                        </div>

                        <!-- 已完成或失敗的任務顯示簡化資訊 -->
                        <div v-else class="p-4 cursor-pointer hover:bg-gray-100 transition-colors"
                            @click="handleHistoryItemClick(item.uid)">
                            <div class="flex flex-col sm:flex-row justify-between">
                                <div class="mb-2 sm:mb-0 flex-1">
                                    <div class="flex items-center justify-between">
                                        <h3 class="font-medium text-gray-900 flex items-center">
                                            <!-- 狀態圖示 -->
                                            <span class="mr-2">
                                                <svg v-if="item.status === ProcessingStatus.Completed"
                                                    class="w-5 h-5 text-green-500" fill="currentColor"
                                                    viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                                <svg v-else-if="item.status === ProcessingStatus.Failed"
                                                    class="w-5 h-5 text-red-500" fill="currentColor"
                                                    viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                                <svg v-else-if="item.status === ProcessingStatus.Cancelled"
                                                    class="w-5 h-5 text-orange-500" fill="currentColor"
                                                    viewBox="0 0 20 20">
                                                    <path fill-rule="evenodd"
                                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z"
                                                        clip-rule="evenodd" />
                                                </svg>
                                            </span>
                                            {{ item.fileName }}
                                        </h3>

                                        <!-- 刪除按鈕 -->
                                        <div class="relative inline-block">
                                            <button @click.stop="handleDeleteHistory(item.uid, item.fileName)"
                                                @mouseenter="showTooltip(item.uid)"
                                                @mouseleave="hideTooltip(item.uid)"
                                                class="p-1 text-red-500 hover:text-red-700 hover:bg-red-50 rounded transition-colors hover:cursor-pointer">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none"
                                                    viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                            </button>
                                            <!-- 自定義 ToolTip -->
                                            <div v-if="tooltipVisible[item.uid]"
                                                class="absolute z-50 px-2 py-1 text-xs text-white bg-gray-800 rounded shadow-lg -left-16 top-8 whitespace-nowrap transform transition-opacity duration-200">
                                                刪除此記錄
                                                <!-- 箭頭 -->
                                                <div class="absolute bottom-full right-2 w-0 h-0 border-l-4 border-r-4 border-b-4 border-transparent border-b-gray-800"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 狀態標籤 -->
                                    <div class="mt-2 flex items-center space-x-4">
                                        <span
                                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-sm font-medium"
                                            :class="getStatusBadgeClass(item.status)">
                                            {{ getStatusText(item.status) }}
                                        </span>

                                        <!-- 錯誤訊息 -->
                                        <span v-if="item.errorMessage" class="text-sm text-red-600 truncate max-w-xs"
                                            :title="item.errorMessage">
                                            {{ item.errorMessage }}
                                        </span>
                                    </div>

                                    <!-- 詳細資訊 -->
                                    <div class="mt-2 grid grid-cols-2 sm:grid-cols-4 gap-2 text-sm text-gray-600">
                                        <div class="flex items-center text-green-600">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            長度: {{ item.duration ? item.duration.toFixed(2) + '秒' : 'N/A' }}
                                        </div>

                                        <div class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                                            </svg>
                                            {{ formatFileSize(item.fileSize) }}
                                        </div>

                                        <div v-if="item.whisperApiCost"
                                            class="text-sm flex items-center text-indigo-600">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3-.895 3-2-.343-2-3-2z" />
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M12 6v2m0 8v2M6 12h2m8 0h2" />
                                            </svg>
                                            費用: {{ formatCost(item.whisperApiCost) }} 美元
                                        </div>

                                        <div class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                            </svg>
                                            {{ new Date(item.createdAt).toLocaleString('zh-TW', {
                                                hour12: false, minute:
                                                    'numeric', hour: 'numeric', year: 'numeric', month: 'numeric', day:
                                            'numeric' }) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 分頁控制 -->
                <div v-if="totalPages > 1" class="flex justify-center mt-6">
                    <nav class="flex items-center space-x-2">
                        <button @click="changePage(currentPage - 1)" :disabled="currentPage === 1"
                            :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
                            class="px-3 py-1 rounded-md bg-indigo-100 text-indigo-700 hover:bg-indigo-200 transition-colors">
                            上一頁
                        </button>

                        <div v-for="page in displayedPageNumbers" :key="page" class="flex items-center">
                            <button v-if="page !== '...'" @click="changePage(Number(page))"
                                :class="{ 'bg-indigo-600 text-white': currentPage === page, 'bg-gray-100 text-gray-700 hover:bg-gray-200': currentPage !== page }"
                                class="w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                                {{ page }}
                            </button>
                            <span v-else class="px-1">...</span>
                        </div>

                        <button @click="changePage(currentPage + 1)" :disabled="currentPage === totalPages"
                            :class="{ 'opacity-50 cursor-not-allowed': currentPage === totalPages }"
                            class="px-3 py-1 rounded-md bg-indigo-100 text-indigo-700 hover:bg-indigo-200 transition-colors">
                            下一頁
                        </button>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import type { FileLog } from '../types';
import { ProcessingStatus } from '../types';
import { useHistoryStore } from '@/stores/historyStore';
import EnhancedProgressView from './EnhancedProgressView.vue';

// 使用 historyStore
const historyStore = useHistoryStore();

// 定義要發出的事件
const emit = defineEmits<{
    (e: 'open-history', guid: string): void;
    (e: 'history-deleted', guid: string): void;
}>();

// 組件掛載時載入歷史記錄
onMounted(async () => {
    console.log('📜 HistoryList 組件已掛載，載入歷史記錄...');
    await historyStore.loadHistoryList();
});

// 分頁相關
const itemsPerPage = 6; // 每頁顯示 6 筆記錄
const currentPage = ref(1);

// ToolTip 相關
const tooltipVisible = ref<Record<string, boolean>>({});

// 計算總頁數
const totalPages = computed(() => {
    return Math.ceil(historyStore.historyList.length / itemsPerPage);
});

// 計算當前頁面應該顯示的記錄
const paginatedHistoryList = computed(() => {
    const start = (currentPage.value - 1) * itemsPerPage;
    const end = start + itemsPerPage;
    return historyStore.historyList.slice(start, end);
});

// 計算應該顯示哪些頁碼（最多顯示 5 個頁碼，其餘用 ... 表示）
const displayedPageNumbers = computed(() => {
    const result = [];
    const maxVisiblePages = 5;

    if (totalPages.value <= maxVisiblePages) {
        // 如果總頁數少於或等於最大可見頁數，則顯示所有頁碼
        for (let i = 1; i <= totalPages.value; i++) {
            result.push(i);
        }
    } else {
        // 否則，顯示部分頁碼，其餘用 ... 表示
        result.push(1); // 始終顯示第一頁

        if (currentPage.value <= 3) {
            // 當前頁靠近開始
            for (let i = 2; i <= 4; i++) {
                result.push(i);
            }
            result.push('...');
            result.push(totalPages.value);
        } else if (currentPage.value >= totalPages.value - 2) {
            // 當前頁靠近結束
            result.push('...');
            for (let i = totalPages.value - 3; i <= totalPages.value; i++) {
                result.push(i);
            }
        } else {
            // 當前頁在中間
            result.push('...');
            for (let i = currentPage.value - 1; i <= currentPage.value + 1; i++) {
                result.push(i);
            }
            result.push('...');
            result.push(totalPages.value);
        }
    }

    return result;
});

// 切換頁面
const changePage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
    }
};

// 處理點擊歷史記錄項的函數
const handleHistoryItemClick = (guid: string) => {
    emit('open-history', guid);
};

// 檢查是否正在處理中
const isProcessing = (item: FileLog): boolean => {
    return item.status === ProcessingStatus.Pending || item.status === ProcessingStatus.Processing;
};

// 計算檔案長度顯示的函數
const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 格式化成本顯示的函數
const formatCost = (cost: number): string => {
    // 如果小數點後超過8位，則只顯示到小數點後8位
    if (cost.toString().includes('.') && cost.toString().split('.')[1].length > 8) {
        return cost.toFixed(8);
    }
    return cost.toString();
};

// 處理刪除歷史記錄的函數
const handleDeleteHistory = async (guid: string, fileName: string) => {
    // 顯示確認對話框
    if (!confirm(`確定要刪除「${fileName}」的歷史記錄嗎？\n\n此操作將永久刪除相關的所有檔案和資料，無法復原。`)) {
        return;
    }

    try {
        // 動態導入 removeHistory API
        const { removeHistory } = await import('@/api');

        // 呼叫刪除 API
        const result = await removeHistory(guid);

        // 發送刪除成功事件給父組件
        emit('history-deleted', guid);

        console.log('刪除成功:', result.message);
    } catch (error) {
        console.error('刪除歷史記錄時發生錯誤：', error);
        alert('刪除失敗，請稍後再試。');
    }
};

// 取得狀態標籤的樣式類別
const getStatusBadgeClass = (status: number) => {
    switch (status) {
        case ProcessingStatus.Completed:
            return 'bg-green-100 text-green-800';
        case ProcessingStatus.Failed:
            return 'bg-red-100 text-red-800';
        case ProcessingStatus.Cancelled:
            return 'bg-orange-100 text-orange-800';
        case ProcessingStatus.Pending:
            return 'bg-yellow-100 text-yellow-800';
        case ProcessingStatus.Processing:
            return 'bg-blue-100 text-blue-800';
        default:
            return 'bg-gray-100 text-gray-800';
    }
};

// 取得狀態文字
const getStatusText = (status: number) => {
    switch (status) {
        case ProcessingStatus.Pending:
            return '等待處理';
        case ProcessingStatus.Processing:
            return '處理中';
        case ProcessingStatus.Completed:
            return '已完成';
        case ProcessingStatus.Failed:
            return '處理失敗';
        case ProcessingStatus.Cancelled:
            return '已取消';
        default:
            return '未知狀態';
    }
};

// 格式化日期顯示
const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    const now = new Date();
    const diffTime = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
        return date.toLocaleTimeString('zh-TW', {
            hour: '2-digit',
            minute: '2-digit'
        });
    } else if (diffDays === 1) {
        return '昨天 ' + date.toLocaleTimeString('zh-TW', {
            hour: '2-digit',
            minute: '2-digit'
        });
    } else if (diffDays < 7) {
        return `${diffDays}天前`;
    } else {
        return date.toLocaleDateString('zh-TW', {
            month: 'numeric',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    }
};

// ToolTip 控制函數
const showTooltip = (uid: string) => {
    tooltipVisible.value[uid] = true;
};

const hideTooltip = (uid: string) => {
    tooltipVisible.value[uid] = false;
};
</script>