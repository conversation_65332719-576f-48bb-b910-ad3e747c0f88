<script setup lang="ts">
// --------------------------
// 雲端處理警語 Dialog 控制
// --------------------------
import { ref, computed, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import { whisperAPIurl, loadTranscribe } from '@/api';
import type { Transcribe } from '@/types';
import { marked } from 'marked';
import ResultView from './ResultView.vue';
import HistoryList from './HistoryList.vue';
import PreviewFeatures from './PreviewFeatures.vue';

// Pinia stores
import { useHistoryStore } from '@/stores/historyStore';
import { useToastStore } from '@/stores/toastStore';

// 使用 stores
const historyStore = useHistoryStore();
const toastStore = useToastStore();

// 控制雲端警語 Dialog 是否顯示
const showCloudWarningDialog = ref(false);

/**
 * 當選擇雲端處理時顯示警語 Dialog
 * @zh-TW 若選擇雲端處理，提醒用戶注意資料隱私
 */
function onCloudModeSelected() {
  if (processingMode.value === 'cloud') {
    showCloudWarningDialog.value = true;
  }
}

// 檔案上傳相關狀態
const file = ref<File | null>(null);
const fileInputRef = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);
const isProcessing = ref(false);

// 處理模式：雲端或地端
const processingMode = ref('local'); // 預設為地端處理

const activeTab = ref('transcript'); // 預設顯示逐字稿標籤

// 是否顯示上傳區域
const showUpload = computed(() => !showResult.value);

// 是否顯示結果
const showResult = ref(false);

// 是否顯示歷史記錄
const showHistory = ref(false);
const historyButtonLabel = ref('歷史記錄');

// 計時器相關狀態
const processingTime = ref(0);
const timerInterval = ref<number | null>(null);

// 結果相關狀態
const transcribe = ref<Transcribe>({} as Transcribe);

// 組件生命週期
onMounted(async () => {
  // 載入歷史記錄（store 會自動處理任務監控）
  await historyStore.loadHistoryList();

  // 清理過期的通知記錄（只保留最近的50個）
  const notifiedTasks = JSON.parse(localStorage.getItem('notifiedTasks') || '[]');
  if (notifiedTasks.length > 50) {
    localStorage.setItem('notifiedTasks', JSON.stringify(notifiedTasks.slice(-50)));
  }

  // 監聽任務完成事件
  window.addEventListener('taskCompleted', handleTaskCompleted);
  window.addEventListener('taskFailed', handleTaskFailed);
});

onUnmounted(() => {
  // 清理任務監控
  historyStore.cleanup();
  
  // 移除事件監聽器
  window.removeEventListener('taskCompleted', handleTaskCompleted);
  window.removeEventListener('taskFailed', handleTaskFailed);
});

// 任務完成事件處理
const handleTaskCompleted = (event: Event) => {
  const customEvent = event as CustomEvent;
  const { fileName } = customEvent.detail;
  toastStore.showToast('success', '任務完成', `檔案 ${fileName} 處理完成`);
};

// 任務失敗事件處理
const handleTaskFailed = (event: Event) => {
  const customEvent = event as CustomEvent;
  const { fileName, error } = customEvent.detail;
  toastStore.showToast('error', '任務失敗', `檔案 ${fileName} 處理失敗: ${error || '未知錯誤'}`);
};

// 切換歷史記錄顯示
const toggleHistory = () => {
  showHistory.value = !showHistory.value;
  if (showHistory.value) {
    historyButtonLabel.value = '關閉歷史記錄';
    console.log('📖 開啟歷史記錄頁面');
  } else {
    closeHistory();
  }
};

// 回呼函數用於開啟歷史記錄
const openHistory = async (guid: string) => {
  console.log(`開啟歷史記錄: ${guid}`);
  await loadHistoryItem(guid);
  closeHistory();
};

const closeHistory = () => {
  showHistory.value = false;
  historyButtonLabel.value = '歷史記錄';
};

// 載入特定歷史記錄
const loadHistoryItem = async (guid: string) => {
  // 這裡實作與Web API的整合
  showHistory.value = false;
  historyButtonLabel.value = '歷史記錄';
  // load Transcribe
  transcribe.value = await loadTranscribe(guid);
  // 切換到結果標籤
  activeTab.value = 'transcript';
  showResult.value = true;
};

// 處理歷史記錄刪除事件
const handleHistoryDeleted = async (guid: string) => {
  // 顯示成功訊息
  toastStore.showToast('success', '刪除成功', '歷史記錄已成功刪除');

  // 從 store 中移除項目
  historyStore.removeHistoryItem(guid);

  // 如果當前顯示的結果是被刪除的記錄，則清除結果顯示
  if (transcribe.value.guid === guid) {
    clearFile();
  }
};

// 顯示檔案大小的格式化
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 檔案名稱和大小的顯示
const fileInfo = computed(() => {
  if (!file.value) return null;
  return {
    name: file.value.name,
    size: formatFileSize(file.value.size),
    type: file.value.type
  };
});

// 處理檔案拖放
const handleDragOver = (e: DragEvent) => {
  e.preventDefault();
  isDragging.value = true;
};

// 處理拖放離開
const handleDragLeave = (e: DragEvent) => {
  e.preventDefault();
  isDragging.value = false;
};

// 處理檔案放下
const handleDrop = (e: DragEvent) => {
  e.preventDefault();
  isDragging.value = false;

  if (e.dataTransfer?.files.length) {
    const droppedFile = e.dataTransfer.files[0];
    file.value = droppedFile;
  }
};

// 處理檔案選擇
const handleFileSelect = (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (target.files?.length) {
    const selectedFile = target.files[0];
    file.value = selectedFile;
  }
};

// 觸發檔案選擇對話框
const triggerFileInput = () => {
  showHistory.value = false;
  historyButtonLabel.value = '歷史記錄';
  fileInputRef.value?.click();
};

// 清除已選擇的檔案和結果
const clearFile = () => {
  file.value = null;
  if (fileInputRef.value) {
    fileInputRef.value.value = '';
  }

  // 清空所有結果相關的狀態變數
  transcribe.value = {} as Transcribe;

  // 重置計時器
  processingTime.value = 0;
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
  showResult.value = false;
  showHistory.value = false;
  historyButtonLabel.value = '歷史記錄';
};

// 處理檔案上傳和處理
const processFile = async () => {
  if (!file.value) return;

  isProcessing.value = true;
  processingTime.value = 0;
  showResult.value = false;
  showHistory.value = false;

  console.log(`使用${processingMode.value === 'local' ? '地端' : '雲端'}處理模式`);

  // 設置計時器，每秒更新處理時間
  timerInterval.value = setInterval(() => {
    processingTime.value++;
  }, 1000);

  try {
    const formData = new FormData();
    formData.append('file', file.value);
    formData.append('mode', processingMode.value);

    const response = await axios.post(whisperAPIurl, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    // 新的 API 回應格式: { taskId, message, status }
    const taskInfo = response.data;

    if (taskInfo.taskId) {
      // 顯示成功訊息
      toastStore.showToast('success', '上傳成功', taskInfo.Message || '檔案已成功上傳，正在背景處理中');

      // 將任務加入監控清單
      historyStore.addMonitoringTask(taskInfo.taskId);

      // 清除檔案，讓用戶可以上傳新檔案
      clearFile();

      // 刷新歷史記錄以顯示新任務
      await historyStore.loadHistoryList();
    } else {
      // 處理舊格式回應（向下相容）
      transcribe.value = response.data;
      if (transcribe.value.summary != '') {
        transcribe.value.summary = marked.parse(transcribe.value.summary).toString();
      }
      showResult.value = true;
      activeTab.value = 'transcript';
    }
  } catch (error) {
    showResult.value = false;
    console.error('處理檔案時發生錯誤:', error);

    // 使用 Toast 顯示錯誤訊息而非 alert
    if (axios.isAxiosError(error) && error.response) {
      const errorMessage = error.response.data?.message || error.response.data || '未知錯誤';
      toastStore.showToast('error', '上傳失敗', `處理檔案時發生錯誤：${errorMessage}`);
    } else {
      toastStore.showToast('error', '上傳失敗', '處理檔案時發生錯誤，請稍後再試。');
    }
  } finally {
    // 停止計時器但保留計時結果
    if (timerInterval.value) {
      clearInterval(timerInterval.value);
      timerInterval.value = null;
    }
    isProcessing.value = false;
  }
};

// 格式化時間為分:秒格式
const formatTime = (seconds: number): string => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 切換標籤
const switchTab = (tab: string) => {
  activeTab.value = tab;
};
</script>

<template>
  <div class="container mx-auto px-4 py-8 max-w-5xl">
    <!-- 標題區域 -->
    <div class="flex justify-between items-center mb-8">
      <h1 class="text-3xl font-bold text-center text-gray-800">會議小幫手</h1>
      <button @click="toggleHistory"
        class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
        {{ historyButtonLabel }}
      </button>
    </div>

    <!-- 歷史記錄清單 - 直接使用 store -->
    <HistoryList v-if="showHistory" @open-history="openHistory"
      @history-deleted="handleHistoryDeleted"></HistoryList>

    <!-- 上傳區域 -->
    <div v-if="showUpload" class="mb-8">
      <div class="border-2 border-dashed rounded-lg p-8 text-center" :class="{
        'border-blue-400 bg-blue-50': isDragging,
        'border-gray-300 hover:border-blue-300': !isDragging
      }" @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop">
        <div v-if="!file">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400" fill="none"
            viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          <p class="mt-4 text-lg">拖放檔案至此處，或</p>
          <button @click="triggerFileInput"
            class="mt-2 px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
            選擇檔案
          </button>
          <input ref="fileInputRef" type="file" class="hidden" @change="handleFileSelect" accept="video/*, audio/*" />
          <p class="mt-4 text-sm text-gray-500">
            支援的格式: 所有 視訊 和 音訊 檔案
          </p>
        </div>

        <div v-else class="py-4">
          <div class="flex items-center justify-center mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-500 mr-2" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span class="text-lg font-medium">已選擇檔案</span>
          </div>

          <div class="bg-gray-100 rounded-md p-4 mb-4 max-w-md mx-auto text-left">
            <p class="font-medium truncate">{{ fileInfo?.name }}</p>
            <p class="text-sm text-gray-600">{{ fileInfo?.size }}</p>
          </div>

          <div class="flex justify-center space-x-4">
            <button @click="clearFile"
              class="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300">
              清除
            </button>
            <button @click="processFile"
              class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300"
              :disabled="isProcessing">
              <span v-if="isProcessing" class="flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                  viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                處理中...
              </span>
              <span v-else>開始處理</span>
            </button>
            <span v-if="processingTime > 0" class="py-2 text-gray-600">
              處理時間: {{ formatTime(processingTime) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 結果顯示區域 -->
    <ResultView v-if="showResult" :transcribe="transcribe" @clear="clearFile" />

    <!-- 功能預覽區域 - 在最下方顯示所有功能 -->
    <PreviewFeatures v-if="!showResult" />

  </div>
  <!-- 雲端處理警語 Dialog -->
  <div v-if="showCloudWarningDialog" class="fixed inset-0 flex items-center justify-center z-50 bg-black/40">
    <div class="bg-white rounded-lg shadow-lg p-6 max-w-xs w-full text-center">
      <div class="mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-yellow-500 mx-auto" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M13 16h-1v-4h-1m1-4h.01M12 2a10 10 0 100 20 10 10 0 000-20z" />
        </svg>
      </div>
      <p class="text-gray-800 text-base font-medium mb-4">
        提醒您：若檔案包含機密或敏感資訊，請勿選擇雲端處理，以保障資料安全。
      </p>
      <button @click="showCloudWarningDialog = false"
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">確定</button>
    </div>
  </div>

  <!-- Toast 通知組件 -->
  <div class="fixed top-4 right-4 z-50 space-y-2">
    <div v-for="toast in toastStore.toasts" :key="toast.id" :class="[
      'transform transition-all duration-300 ease-in-out',
      'w-80 md:w-96 shadow-lg rounded-lg pointer-events-auto overflow-hidden',
      toast.show ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0',
      {
        'bg-green-50 border-l-4 border-green-400': toast.type === 'success',
        'bg-red-50 border-l-4 border-red-400': toast.type === 'error',
        'bg-blue-50 border-l-4 border-blue-400': toast.type === 'info'
      }
    ]">
      <div class="p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <!-- Success Icon -->
            <svg v-if="toast.type === 'success'" class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clip-rule="evenodd" />
            </svg>
            <!-- Error Icon -->
            <svg v-else-if="toast.type === 'error'" class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clip-rule="evenodd" />
            </svg>
            <!-- Info Icon -->
            <svg v-else class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
              fill="currentColor">
              <path fill-rule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3 w-0 flex-1">
            <p :class="[
              'text-sm font-medium',
              {
                'text-green-800': toast.type === 'success',
                'text-red-800': toast.type === 'error',
                'text-blue-800': toast.type === 'info'
              }
            ]">
              {{ toast.title }}
            </p>
            <p :class="[
              'mt-1 text-sm',
              {
                'text-green-700': toast.type === 'success',
                'text-red-700': toast.type === 'error',
                'text-blue-700': toast.type === 'info'
              }
            ]">
              {{ toast.message }}
            </p>
          </div>
          <div class="ml-4 flex-shrink-0 flex">
            <button @click="toastStore.hideToast(toast.id)" :class="[
              'inline-flex rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2',
              {
                'text-green-400 hover:text-green-600 focus:ring-green-500': toast.type === 'success',
                'text-red-400 hover:text-red-600 focus:ring-red-500': toast.type === 'error',
                'text-blue-400 hover:text-blue-600 focus:ring-blue-500': toast.type === 'info'
              }
            ]">
              <span class="sr-only">關閉</span>
              <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                  d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                  clip-rule="evenodd" />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
