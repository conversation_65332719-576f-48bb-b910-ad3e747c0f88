<template>
    <div>
        <div id="resultView" class="bg-white rounded-lg shadow-md overflow-hidden">
            <!-- 標籤列 -->
            <div class="flex border-b">
                <button v-for="(label, tab) in {
                    transcript: '逐字稿',
                    summary: '摘要',
                    subtitle: '字幕',
                    translation: '翻譯'
                }" :key="tab" @click="switchTab(tab)" class="px-6 py-3 text-sm font-medium focus:outline-none" :class="{
                    'border-b-2 border-blue-500 text-blue-600': activeTab === tab,
                    'text-gray-600 hover:text-gray-800 hover:bg-gray-50': activeTab !== tab
                }">
                    {{ label }}
                </button>
            </div>

            <!-- 標籤內容 -->
            <div class="p-6">
                <!-- 控制按鈕 -->
                <div class="flex justify-between mb-6">
                    <button @click="clearFile"
                        class="px-4 py-2 border border-gray-300 text-white bg-purple-500 rounded-md hover:bg-purple-600 hover:text-yellow-100 focus:outline-none focus:ring-2 focus:ring-gray-300">
                        上傳新檔案
                    </button>
                    <button @click="downloadFile"
                        class="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-300">
                        下載結果
                    </button>
                </div>

                <!-- 逐字稿 -->
                <div v-if="activeTab === 'transcript'" class="bg-gray-50 p-4 rounded-md">
                    <h2 class="text-xl font-semibold mb-4">逐字稿</h2>
                    <div class="whitespace-pre-line text-gray-700">
                        {{ props.transcribe.transcript }}
                    </div>
                </div>

                <!-- 摘要 -->
                <div v-if="activeTab === 'summary'" class="bg-gray-50 p-4 rounded-md">
                    <h2 class="text-xl font-semibold mb-4">摘要</h2>
                    <div class="markdown-content text-gray-700" v-html="summaryHtml">
                    </div>
                </div>

                <!-- 字幕 -->
                <div v-if="activeTab === 'subtitle'" class="bg-gray-50 p-4 rounded-md">
                    <h2 class="text-xl font-semibold mb-4">字幕</h2>
                    <div class="whitespace-pre-line text-gray-700">
                        {{ props.transcribe.srt }}
                    </div>
                </div>

                <!-- 翻譯 -->
                <div v-if="activeTab === 'translation'" class="bg-gray-50 p-4 rounded-md">
                    <h2 class="text-xl font-semibold mb-4">翻譯</h2>
                    <div class="whitespace-pre-line text-gray-700">
                        {{ props.transcribe.translation }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, computed } from 'vue';
import type { Transcribe } from '@/types';
import axios from 'axios';
import { downloadAPIurl } from '../api';
import { marked } from 'marked';

// 設定 marked 選項
marked.setOptions({
    breaks: true, // 支援換行
    gfm: true,    // 支援 GitHub Flavored Markdown
});

const props = defineProps<{
    transcribe: Transcribe;
}>();

const activeTab = ref('transcript');

// 將 Markdown 轉換為 HTML 的 computed 屬性
const summaryHtml = computed(() => {
    if (!props.transcribe.summary) {
        return '';
    }
    try {
        return marked(props.transcribe.summary);
    } catch (error) {
        console.error('Markdown 轉換錯誤：', error);
        return props.transcribe.summary; // 如果轉換失敗，返回原始文字
    }
});

// 切換標籤
const switchTab = (tab: string) => {
    activeTab.value = tab;
};

// 清除已選擇的檔案和結果
const clearFile = () => {
    emit('clear');
};

/**
 * 獲取檔案的主檔名（不含副檔名）
 * @param filename 完整檔名（含副檔名）
 * @returns 不含副檔名的主檔名
 */
const getMainFileName = (filename: string): string => {
    // 找到最後一個點的位置
    const lastDotIndex = filename.lastIndexOf('.');
    // 如果沒有找到點或點在開頭，則返回整個檔名
    if (lastDotIndex === -1 || lastDotIndex === 0) {
        return filename;
    }
    // 返回最後一個點之前的所有內容
    return filename.substring(0, lastDotIndex);
};

// 下載結果檔案
const downloadFile = async () => {
    // 從檔名中獲取主檔名（不含副檔名）
    const fileName = getMainFileName(props.transcribe.fileName);
    try {
        const response = await axios.get(downloadAPIurl, {
            params: {
                guid: props.transcribe.guid
            },
            responseType: 'blob'
        });

        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${fileName}.zip`);
        document.body.appendChild(link);
        link.click();
        link.remove();
    } catch (error) {
        console.error('下載檔案時發生錯誤：', error);
    }
};

const emit = defineEmits<{
    (e: 'clear'): void;
}>();
</script>

<style scoped>
.markdown-content {
    line-height: 1.6;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #374151;
}

.markdown-content h1 {
    font-size: 1.875rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
}

.markdown-content h2 {
    font-size: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.25rem;
}

.markdown-content h3 {
    font-size: 1.25rem;
}

.markdown-content h4 {
    font-size: 1.125rem;
}

.markdown-content p {
    margin-bottom: 1rem;
}

.markdown-content ul,
.markdown-content ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.markdown-content li {
    margin-bottom: 0.25rem;
}

.markdown-content blockquote {
    border-left: 4px solid #3b82f6;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    background-color: #f8fafc;
    padding: 1rem;
    border-radius: 0.375rem;
}

.markdown-content code {
    background-color: #f1f5f9;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
}

.markdown-content pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
}

.markdown-content pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
}

.markdown-content strong {
    font-weight: 600;
}

.markdown-content em {
    font-style: italic;
}

.markdown-content a {
    color: #3b82f6;
    text-decoration: underline;
}

.markdown-content a:hover {
    color: #1d4ed8;
}

.markdown-content table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
}

.markdown-content th,
.markdown-content td {
    border: 1px solid #d1d5db;
    padding: 0.5rem;
    text-align: left;
}

.markdown-content th {
    background-color: #f3f4f6;
    font-weight: 600;
}

.markdown-content hr {
    border: none;
    border-top: 1px solid #d1d5db;
    margin: 2rem 0;
}
</style>