<script setup lang="ts">
// 不需要任何 props 或 emits，因為這是純展示組件
</script>

<template>
    <div class="mt-8 mb-8">
      <h2 class="text-2xl font-semibold text-center text-gray-700 mb-4">我們能為您的會議做什麼</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- 逐字稿功能 -->
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-start mb-4">
            <div class="bg-blue-100 p-3 rounded-full mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-800">逐字稿生成</h3>
              <p class="text-gray-600 mt-1">將您的會議音訊轉換為精確的文字記錄，捕捉每一個重要細節。</p>
            </div>
          </div>
        </div>

        <!-- 會議摘要功能 -->
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-start mb-4">
            <div class="bg-purple-100 p-3 rounded-full mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-800">會議摘要</h3>
              <p class="text-gray-600 mt-1">智能識別會議的項目內容，確保所有參與者明確了解會議內容。</p>
            </div>
          </div>
        </div>

        <!-- 字幕功能 -->
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-start mb-4">
            <div class="bg-green-100 p-3 rounded-full mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-800">字幕</h3>
              <p class="text-gray-600 mt-1">替您的會議製作為精確的字幕檔，記錄每一個重要細節發生時間。</p>
            </div>
          </div>
        </div>

        <!-- 文字翻譯功能 -->
        <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div class="flex items-start mb-4">
            <div class="bg-yellow-100 p-3 rounded-full mr-4">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-800">文字翻譯</h3>
              <p class="text-gray-600 mt-1">將會議內容翻譯成不同語言，打破語言障礙，促進全球團隊協作。</p>
            </div>
          </div>
        </div>
      </div>
    </div>

</template>
