<template>
  <div class="enhanced-progress-view">
    <!-- 顯示基本進度條 -->
    <div class="basic-progress bg-white rounded-lg shadow-sm border p-6 cursor-pointer"
      @click="emit('progress-click')">
      <div class="flex justify-between items-center mb-2">
        <h3 class="text-lg font-semibold text-gray-800">{{ fileName }}</h3>
        <span class="text-sm text-gray-600">{{ taskProgress }}%</span>
      </div>

      <div class="w-full bg-gray-200 rounded-full h-3 mb-2">
        <div class="bg-blue-600 h-3 rounded-full transition-all duration-300 ease-out"
          :style="{ width: `${taskProgress}%` }"></div>
      </div>

      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">{{ statusDescription }}</div>
        <button v-if="shouldAutoRefresh" @click.stop="refreshProgress"
          class="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors">
          重新整理
        </button>
      </div>
    </div>

    <!-- 載入中狀態 -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="flex items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span class="ml-2 text-gray-600">載入進度資訊...</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { useHistoryStore } from '@/stores/historyStore';
import { useToastStore } from '@/stores/toastStore';

// Props
interface Props {
  taskId: string;
  fileName: string;
  status: number; // ProcessingStatus
  isCompleted: boolean;
  errorMessage?: string | null | undefined;
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  'progress-click': [];
}>();

// Stores
const historyStore = useHistoryStore();
const toastStore = useToastStore();

// State
const isLoading = ref(false);
const refreshInterval = ref<number | null>(null);
const taskProgress = ref(0);

// Computed
const statusDescription = computed(() => {
  switch (props.status) {
    case 0: return '等待處理';
    case 1: return '處理中';
    case 2: return '處理完成';
    case 3: return '處理失敗';
    case 4: return '已取消';
    default: return '未知狀態';
  }
});

const shouldAutoRefresh = computed(() => {
  return props.status === 0 || props.status === 1; // Pending or Processing
});

// Methods
const refreshProgress = async () => {
  if (isLoading.value) return;

  isLoading.value = true;
  try {
    const progress = await historyStore.getTaskProgress(props.taskId);
    if (typeof progress === 'number') {
      taskProgress.value = progress;
    }
  } catch (error) {
    console.error('重新整理進度失敗:', error);
    toastStore.showToast('error', '錯誤', '無法載入進度資訊');
  } finally {
    isLoading.value = false;
  }
};

const updateProgressFromStatus = () => {
  // 根據狀態計算簡單的進度百分比
  switch (props.status) {
    case 0: taskProgress.value = 0; break;   // Pending
    case 1: taskProgress.value = 50; break;  // Processing
    case 2: taskProgress.value = 100; break; // Completed
    case 3: taskProgress.value = 0; break;   // Failed
    case 4: taskProgress.value = 0; break;   // Cancelled
    default: taskProgress.value = 0;
  }
};

const startAutoRefresh = () => {
  if (refreshInterval.value) return;

  refreshInterval.value = setInterval(() => {
    if (shouldAutoRefresh.value) {
      refreshProgress();
    } else {
      stopAutoRefresh();
    }
  }, 10000); // 每10秒重新整理
};

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
};

// Lifecycle
onMounted(async () => {
  // 初始設定進度
  updateProgressFromStatus();

  // 如果任務還在處理中，嘗試載入詳細進度並啟動自動重新整理
  if (shouldAutoRefresh.value) {
    await refreshProgress();
    startAutoRefresh();
  }
});

onUnmounted(() => {
  stopAutoRefresh();
});

// Watch status changes
import { watch } from 'vue';
watch(() => props.status, () => {
  updateProgressFromStatus();
  
  if (shouldAutoRefresh.value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
});
</script>

<style scoped>
@reference "../assets/main.css";

.enhanced-progress-view {
  @apply relative;
}

.loading-overlay {
  @apply absolute inset-0 bg-white/75 flex items-center justify-center rounded-lg;
}

.basic-progress .bg-blue-600 {
  background: linear-gradient(90deg, #3B82F6 0%, #1D4ED8 100%);
}
</style>