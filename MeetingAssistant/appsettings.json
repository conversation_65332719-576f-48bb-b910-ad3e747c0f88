{"ConnectionStrings": {"MIS": "Ohc68gCEYfQVBhDVnRnP8cBx1C1fzpcbe7RN8WU0UwkLRL3a/6QhRY/0gicWmUKyLWORgZCcR/gYFsH3Dze8fqAB09Ia+lnnawScUJc8iY3Ft0yfkkrRTClW3QC6rnJaN+qgM7BBx6qid8nXe5LkShNuXuSLSvd0ahwv1tLJ3ixuCH140u+slZqyJKBXoVnWftdzK2iwrPsyRzZntbZE+u2wHNA+IB2K7uQE5o2bKGLBr+B/C6oG74jB2MUS6BzJt/kaZSxwp+lAvKxAdrafE03tcplHu5Y3zFUyyQrbVnRWITVhEXi/B6U/v7r/JlJcxxkzwzdKH5mIqkVvffgHImdgrySRMA0rt73NnZSkAOGYKQ3wnKUE6EhiAm0rW5XWq0/sq5xiMsqXiHZgcvz4jBJP9QM9kVrHCakF+jdv1oLyVIh0f+sshQd//ryxU46q/56ixfDOFDX+xhbpW1RkzM9hAfmBJnZAWO3zaHmnEbK/Z0ecw3rEj+d7XBwrA0b+Klsjh6pP8X41uW6vfJ7TfaQvPrU5pR+bno7YpXRz6bIKHEXBa3KVZt+lx6BBxTz88NcNtg7PX8jaoq4jmDMu8LjASg+NsGkJEo+1lyDevMLjbBznf7WuiZXKl0QHyW8/Soy8Nr0U+jby2KVFPwiCXX5nVIj0XceEzpE6JP8nfQk=", "MeetingAssistant": "Server=localhost;Database=MeetingAssistant;User Id=sa;Password=superman;Encrypt=Yes;TrustServerCertificate=Yes;Application Name=MeetingAssistant;"}, "FileDir": "d:\\WWW\\MeetingAssistant\\Files", "CallOpenAI": false, "OpenAiApiUrl": "https://api.openai.com/v1/chat/completions", "OllamaApiUrl": "http://************:11434/v1/chat/completions", "WhisperApiUrl": "https://api.openai.com/v1/audio/transcriptions", "OnPremisesApiUrl": "http://************:22434/transcribe", "OnPremisesTranslationModel": "gemma3:4b", "OnPremisesSummaryModel": "gemma3:4b", "APIKey": {"OPENAI_API_KEY": "********************************************************************************************************************************************************************"}, "WhisperTranslationModel": "gpt-4.1", "WhisperSummaryModel": "gpt-4.1", "BackgroundTasks": {"MaxConcurrentTasks": 2, "MaxRetryCount": 3, "RetryDelayMinutes": [1, 5, 15], "TaskTimeoutMinutes": 60}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*"}