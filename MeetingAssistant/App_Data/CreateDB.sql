USE [master]
GO
/****** Object:  Database [MeetingAssistant]    Script Date: 2025/3/21 下午 01:25:01 ******/
CREATE DATABASE [MeetingAssistant]
GO
ALTER DATABASE [MeetingAssistant] SET COMPATIBILITY_LEVEL = 140
GO
IF (1 = FULLTEXTSERVICEPROPERTY('IsFullTextInstalled'))
begin
EXEC [MeetingAssistant].[dbo].[sp_fulltext_database] @action = 'enable'
end
GO
ALTER DATABASE [MeetingAssistant] SET ANSI_NULL_DEFAULT OFF 
GO
ALTER DATABASE [MeetingAssistant] SET ANSI_NULLS OFF 
GO
ALTER DATABASE [MeetingAssistant] SET ANSI_PADDING OFF 
GO
ALTER DATABASE [MeetingAssistant] SET ANSI_WARNINGS OFF 
GO
ALTER DATABASE [MeetingAssistant] SET ARITHABORT OFF 
GO
ALTER DATABASE [MeetingAssistant] SET AUTO_CLOSE OFF 
GO
ALTER DATABASE [MeetingAssistant] SET AUTO_SHRINK OFF 
GO
ALTER DATABASE [MeetingAssistant] SET AUTO_UPDATE_STATISTICS ON 
GO
ALTER DATABASE [MeetingAssistant] SET CURSOR_CLOSE_ON_COMMIT OFF 
GO
ALTER DATABASE [MeetingAssistant] SET CURSOR_DEFAULT  GLOBAL 
GO
ALTER DATABASE [MeetingAssistant] SET CONCAT_NULL_YIELDS_NULL OFF 
GO
ALTER DATABASE [MeetingAssistant] SET NUMERIC_ROUNDABORT OFF 
GO
ALTER DATABASE [MeetingAssistant] SET QUOTED_IDENTIFIER OFF 
GO
ALTER DATABASE [MeetingAssistant] SET RECURSIVE_TRIGGERS OFF 
GO
ALTER DATABASE [MeetingAssistant] SET  DISABLE_BROKER 
GO
ALTER DATABASE [MeetingAssistant] SET AUTO_UPDATE_STATISTICS_ASYNC OFF 
GO
ALTER DATABASE [MeetingAssistant] SET DATE_CORRELATION_OPTIMIZATION OFF 
GO
ALTER DATABASE [MeetingAssistant] SET TRUSTWORTHY OFF 
GO
ALTER DATABASE [MeetingAssistant] SET ALLOW_SNAPSHOT_ISOLATION OFF 
GO
ALTER DATABASE [MeetingAssistant] SET PARAMETERIZATION SIMPLE 
GO
ALTER DATABASE [MeetingAssistant] SET READ_COMMITTED_SNAPSHOT OFF 
GO
ALTER DATABASE [MeetingAssistant] SET HONOR_BROKER_PRIORITY OFF 
GO
ALTER DATABASE [MeetingAssistant] SET RECOVERY SIMPLE 
GO
ALTER DATABASE [MeetingAssistant] SET  MULTI_USER 
GO
ALTER DATABASE [MeetingAssistant] SET PAGE_VERIFY CHECKSUM  
GO
ALTER DATABASE [MeetingAssistant] SET DB_CHAINING OFF 
GO
ALTER DATABASE [MeetingAssistant] SET FILESTREAM( NON_TRANSACTED_ACCESS = OFF ) 
GO
ALTER DATABASE [MeetingAssistant] SET TARGET_RECOVERY_TIME = 60 SECONDS 
GO
ALTER DATABASE [MeetingAssistant] SET DELAYED_DURABILITY = DISABLED 
GO
ALTER DATABASE [MeetingAssistant] SET ACCELERATED_DATABASE_RECOVERY = OFF  
GO
ALTER DATABASE [MeetingAssistant] SET QUERY_STORE = ON
GO
ALTER DATABASE [MeetingAssistant] SET QUERY_STORE (OPERATION_MODE = READ_WRITE, CLEANUP_POLICY = (STALE_QUERY_THRESHOLD_DAYS = 30), DATA_FLUSH_INTERVAL_SECONDS = 900, INTERVAL_LENGTH_MINUTES = 60, MAX_STORAGE_SIZE_MB = 1000, QUERY_CAPTURE_MODE = AUTO, SIZE_BASED_CLEANUP_MODE = AUTO, MAX_PLANS_PER_QUERY = 200, WAIT_STATS_CAPTURE_MODE = ON)
GO
USE [MeetingAssistant]
GO
/****** Object:  Table [dbo].[FileLog]    Script Date: 2025/3/21 下午 01:25:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[FileLog](
	[Uid] [uniqueidentifier] NOT NULL,
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmpNo] [nvarchar](20) NOT NULL,
	[Name] [nvarchar](10) NOT NULL,
	[DeptNo] [int] NOT NULL,
	[Department] [nvarchar](32) NOT NULL,
	[IP] [nvarchar](64) NOT NULL,
	[Hostname] [nvarchar](64) NULL,
	[UploadTime] [datetime2](7) NOT NULL,
	[FileName] [nvarchar](256) NOT NULL,
	[FileSize] [bigint] NOT NULL,
	[Duration] [float] NOT NULL,
	[WhisperApiCost] [float] NOT NULL,
	[IsCompleted] [bit] NOT NULL,
	[Status] [int] NOT NULL,
	[ProcessedTime] [time](7) NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[UpdatedAt] [datetime2](7) NULL,
	[Remark] [nvarchar](512) NULL,
 CONSTRAINT [PK_FileLog] PRIMARY KEY CLUSTERED 
(
	[Uid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO
/****** Object:  Table [dbo].[UsageLog]    Script Date: 2025/3/21 下午 01:25:01 ******/
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO
CREATE TABLE [dbo].[UsageLog](
	[Uid] [uniqueidentifier] NOT NULL,
	[Id] [int] IDENTITY(1,1) NOT NULL,
	[EmpNo] [nvarchar](20) NOT NULL,
	[Name] [nvarchar](10) NOT NULL,
	[DeptNo] [int] NOT NULL,
	[Department] [nvarchar](32) NOT NULL,
	[IP] [nvarchar](64) NOT NULL,
	[Hostname] [nvarchar](64) NULL,
	[CallTime] [datetime2](7) NOT NULL,
	[APIName] [nvarchar](64) NOT NULL,
	[ErrorMessage] [nvarchar](512) NULL,
	[ResponseData] [nvarchar](max) NULL,
	[CreatedAt] [datetime2](7) NOT NULL,
	[UpdatedAt] [datetime2](7) NULL,
	[Remark] [nvarchar](500) NULL,
 CONSTRAINT [PK_UsageLog] PRIMARY KEY CLUSTERED 
(
	[Uid] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO
USE [master]
GO
ALTER DATABASE [MeetingAssistant] SET  READ_WRITE 
GO
