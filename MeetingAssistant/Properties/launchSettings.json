{"$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": true, "anonymousAuthentication": false, "iisExpress": {"applicationUrl": "http://localhost:34595", "sslPort": 44351}}, "profiles": {"MeetingAssistant": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:7156;http://localhost:5156", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}