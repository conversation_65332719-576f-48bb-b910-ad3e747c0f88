# 專案結構說明

這份文件提供了 MeetingAssistant 專案的整體結構、主要功能模組以及核心檔案的說明。

## 主要功能模組

- **前端 (ClientApp)**: 使用 Vue 3 + TypeScript + Vite 開發，負責提供使用者介面，包括檔案上傳、進度追蹤、歷史記錄查詢和結果展示。
- **後端 (MeetingAssistant)**: ASP.NET Core Web API，作為系統的主要入口，處理 HTTP 請求、使用者驗證，並透過背景任務協調轉錄流程。
- **共用工具庫 (MeetingUtility)**: 封裝了專案中共享的核心商業邏輯、資料模型和介面，例如轉錄處理器、翻譯/摘要輔助工具等。
- **地端 Whisper 服務 (OnPremisesWhisper)**: 一個獨立的 ASP.NET Core 服務，用於提供本地部署的語音轉文字 API，確保資料的私密性。
- **Python 工具集 (PythonWhisper)**: 包含一系列 Python 腳本，用於快速原型開發、模型測試或提供特定的 AI 功能（如摘要、翻譯）。
- **測試 (Tests)**: 包含對後端邏輯的單元測試和整合測試，確保程式碼品質。

## 專案檔案列表

```text
MeetingAssistant/                      # 專案根目錄
├── MeetingAssistant.sln               # Visual Studio 解決方案檔，整合所有專案
│
├── MeetingAssistant/                  # 主專案 - ASP.NET Core Web API 與前端
│   ├── Controllers/
│   │   └── WhisperController.cs       # 核心 API 控制器，處理檔案上傳、歷史記錄、下載等
│   ├── ClientApp/                     # 前端 Vue.js 專案
│   │   ├── src/
│   │   │   ├── App.vue                # Vue 根組件
│   │   │   ├── main.ts                # 前端應用程式進入點
│   │   │   ├── components/            # Vue 組件
│   │   │   │   ├── AssistantIndex.vue # 主介面，包含上傳和結果顯示區域
│   │   │   │   ├── HistoryList.vue    # 歷史記錄列表組件
│   │   │   │   └── ResultView.vue     # 顯示轉錄/摘要結果的組件
│   │   │   └── stores/                # Pinia 狀態管理
│   │   │       ├── historyStore.ts    # 管理歷史記錄狀態
│   │   │       └── toastStore.ts      # 管理提示訊息狀態
│   │   ├── package.json               # 前端依賴套件設定
│   │   └── vite.config.ts             # Vite 建置設定
│   ├── Services/                      # 背景服務相關實作
│   │   ├── TranscribeBackgroundService.cs # 主要的背景託管服務，監聽任務隊列
│   │   ├── TranscribeTaskProcessor.cs # 實際執行轉錄任務的處理器
│   │   ├── TranscribeTaskQueue.cs   # 記憶體中的任務隊列，用於緩衝轉錄請求
│   │   └── TaskRetryPolicy.cs       # 任務失敗時的重試策略
│   ├── Models/
│   │   └── FileLogDbContext.cs        # Entity Framework Core 的資料庫上下文
│   ├── Program.cs                     # 應用程式進入點，設定服務與中介軟體
│   └── appsettings.json               # 應用程式主要設定檔
│
├── MeetingUtility/                    # 共用類別庫專案
│   ├── Interfaces/                    # 定義專案中使用的介面
│   │   ├── IWhisperProcessor.cs     # 定義轉錄處理器的合約
│   │   ├── ITranscribeTaskQueue.cs  # 任務隊列介面
│   │   └── ITranscribeTaskProcessor.cs # 任務處理器介面
│   ├── Models/                        # 共用的資料模型
│   │   ├── FileLog.cs                 # 檔案處理記錄的資料庫實體
│   │   ├── TranscribeTask.cs          # 轉錄任務的資料結構
│   │   ├── ProcessingStatus.cs        # 處理狀態的枚舉
│   │   └── Transcribe.cs              # 轉錄結果的資料模型
│   ├── Processors/                    # 轉錄處理器的具體實作
│   │   ├── WhisperProcessorBase.cs    # 轉錄器的抽象基底類別，包含共用邏輯 (如 ffmpeg 操作)
│   │   ├── WhisperApiProcessor.cs     # 使用 OpenAI 雲端 API 的處理器
│   │   └── WhisperOnPremisesProcessor.cs # 使用本地服務的處理器
│   └── MeetingHelper.cs               # 核心輔助類別，處理翻譯、摘要等
│
├── OnPremisesWhisper/                 # 地端 Whisper API 服務專案
│   ├── Controllers/
│   │   └── WhisperController.cs       # 提供本地轉錄服務的 API 端點
│   └── Program.cs                     # 服務進入點
│
├── PythonWhisper/                     # Python 相關的實驗性或輔助腳本
│   ├── requirements.txt               # Python 依賴列表
│   ├── fast.py                        # 使用 FastAPI 實現的服務
│   └── flask-whisper.py               # 使用 Flask 實現的服務
│
└── Tests/                             # 測試專案
    ├── MeetingHelperTests.cs          # MeetingHelper 的單元測試
    ├── TranscribeTaskProcessorTests.cs # 任務處理器的單元測試
    └── WhisperProcessorTests.cs       # 不同轉錄處理器的測試
```
