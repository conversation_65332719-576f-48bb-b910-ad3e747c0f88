# 會議助手專案架構與UML分析

## 專案概述

會議助手是一個企業內部的會議輔助系統，主要功能包括：

- 音視頻檔案上傳與處理
- 語音轉文字（使用 Whisper API）
- 字幕生成（SRT、WebVTT格式）
- 會議摘要生成
- 文字翻譯
- 成本追蹤與使用記錄

## 系統架構圖

```mermaid
graph TB
    subgraph "前端層 (Vue 3 + TypeScript)"
        A[AssistantIndex.vue<br/>主要上傳介面]
        B[ResultView.vue<br/>結果展示]
        C[HistoryList.vue<br/>歷史記錄]
        D[Toast System<br/>訊息通知]
        E[API Client<br/>API呼叫層]
    end
    
    subgraph "後端API層 (.NET 8.0)"
        F[WhisperController<br/>主要控制器]
        G[OnPremisesWhisperController<br/>本地Whisper控制器]
    end
    
    subgraph "服務層 (Services)"
        H[TranscribeTaskQueue<br/>任務隊列]
        I[TranscribeBackgroundService<br/>背景服務]
        J[TranscribeTaskProcessor<br/>任務處理器]
        K[TaskRetryPolicy<br/>重試策略]
    end
    
    subgraph "資料存取層"
        L[FileLogDbContext<br/>資料庫上下文]
        M[FileLog Model<br/>檔案記錄]
        N[Transcribe Model<br/>轉錄結果]
        O[UsageLog Model<br/>使用記錄]
    end
    
    subgraph "外部服務"
        P[OpenAI Whisper API<br/>雲端語音轉文字]
        Q[本地Whisper服務<br/>地端語音轉文字]
        R[檔案儲存系統<br/>音視頻檔案存放]
    end
    
    subgraph "資料庫"
        S[(SQL Server<br/>主要資料庫)]
    end
    
    A --> E
    B --> E
    C --> E
    E --> F
    E --> G
    F --> H
    H --> I
    I --> J
    J --> K
    F --> L
    L --> M
    L --> N
    L --> O
    L --> S
    J --> P
    J --> Q
    F --> R
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style H fill:#fff3e0
    style L fill:#e8f5e8
    style P fill:#ffebee
```

## 類別關係圖 (Class Diagram)

```mermaid
classDiagram
    class FileLog {
        +Guid Uid
        +int Id
        +string EmpNo
        +string Name
        +int DeptNo
        +string Department
        +string IP
        +string Hostname
        +DateTime UploadTime
        +string FileName
        +long FileSize
        +double Duration
        +double WhisperApiCost
        +bool IsCompleted
        +ProcessingStatus Status
        +TimeSpan ProcessedTime
        +DateTime CreatedAt
        +DateTime UpdatedAt
        +int RetryCount
        +string ErrorMessage
        +DateTime LastProcessedAt
        +string Remark
    }
    
    class Transcribe {
        +string Transcript
        +string FileName
        +string Summary
        +string Srt
        +string Vtt
        +string Translation
        +Guid Guid
        +double Duration
        +double WhisperApiCost
        +FromFileLog(FileLog) Transcribe
    }
    
    class TranscribeTask {
        +Guid TaskId
        +string FileName
        +string FilePath
        +string EmpNo
        +string Name
        +int DeptNo
        +string Department
        +string IP
        +string Hostname
        +string Mode
        +int Priority
        +DateTime CreatedAt
        +DateTime StartedAt
        +ProcessingStatus Status
        +int RetryCount
        +string ErrorMessage
    }
    
    class WhisperController {
        -IConfiguration _configuration
        -FileLogDbContext _dbContext
        -ILogger _logger
        -ITranscribeTaskQueue _taskQueue
        +LoadTranscribe(Guid) IActionResult
        +LoadHistory() IActionResult
        +Download(string) IActionResult
        +Transcribe(List~IFormFile~, string) IActionResult
        +GetTaskStatus(Guid) IActionResult
        +CancelTask(Guid) IActionResult
        -GetDataDirectory(string, string) string
        -CreateZip(string, string[]) void
    }
    
    class TranscribeTaskQueue {
        -SortedDictionary _priorityQueues
        -ConcurrentDictionary _processingTasks
        -ConcurrentDictionary _completedTasks
        -ConcurrentDictionary _failedTasks
        +EnqueueTaskAsync(TranscribeTask) bool
        +DequeueTaskAsync() TranscribeTask
        +GetPendingTaskCount() int
        +GetProcessingTaskCount() int
        +GetStatistics() TaskQueueStatistics
        +TryCancelTaskAsync(Guid) bool
        +MarkTaskAsCompletedAsync(Guid) bool
    }
    
    class TranscribeBackgroundService {
        -ITranscribeTaskQueue _taskQueue
        -ITranscribeTaskProcessor _processor
        -ILogger _logger
        +ExecuteAsync(CancellationToken) Task
        -ProcessTasksAsync(CancellationToken) Task
    }
    
    class TranscribeTaskProcessor {
        -IWhisperProcessor _whisperProcessor
        -FileLogDbContext _dbContext
        -ILogger _logger
        +ProcessTaskAsync(TranscribeTask) Task
        -UpdateTaskStatus(Guid, ProcessingStatus) Task
        -SaveTranscribeResult(TranscribeTask, Transcribe) Task
    }
    
    class FileLogDbContext {
        +DbSet~FileLog~ FileLogs
        +DbSet~UsageLog~ UsageLogs
        #OnConfiguring(DbContextOptionsBuilder) void
    }
    
    class ProcessingStatus {
        <<enumeration>>
        Pending
        Processing
        Completed
        Failed
        Cancelled
    }
    
    WhisperController --> FileLogDbContext
    WhisperController --> TranscribeTaskQueue
    TranscribeBackgroundService --> TranscribeTaskQueue
    TranscribeBackgroundService --> TranscribeTaskProcessor
    TranscribeTaskProcessor --> FileLogDbContext
    TranscribeTaskQueue --> TranscribeTask
    FileLogDbContext --> FileLog
    FileLog --> ProcessingStatus
    TranscribeTask --> ProcessingStatus
    Transcribe --> FileLog : converts from
```

## 背景任務處理流程圖

```mermaid
sequenceDiagram
    participant Client as 前端客戶端
    participant Controller as WhisperController
    participant Queue as TranscribeTaskQueue
    participant Background as TranscribeBackgroundService
    participant Processor as TranscribeTaskProcessor
    participant DB as 資料庫
    participant API as Whisper API
    
    Client->>Controller: 上傳檔案 (POST /Transcribe)
    Controller->>DB: 建立 FileLog 記錄
    Controller->>Queue: 加入轉錄任務到隊列
    Controller-->>Client: 返回任務ID (立即回應)
    
    loop 背景處理循環
        Background->>Queue: 取出待處理任務
        Queue-->>Background: 返回任務 (如果有)
        Background->>Processor: 處理任務
        Processor->>DB: 更新任務狀態為 Processing
        Processor->>API: 呼叫 Whisper API
        API-->>Processor: 返回轉錄結果
        Processor->>DB: 儲存轉錄結果
        Processor->>DB: 更新任務狀態為 Completed
        Processor->>Queue: 標記任務完成
    end
    
    Client->>Controller: 查詢任務狀態 (GET /GetTaskStatus)
    Controller->>DB: 查詢任務狀態
    Controller-->>Client: 返回狀態資訊
    
    Client->>Controller: 載入轉錄結果 (GET /LoadTranscribe)
    Controller->>DB: 查詢轉錄結果
    Controller-->>Client: 返回完整結果
```

## 檔案處理流程圖

```mermaid
flowchart TD
    A[使用者上傳檔案] --> B{檔案格式檢查}
    B -->|支援格式| C[建立 FileLog 記錄]
    B -->|不支援格式| D[返回錯誤訊息]
    
    C --> E[建立 TranscribeTask]
    E --> F[加入任務隊列]
    F --> G[返回任務ID給前端]
    
    G --> H[背景服務取出任務]
    H --> I[更新狀態為 Processing]
    I --> J{選擇處理模式}
    
    J -->|雲端模式| K[呼叫 OpenAI Whisper API]
    J -->|地端模式| L[呼叫本地 Whisper 服務]
    
    K --> M[接收轉錄結果]
    L --> M
    
    M --> N[計算處理成本]
    N --> O[生成字幕檔案 SRT/WebVTT]
    O --> P[呼叫 GPT API 生成摘要]
    P --> Q[儲存所有結果到檔案系統]
    Q --> R[更新資料庫記錄]
    R --> S[標記任務完成]
    
    S --> T[前端輪詢狀態更新]
    T --> U[顯示處理結果]
    
    style A fill:#e1f5fe
    style G fill:#f3e5f5
    style M fill:#fff3e0
    style U fill:#e8f5e8
```

## 資料模型關係圖

```mermaid
erDiagram
    FileLog {
        Guid Uid PK
        int Id
        string EmpNo
        string Name
        int DeptNo
        string Department
        string IP
        string Hostname
        DateTime UploadTime
        string FileName
        long FileSize
        double Duration
        double WhisperApiCost
        bool IsCompleted
        ProcessingStatus Status
        TimeSpan ProcessedTime
        DateTime CreatedAt
        DateTime UpdatedAt
        int RetryCount
        string ErrorMessage
        DateTime LastProcessedAt
        string Remark
    }
    
    UsageLog {
        Guid Uid PK
        string EmpNo
        string Name
        int DeptNo
        string Department
        string IP
        string Hostname
        DateTime AccessTime
        string Action
        string FileGuid
        string Remark
    }
    
    TranscribeTask {
        Guid TaskId PK
        string FileName
        string FilePath
        string EmpNo
        string Name
        int DeptNo
        string Department
        string IP
        string Hostname
        string Mode
        int Priority
        DateTime CreatedAt
        DateTime StartedAt
        ProcessingStatus Status
        int RetryCount
        string ErrorMessage
    }
    
    FileLog ||--o{ UsageLog : "tracks usage"
    FileLog ||--|| TranscribeTask : "corresponds to"
```

## 系統部署架構圖

```mermaid
graph TB
    subgraph "用戶端"
        A[Web Browser<br/>Vue 3 SPA]
    end
    
    subgraph "Web Server (Kestrel)"
        B[.NET 8.0 WebAPI<br/>MeetingAssistant]
        C[靜態檔案服務<br/>Vue Build Output]
    end
    
    subgraph "背景服務"
        D[TranscribeBackgroundService<br/>任務處理服務]
        E[TaskQueue<br/>任務隊列管理]
    end
    
    subgraph "檔案儲存"
        F[本地檔案系統<br/>音視頻檔案]
        G[處理結果檔案<br/>逐字稿、字幕、摘要]
    end
    
    subgraph "資料庫"
        H[(SQL Server<br/>檔案記錄與使用統計)]
    end
    
    subgraph "外部API"
        I[OpenAI Whisper API<br/>語音轉文字]
        J[OpenAI GPT API<br/>摘要生成]
        K[本地Whisper服務<br/>地端處理選項]
    end
    
    A --> B
    A --> C
    B --> D
    B --> E
    B --> F
    B --> G
    B --> H
    D --> I
    D --> J
    D --> K
    D --> F
    D --> G
    D --> H
    
    style A fill:#e3f2fd
    style B fill:#f3e5f5
    style D fill:#fff3e0
    style H fill:#e8f5e8
    style I fill:#ffebee
```

## 技術棧總覽

### 前端技術

- **框架**: Vue 3 + TypeScript
- **建置工具**: Vite 6
- **狀態管理**: Composition API
- **UI組件**: 自定義組件 + Toast系統
- **HTTP客戶端**: Fetch API

### 後端技術

- **框架**: .NET 8.0 WebAPI
- **ORM**: Entity Framework Core
- **資料庫**: SQL Server
- **背景服務**: IHostedService
- **日誌**: ILogger
- **設定管理**: IConfiguration

### 外部整合

- **語音轉文字**: OpenAI Whisper API / 本地Whisper服務
- **文字生成**: OpenAI GPT-4 API
- **檔案處理**: 支援多種音視頻格式
- **字幕格式**: SRT、WebVTT

### 部署與運維

- **Web伺服器**: Kestrel
- **檔案上傳**: 支援最大4GB檔案
- **成本追蹤**: API使用成本計算與記錄
- **錯誤處理**: 統一錯誤處理與重試機制
- **監控**: 任務狀態追蹤與統計

## 主要設計模式

1. **Repository Pattern**: 透過 DbContext 進行資料存取
2. **Queue Pattern**: 使用優先級隊列管理背景任務
3. **Strategy Pattern**: 支援多種語音處理模式（雲端/地端）
4. **Observer Pattern**: 任務狀態變更通知
5. **Retry Pattern**: 失敗任務自動重試機制
6. **Factory Pattern**: 任務建立與處理器實例化
