# Coding

## Always speak zh-TW

## 專案為企業內部的會議助手，功能如下

- 上傳檔案，支援格式為：mp3, mp4, mpeg, mpga, m4a, ogg, wav, webm
- 生成影片逐字稿/字幕
- 生成音檔逐字稿
- 生成會議摘要
- 生成會議結論
- 文字翻譯

## My testing Framework is xUnit and vitest

- folder: Tests
- prefer FakeItEasy over Moq

## Web / User Interface / FrontEnd is written in Vue 3 + Vite 6 + TypeScript

- folder: MeetingAssistant/ClientApp
- build tool: npm

## WebAPI is written in .NET 8.0 C\#

- folder: MeetingAssistant/Controllers

## API 成本追蹤

- 實作 Whisper API 成本追蹤
  - 每分鐘語音處理成本: $0.006 美元
  - 在 FileLog 和 Transcribe 模型中追蹤成本
- 實作翻譯和摘要 API 成本追蹤
  - GPT-4 輸入成本: $0.15/1K tokens
  - GPT-4 輸出成本: $0.60/1K tokens

## 音訊處理

- 支援音檔長度計算
  - 在 Transcribe 模型中追蹤音檔總長度(秒)
  - 在 ProcessVideoFileAsync 方法中計算
- 支援多種字幕格式
  - SRT 格式
  - WEBVTT 格式

## 專案結構

- 主要元件
  - WhisperProcessor: 處理語音轉文字
  - MeetingHelper: 會議輔助工具類
  - FileLog/Transcribe 模型: 資料模型
- 專案結構說明 在 Structure.md
  
## 開發環境

- .NET 8.0
- Node.js 18+
- 必要環境變數
  - OPENAI_API_KEY: OpenAI API 金鑰
  - FileDir: 檔案儲存目錄

## 部署

- 使用 Kestrel 作為網頁伺服器
- 支援大型檔案上傳 (最大 4GB)
- 設定 requestLimits maxAllowedContentLength

## 變更儲存至 CHANGELOG.md , 盡可能的詳細，要包含所有的變更，並且要有版本號

- 版本號格式為：x.x.x (x = 數字)，例如：1.0.0
- 版本號要遞增，
- 要有日期時間 (格式 yyyy/MM/dd hh:mm)
- 每次變更都要有一個標題，標題格式為：`[版本號] [日期] [變更內容]`，例如：`[1.0.0] [2023-10-01] [新增功能]`
- 每次變更都要有一個描述，描述格式為：`[變更內容]`，例如：`[新增功能]`
  