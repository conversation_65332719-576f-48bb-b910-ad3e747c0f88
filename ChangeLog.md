# 變更記錄 (CHANGELOG)

## [2.0.5] [2025/07/02 16:06] [清理未使用的詳細進度追蹤系統]

### 🧹 程式碼清理

- **移除未使用的詳細進度追蹤系統**：
  - 刪除 `MeetingUtility/Models/TaskProgress.cs` - 包含 TaskProgress 類別、ProcessingStage 枚舉、AudioSegmentInfo 類別、ProgressCalculator 靜態類別
  - 刪除 `MeetingUtility/Interfaces/ITaskProgressTracker.cs` - 詳細進度追蹤服務介面
  - 刪除 `MeetingAssistant/Services/TaskProgressTracker.cs` - 詳細進度追蹤服務實作
  - 刪除 `MeetingAssistant/ClientApp/src/components/TaskProgressView.vue` - 詳細進度顯示組件

### 🔧 後端程式碼清理

- **WhisperController.cs**：
  - 移除建構函式中的 `ITaskProgressTracker _progressTracker` 參數和欄位
  - 移除 `MapStatusToStage` 方法（將 ProcessingStatus 映射到 ProcessingStage）
  - 保留 `GetTaskProgress` 方法用於簡單百分比進度計算

- **TranscribeTaskProcessor.cs**：
  - 移除建構函式中的 `ITaskProgressTracker _progressTracker` 參數和欄位
  - 簡化 `ProcessWithOnPremisesApiWithProgressAsync` 方法，移除所有詳細進度追蹤呼叫
  - 移除 `using MeetingUtility.Models` 引用（因模型已刪除）
  - 保留基本的任務處理邏輯

- **Program.cs**：
  - 移除 `builder.Services.AddSingleton<ITaskProgressTracker, TaskProgressTracker>();` 服務註冊

### 🎨 前端程式碼簡化

- **EnhancedProgressView.vue**：
  - 簡化組件邏輯，移除複雜的詳細進度載入功能
  - 保留基本的百分比進度顯示和自動重新整理
  - 新增狀態變化監聽，自動更新進度顯示
  - 根據 ProcessingStatus 計算簡單進度：Pending=0%, Processing=50%, Completed=100%, Failed/Cancelled=0%

### 🧪 測試程式碼更新

- **TranscribeTaskProcessorTests.cs**：
  - 移除所有 `ITaskProgressTracker` 相關的模擬設定和測試程式碼
  - 更新 `TranscribeTaskProcessor` 建構函式呼叫，移除 progressTracker 參數
  - 保持所有現有測試功能的完整性

### 📊 系統簡化效果

- **移除程式碼量**：
  - 約 800+ 行未使用的 C# 程式碼
  - 約 200+ 行複雜的前端組件程式碼
  - 4 個完整的檔案（3 個後端 + 1 個前端組件）

- **架構簡化**：
  - 移除複雜的 8 階段進度追蹤系統
  - 移除分段進度和音檔時長進度計算
  - 移除預估剩餘時間功能
  - 移除記憶體中的進度快取系統

### 🎯 保留的功能

- **簡單進度系統**：
  - 基於 `ProcessingStatus` 的簡單百分比計算
  - `TranscribeTask.ProcessPercentage` 屬性用於任務隊列進度
  - 前端基本進度條顯示和自動重新整理

- **核心功能完整性**：
  - 所有轉錄、翻譯、摘要功能保持不變
  - 背景任務處理系統正常運作
  - 用戶介面和使用體驗無影響

### 💡 技術改進

- **降低複雜度**：移除過度設計的詳細進度追蹤，採用簡單有效的進度顯示
- **提升維護性**：減少程式碼量，降低維護成本
- **改善可讀性**：簡化系統架構，提高程式碼可讀性
- **減少依賴**：移除不必要的服務依賴和介面

### 📋 影響評估

- **向後相容性**：完全相容，不影響現有 API 和功能
- **用戶體驗**：保持基本進度顯示，用戶感知無明顯差異
- **系統效能**：移除記憶體快取和複雜計算，略微提升效能
- **開發效率**：簡化的程式碼結構有助於後續開發和維護

## [2.0.4] [2025/07/02 11:12] [改善刪除按鈕 ToolTip 效果]

### 🎨 用戶體驗優化

- **改善刪除按鈕 ToolTip 效果**：
  - 替換原本的 `title` 屬性為自定義 ToolTip 組件
  - 解決 `title` ToolTip 會被滑鼠游標擋住的問題
  - 新增自定義 ToolTip 樣式，包含箭頭指示器
  - 使用 `@mouseenter` 和 `@mouseleave` 事件控制 ToolTip 顯示/隱藏
  - ToolTip 位置優化，避免被游標遮擋
  - 添加淡入淡出動畫效果，提升視覺體驗

### 🔧 技術實作

- **HistoryList.vue 組件更新**：
  - 新增 `tooltipVisible` 響應式狀態管理
  - 實作 `showTooltip` 和 `hideTooltip` 函數
  - 使用 `Record<string, boolean>` 類型管理多個 ToolTip 狀態
  - 自定義 ToolTip 樣式：深色背景、白色文字、圓角陰影
  - 箭頭指示器使用 CSS border 技術實現
  - 響應式定位：調整為 `-right-2 top-8` 避免被卡片效果遮擋
  - 箭頭位置相應調整為向上指向按鈕

### 🎯 樣式特點

- **視覺設計**：
  - 深色背景 (`bg-gray-800`) 提升對比度
  - 白色文字 (`text-white`) 確保可讀性
  - 圓角設計 (`rounded`) 現代化外觀
  - 陰影效果 (`shadow-lg`) 增加層次感
  - 高 z-index (`z-50`) 確保顯示在最上層

- **動畫效果**：
  - 淡入淡出過渡效果 (`transition-opacity duration-200`)
  - 流暢的顯示/隱藏動畫
  - 即時響應滑鼠互動

### 📱 使用體驗

- **改善前**：使用瀏覽器原生 `title` 屬性，容易被滑鼠游標遮擋
- **改善後**：自定義 ToolTip 位置智能避開游標，提供更清晰的提示資訊
- **互動性**：滑鼠懸停即時顯示，離開立即隱藏，反應靈敏

## [2.0.3] [2025/07/02 10:01] [修正 Tailwind CSS v4 使用方式]

### 🔧 修復

- **修正 Tailwind CSS v4 在 Vue 組件中的使用方式**：
  - 在所有 Vue 組件的 `<style scoped>` 區塊中添加 `@reference "../assets/main.css"` 指令
  - 修正 `EnhancedProgressView.vue`、`TaskProgressView.vue` 組件
  - 解決 Tailwind CSS v4 中 `@apply` 指令在組件樣式中無法正常工作的問題
  - 更新 opacity 語法：`bg-opacity-75` → `bg-white/75` (符合 v4 新語法)
  - 確保所有自定義樣式都能正確引用 Tailwind CSS 的工具類別

### 📚 技術說明

- **Tailwind CSS v4 變更**：
  - 在 Vue 組件的 `<style scoped>` 區塊中使用 `@apply` 時，需要使用 `@reference` 指令引用主要樣式表
  - 這是 Tailwind CSS v4 的新要求，用於確保工具類別在組件樣式中可用
  - 修正後的語法：

    ```css
    <style scoped>
    @reference "../assets/main.css";
    
    .my-class {
      @apply bg-blue-500 text-white;
    }
    </style>
    ```

  - **Opacity 語法更新**：
    - v3: `bg-white bg-opacity-75`
    - v4: `bg-white/75`

### 🎯 影響範圍

- **修正的組件**：
  - `EnhancedProgressView.vue`：修正 `.enhanced-progress-view` 和 `.loading-overlay` 樣式，更新 `bg-opacity-75` → `bg-white/75`
  - `TaskProgressView.vue`：修正 `.task-progress-container` 和 `.stage-details` 樣式
  - `HistoryList.vue`：修正 hover 效果，`hover:bg-opacity-80` → `hover:bg-gray-100`
  - `AssistantIndex.vue`：修正對話框背景，`bg-black bg-opacity-40` → `bg-black/40`

- **技術環境**：
  - Tailwind CSS 版本：4.1.10
  - Vue 3 + TypeScript + Vite 6
  - 所有樣式現在都能正確編譯和應用

## [2.0.2] [2025/07/02 09:15] [修復] TranscribeTaskProcessorTests.cs 建構函式呼叫問題

### 🔧 修復

- **修復 `TranscribeTaskProcessorTests.cs` 測試編譯錯誤**：
  - 更新 `TranscribeTaskProcessorTests.cs` 中 `TranscribeTaskProcessor` 的建構函式呼叫，以包含新增加的 `ITaskProgressTracker` 參數。
  - 新增 `FakeItEasy` 對 `ITaskProgressTracker` 介面的模擬設定，確保測試環境的完整性。
  - 修正了測試類別的成員變數宣告，並添加了 `MeetingUtility.Interfaces` 和 `MeetingUtility.Models` 的 `using` 引用，解決了相關的編譯錯誤。
  - 確保了在 2.0.1 版本更新後，測試專案能夠成功建置。

## [2.0.1] [2025/07/02 08:52] [進度顯示增強功能完整實現]

### 🎯 主要功能

- **詳細進度追蹤系統**：實現了完整的任務進度追蹤機制
  - 新增 `TaskProgress` 模型，包含總進度、階段進度、分段進度等詳細資訊
  - 定義 8 個處理階段：初始化(2%) → 音檔處理(2%) → 轉錄(50%) → 翻譯(24%) → 摘要(24%) → 總摘要(2%) → 完成(1%)
  - 實現 `ProcessingStage` 枚舉和 `ProgressCalculator` 輔助類

- **智能進度顯示**：根據任務狀態自動選擇最適合的顯示方式
  - 處理中任務：顯示詳細的進度資訊，包含階段進度、分段資訊、預估時間
  - 已完成/失敗任務：顯示簡化的卡片式介面，包含狀態標籤和基本資訊

### 🔧 後端增強

- **新增 API 端點**：
  - `GetTaskProgress` API：查詢任務詳細進度資訊
  - 增強 `GetTaskStatus` API：提供基本狀態查詢
  - 進度映射功能：ProcessingStatus 到 ProcessingStage 的智能映射

- **進度追蹤服務**：
  - `ITaskProgressTracker` 介面：定義進度追蹤服務合約
  - `TaskProgressTracker` 實現：使用 ConcurrentDictionary 提供高效能記憶體快取
  - 支援線程安全的並發任務進度更新
  - 自動清理過期進度記錄機制

- **服務整合**：
  - 在 `TranscribeTaskProcessor` 中整合進度追蹤
  - 在 `WhisperController` 中新增進度查詢功能
  - 在 `Program.cs` 中註冊進度追蹤服務

### 🎨 前端組件系統

- **TaskProgressView 組件**：完整的進度顯示組件
  - 總進度條和階段進度顯示
  - 分段處理進度（當前分段、總分段數、音檔時長進度）
  - 時間資訊（開始時間、已處理時間、預估剩餘時間）
  - 視覺化的 8 個處理階段狀態顯示
  - 自動重新整理和錯誤處理

- **EnhancedProgressView 組件**：智能進度顯示包裝器
  - 自動載入詳細進度資訊
  - 智能切換詳細進度和基本進度條
  - 自動監控處理中任務的進度更新
  - 支援點擊查看功能

- **HistoryList 組件整合**：
  - 處理中任務自動顯示 `EnhancedProgressView`
  - 已完成/失敗任務顯示簡化卡片介面
  - 新增狀態圖示和標籤系統
  - 優化的響應式設計和用戶體驗

### 📊 進度計算邏輯

- **精確的階段權重分配**：
  - 轉錄階段：50% 總進度，按分段音檔計算百分比
  - 翻譯階段：24% 總進度，按分段計算進度
  - 摘要階段：24% 總進度，按分段計算進度
  - 總摘要：2% 總進度
  - 其他階段：初始化(2%) + 音檔處理(2%) + 完成(1%) = 5%

- **智能進度預估**：
  - 基於當前進度和已處理時間計算預估剩餘時間
  - 分段進度追蹤和音檔時長進度顯示
  - 實時狀態訊息和錯誤處理

### 🎯 用戶體驗優化

- **智能顯示模式**：
  - 根據任務狀態（處理中 vs 已完成）自動選擇顯示方式
  - 處理中任務顯示詳細進度，已完成任務顯示簡化資訊
  - 清晰的視覺狀態指示和錯誤訊息顯示

- **實時更新機制**：
  - 處理中任務每 5 秒自動重新整理進度
  - 智能監控啟動和停止機制
  - 任務完成/失敗的即時通知

- **響應式設計**：
  - 適配桌面和行動裝置
  - 流暢的進度條動畫效果
  - 直觀的階段狀態視覺化

### 🔧 技術特點

- **高效能設計**：
  - 使用 `ConcurrentDictionary` 提供線程安全的記憶體快取
  - 自動清理過期進度記錄，避免記憶體洩漏
  - 智能監控控制，避免不必要的資源消耗

- **向後相容**：
  - 保持與現有 API 的完全相容性
  - 不影響現有功能的正常運作
  - 按需求未修改 Python Whisper 端程式碼

- **可擴展性**：
  - 模組化的組件設計，易於擴展和維護
  - 清晰的介面定義，支援未來功能擴展
  - 完整的錯誤處理和日誌記錄

### 📱 API 使用範例

```csharp
// 查詢詳細進度
GET /api/Whisper/GetTaskProgress?taskId={guid}

// 回傳格式
{
  "taskId": "guid",
  "overallProgress": 75,
  "currentStage": "Transcribing",
  "stageProgress": 60,
  "stageDescription": "語音轉文字處理",
  "statusMessage": "正在轉錄第 3/5 段",
  "segmentInfo": {
    "totalSegments": 5,
    "processedSegments": 3,
    "currentSegmentIndex": 2,
    "currentSegmentFile": "segment_003.ogg"
  },
  "estimatedRemainingMinutes": 8
}
```

### 🎨 前端組件使用

```vue
<!-- 智能進度顯示 -->
<EnhancedProgressView
  :task-id="taskId"
  :file-name="fileName"
  :status="status"
  :is-completed="isCompleted"
  @progress-click="handleClick"
/>

<!-- 詳細進度組件 -->
<TaskProgressView
  :task-id="taskId"
  :overall-progress="75"
  :current-stage="'Transcribing'"
  :segment-info="segmentInfo"
  @refresh="refreshProgress"
/>
```

### 📋 測試和驗證

- **功能測試**：
  - 處理中任務的詳細進度顯示
  - 已完成任務的簡化顯示
  - 自動監控和狀態更新
  - 點擊查看和刪除功能

- **效能測試**：
  - 大量歷史記錄的載入效能
  - 多任務同時監控的穩定性
  - 長時間運行的記憶體使用情況

- **用戶體驗測試**：
  - 直觀的進度顯示和狀態指示
  - 流暢的動畫效果和響應式設計
  - 清晰的錯誤提示和狀態描述

### 🎯 專案影響

- **用戶體驗大幅提升**：用戶現在可以清楚了解任務的詳細處理進度
- **系統透明度增加**：提供完整的處理階段和狀態資訊
- **監控能力增強**：管理員可以更好地監控系統運行狀況
- **維護效率提升**：詳細的進度和錯誤資訊有助於問題診斷

## [2.0.0] [2025/07/01 16:03] [Python Whisper API 隊列系統]

### 📚 文檔新增

- **完整的 PythonWhisper/README.md 專案文檔**：
  - 詳細的專案概述和主要功能介紹
  - 技術架構和核心組件說明
  - 架構設計優點分析 (順序執行、資源保護、狀態追蹤等)
- **Mermaid 圖表**：
  - 系統架構圖：展示完整的服務架構和組件關係
  - 任務處理流程圖：詳細的序列圖展示異步任務處理流程
  - 類別關係圖：TranscribeTask、TranscribeQueue、TaskStatus 等類別關係
- **完整的 API 文檔**：
  - 語音轉文字 API (`/transcribe`, `/transcribe_full`)
  - 任務管理 API (`/task/{id}`, `/queue/status`)
  - 其他服務 API (`/translate`, `/summary`, `/health`)
  - 詳細的請求/回應格式和狀態碼說明

### 🚀 部署和維運指南

- **多種部署方式**：
  - 直接執行、Waitress CLI、Gunicorn、Docker 部署
  - 生產環境建議和最佳實踐
- **效能調優指南**：
  - Waitress 設定參數說明 (threads, channel_timeout, max_request_body_size)
  - 記憶體管理和資源監控建議
- **監控和日誌**：
  - 日誌設定和格式說明
  - 監控指標和健康檢查端點
- **故障排除**：
  - 常見問題和解決方案 (GPU 記憶體、檔案上傳、任務處理等)
  - 日誌分析命令和監控方法

### 🔧 開發和測試

- **環境設定**：
  - 系統需求和依賴安裝說明
  - 環境變數設定 (WHISPER_MODEL_PATH, OLLAMA_BASE_URL)
- **測試腳本**：
  - 測試上傳和健康檢查的命令範例
  - 開發模式設定說明

### 💡 技術分析

- **異步任務處理系統**：
  - 設計優點：FIFO 隊列、資源保護、狀態追蹤、錯誤處理
  - 潛在改進點：任務清理機制、優雅關閉、任務取消功能
  - 整體評估和生產環境建議

## [Version 1.x.x 變更記錄](ChangeLog_1xx.md)
